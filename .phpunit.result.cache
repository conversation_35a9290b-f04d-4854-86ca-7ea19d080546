{"version": 1, "defects": {"Tests\\Feature\\TeacherBpsDataTest::test_get_teacher_bps_data_returns_class_based_structure": 4, "Tests\\Feature\\TeacherBpsDataTest::test_get_teacher_bps_data_includes_discipline_items": 4, "Tests\\Feature\\TeacherBpsDataTest::test_get_teacher_bps_data_with_existing_bps_records": 4, "Tests\\Feature\\TeacherBpsDataTest::test_get_teacher_bps_data_invalid_auth_code": 4, "Tests\\Feature\\TeacherBpsDataTest::test_get_teacher_bps_data_non_staff_user": 4, "Tests\\Feature\\TeacherBpsDataTest::test_get_teacher_bps_data_calculates_totals_correctly": 4, "Tests\\Feature\\TeacherBpsDataTest::test_get_teacher_bps_data_groups_students_by_year_level": 4, "Tests\\Feature\\MobileHealthApiTest::test_get_student_health_records_success": 4, "Tests\\Feature\\MobileHealthApiTest::test_get_student_health_info_success": 4, "Tests\\Feature\\MobileHealthApiTest::test_get_teacher_health_data_success": 4, "Tests\\Feature\\MobileHealthApiTest::test_get_health_lookup_data_success": 4, "Tests\\Feature\\MobileHealthApiTest::test_create_student_health_record_staff_only": 4, "Tests\\Feature\\MobileHealthApiTest::test_invalid_auth_code_returns_401": 4, "Tests\\Feature\\MobileHealthApiTest::test_student_cannot_access_teacher_endpoints": 4, "Tests\\Feature\\MobileMessagingApiTest::test_student_can_get_available_users": 4, "Tests\\Feature\\MobileMessagingApiTest::test_student_can_get_teachers": 4, "Tests\\Feature\\MobileMessagingApiTest::test_teacher_can_get_available_users": 4, "Tests\\Feature\\MobileMessagingApiTest::test_teacher_can_get_students": 4, "Tests\\Feature\\MobileMessagingApiTest::test_invalid_auth_code_returns_401": 4, "Tests\\Feature\\MobileMessagingApiTest::test_student_cannot_access_staff_endpoint": 4, "Tests\\Feature\\MobileMessagingApiTest::test_staff_cannot_access_student_endpoint": 4}, "times": {"Tests\\Feature\\TeacherBpsDataTest::test_get_teacher_bps_data_returns_class_based_structure": 0.138, "Tests\\Feature\\TeacherBpsDataTest::test_get_teacher_bps_data_includes_discipline_items": 0.012, "Tests\\Feature\\TeacherBpsDataTest::test_get_teacher_bps_data_with_existing_bps_records": 0.012, "Tests\\Feature\\TeacherBpsDataTest::test_get_teacher_bps_data_invalid_auth_code": 0.014, "Tests\\Feature\\TeacherBpsDataTest::test_get_teacher_bps_data_non_staff_user": 0.013, "Tests\\Feature\\TeacherBpsDataTest::test_get_teacher_bps_data_calculates_totals_correctly": 0.013, "Tests\\Feature\\TeacherBpsDataTest::test_get_teacher_bps_data_groups_students_by_year_level": 0.016, "Tests\\Feature\\MobileHealthApiTest::test_get_student_health_records_success": 0.121, "Tests\\Feature\\MobileHealthApiTest::test_get_student_health_info_success": 0.02, "Tests\\Feature\\MobileHealthApiTest::test_get_teacher_health_data_success": 0.02, "Tests\\Feature\\MobileHealthApiTest::test_get_health_lookup_data_success": 0.024, "Tests\\Feature\\MobileHealthApiTest::test_create_student_health_record_staff_only": 0.023, "Tests\\Feature\\MobileHealthApiTest::test_invalid_auth_code_returns_401": 0.02, "Tests\\Feature\\MobileHealthApiTest::test_student_cannot_access_teacher_endpoints": 0.023, "Tests\\Feature\\MobileMessagingApiTest::test_student_can_get_available_users": 0.115, "Tests\\Feature\\MobileMessagingApiTest::test_student_can_get_teachers": 0.02, "Tests\\Feature\\MobileMessagingApiTest::test_teacher_can_get_available_users": 0.124, "Tests\\Feature\\MobileMessagingApiTest::test_teacher_can_get_students": 0.023, "Tests\\Feature\\MobileMessagingApiTest::test_invalid_auth_code_returns_401": 0.019, "Tests\\Feature\\MobileMessagingApiTest::test_student_cannot_access_staff_endpoint": 0.018, "Tests\\Feature\\MobileMessagingApiTest::test_staff_cannot_access_student_endpoint": 0.022}}