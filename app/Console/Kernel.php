<?php

namespace App\Console;

use App\Library\Jobs\AttendanceJob;
use App\Library\Jobs\LibraryOverdueNotificationJob;
use App\Jobs\AttendanceReminderJob;
use App\Library\Jobs\MobileNotificationJob;
use App\Library\Helper\AcademicHelper;
use App\Library\Repository\MobileNotificationRepository;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    protected $commands = [
        'App\Console\Commands\CalculateHouseScore',
    ];

    /**
     * Define the application's command schedule.
     *
     * @param  \Illuminate\Console\Scheduling\Schedule  $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        $ah = new AcademicHelper();
        $mobileNotificationRepository = new MobileNotificationRepository($ah);

        // Existing attendance notification job
        $schedule->job(new AttendanceJob($ah, $mobileNotificationRepository));

        // Enhanced attendance reminder jobs
        // Morning reminder (7:30 AM) - Prepare for school
        $schedule->job(new AttendanceReminderJob())->dailyAt('07:30')->weekdays();

        // Morning attendance reminder (8:15 AM) - Take morning attendance
        $schedule->job(new AttendanceReminderJob())->dailyAt('08:15')->weekdays();

        $schedule->job(new LibraryOverdueNotificationJob($ah, $mobileNotificationRepository))->dailyAt('08:15')->weekdays();
        // Targeted reminders for missing attendance (every 2 hours during school time)
        $schedule->call(function () {
            $job = new AttendanceReminderJob();
            $job->sendTargetedReminders();
        })->hourlyAt([10, 12, 14, 16])->weekdays();

        // Send mobile notifications every minute during active hours (8 AM to 8 PM)
        $schedule->command('send_mobile_notification')
                 ->everyMinute()
                 ->between('08:00', '20:00')
                 ->weekdays();
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
