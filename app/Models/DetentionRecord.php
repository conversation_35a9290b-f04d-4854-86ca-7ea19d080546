<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DetentionRecord extends Model
{
    use HasFactory;
    protected $table = 'detention_records';
    protected $primaryKey = 'detention_record_id';
    protected $fillable = ['detention_record_id','dps_record_id','student_id', 'latest_point', 'detention_type', 'is_served', 'system_note', 'served_detention_type','academic_year_id','branch_id','academic_semester','date','detention_type_id','served_user_id'];
    public function student(){
        return $this->belongsTo(User::class,'student_id');
    }
    public function type(){
        return $this->belongsTo(DetentionTypeSetting::class,'detention_type_id');
    }
}
