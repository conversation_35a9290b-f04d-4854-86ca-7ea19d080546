<?php

namespace App\Models;

use App\Library\Helper\AcademicHelper;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class LearningArea extends Model
{
    use HasFactory;
    protected $academicHelper;

    public function __construct()
    {
        $this->academicHelper = new AcademicHelper();
    }

    protected $table = 'academic_learning_areas';

    protected $fillable = ['academic_year_id', 'branch_id', 'subject_id', 'grade_id', 'uid', 'name', 'status', 'uuid', 'created_by', 'updated_by'];

    public function subject()
    {
        return $this->belongsTo(Subject::class, 'subject_id');
    }

    public function academicYear()
    {
        return $this->belongsTo(AcademicYear::class, 'academic_year_id');
    }   

    public function branch()
    {
        return $this->belongsTo(Branch::class, 'branch_id');
    }   

    public function getGradeNameAttribute()
    {
        $gradeId = json_decode($this->grades, true);

        return \DB::table('academic_elective_grade')
                        ->whereIn('grade_id', $gradeId)
                        ->pluck('grade_name')->toArray();
    }

    public function getFormattedGradesAttribute()
    {
        $grades = json_decode($this->grades, true); // Decode JSON grades
        if (empty($grades)) {
            return null;
        }

        // Use the global $gradesList variable or inject a service to fetch grades
        $gradesList = $this->academicHelper->branchElectiveGrades();

        return $gradesList->filter(function ($grade) use ($grades) {
            return in_array($grade->grade_id, $grades);
        });
    }

    public function getSubjectNameAttribute()
    {
        $gradeId = json_decode($this->grades, true);

        $subject = \DB::table('academic_elective_grade')
                        ->whereIn('grade_id', $gradeId)
                        ->pluck('subject_id')->toArray();

        return \DB::table('subjects')
                        ->whereIn('subject_id', $subject)
                        ->pluck('subject_name')->toArray();
    
    }



}
