<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class StudentMeasurements extends Model
{
    use HasFactory;
    protected $table = 'student_measurements'; 
    protected $primaryKey = 'id';
    protected $fillable = ['student_id', 'branch_id', 'academic_year_id', 'date', 'height', 'weight'];

    public function student() {
        return $this->belongsTo(User::class, 'student_id');
    }

    public function branch() {
        return $this->belongsTo(Branch::class, 'branch_id');
    }

    public function year() {
        return $this->belongsTo(AcademicYear::class, 'academic_year_id');
    }
}
