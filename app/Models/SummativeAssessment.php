<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SummativeAssessment extends Model
{
    use HasFactory;
    protected $table = 'academic_summative_assessments';
    protected $primaryKey = 'assessment_id';
    protected $fillable = ['branch_id', 'academic_year_id', 'assessment_name', 'teacher_id', 'template_id', 'subject_id', 'grade_id', 'max_score', 'percentage', 'strand', 'date', 'uid', 'comment', 'is_final_exam'];

    public function branch()
    {
        return $this->belongsTo(Branch::class, 'branch_id');
    }

    public function year()
    {
        return $this->belongsTo(AcademicYear::class, 'academic_year_id');
    }

    public function teacher()
    {
        return $this->belongsTo(User::class, 'teacher_id');
    }

    public function subject()
    {
        return $this->belongsTo(Subject::class, 'subject_id');
    }

    public function grade()
    {
        return $this->belongsTo(ElectiveGrade::class, 'grade_id');
    }

    public function data()
    {
        return $this->hasMany(SummativeAssessmentData::class, 'assessment_id');
    }

    public function template()
    {
        return $this->belongsTo(ScoreTemplate::class, 'template_id');
    }
    public function skill_strand()
    {
        return $this->belongsTo(SkillsStrands::class, 'strand', 'skill_strand_id');
    }
}
