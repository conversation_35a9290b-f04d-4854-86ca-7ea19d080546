<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'signature'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
    ];

    public function branch() {
        return $this->belongsTo(Branch::class, 'branch_id', 'branch_id');
    }

    public function details() {
        return $this->belongsTo(UserDetail::class, 'id', 'user_id');
    }

    public function student_details() {
        return $this->belongsTo(StudentDetail::class, 'id', 'student_id');
    }

    public function profession() {
        return $this->belongsTo(UserProfession::class, 'id', 'user_id');
    }

    public function contact() {
        return $this->belongsTo(UserContact::class, 'id', 'user_id');
    }

    public function student_detail() {
        return $this->belongsTo(StudentDetail::class, 'id', 'student_id');
    }

    public function student_classroom() {
        return $this->belongsTo(StudentClassroom::class, 'id', 'student_id');
    }
    public function userBranches() {
        return $this->hasMany(UserBranch::class, 'user_id', 'id');
    }


}
