<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserDetail extends Model
{
    use HasFactory;
    protected $table = 'users_detail';
    protected $primaryKey = 'user_id';

    protected $guarded = ['created_at', 'updated_at'];

    public function user() {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    public function staffCategory() {
        return $this->belongsTo(StaffCategory::class, 'staff_category', 'id');
    }
}
