<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DisciplineItem extends Model
{
    use HasFactory;
    protected $table = 'discipline_bps_items';
    protected $primaryKey = 'discipline_item_id';
    protected $fillable = ['item_title', 'item_type', 'item_point', 'item_status'];
        public function records() {
        return $this->hasMany(DisciplineRecord::class, 'item_id');
    }
}
