<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class HealthGuest extends Model
{
    use HasFactory;
    protected $table = 'health_guests';
    protected $primaryKey = 'health_record_id';
    protected $fillable = ['branch_id', 'academic_year_id', 'full_name', 'date', 'time', 'discharge_time', 'reason', 'action',
        'temperature', 'blood_pressure', 'saturation', 'pulse', 'medication', 'comments', 'time_left_nurse_clinic', 'creator_id', 'status'];
}
