<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DetentionDutyList extends Model
{
    use HasFactory;
    protected $table = 'detention_duty_lists';
    protected $primaryKey = 'detention_duty_list_id';
    protected $fillable = ['detention_duty_list_id','duty_teacher_id','detention_type_id', 'academic_year_id', 'branch_id', 'assigned_user_id','status'];

    public function branch() {
        return $this->belongsTo(Branch::class, 'branch_id');
    }

    public function year() {
        return $this->belongsTo(AcademicYear::class, 'academic_year_id');
    }
    public function duty_teacher(){
        return $this->belongsTo(User::class,'duty_teacher_id');
    }
    public function assigned_user(){
        return $this->belongsTo(User::class,'assigned_user_id');
    }
    public function detention_type(){
        return $this->belongsTo(DetentionTypeSetting::class,'detention_type_id','detention_type_setting_id');
    }
    public function detention_records(){
        return $this->hasMany(DetentionRecord::class,'detention_type_id','detention_type_id');
    }

}
