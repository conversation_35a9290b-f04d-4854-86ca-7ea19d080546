<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TeacherClassLesson extends Model
{
    use HasFactory;
    protected $table = 'teacher_class_lesson';
    protected $primaryKey = 'teacher_class_lesson_id';
    protected $fillable = ['branch_id', 'academic_year_id', 'teacher_id', 'class_id', 'subject_id', 'weekly_count', 'creator_id', 'updater_id','start_date','end_date'];
    public function branch() {
        return $this->belongsTo(BRanch::class, 'branch_id');
    }

    public function year() {
        return $this->belongsTo(AcademicYear::class, 'academic_year_id');
    }

    public function user() {
        return $this->belongsTo(User::class, 'teacher_id', 'id');
    }

    public function classroom() {
        return $this->belongsTo(Classroom::class, 'class_id', 'classroom_id');
    }

    public function subject() {
        return $this->belongsTo(Subject::class, 'subject_id');
    }

}
