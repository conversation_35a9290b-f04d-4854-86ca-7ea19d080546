<?php

namespace App\Library\Helper;

use App\Models\AcademicTerm;
use App\Models\Dictionary;
use App\Models\AcademicYear;
use App\Models\AcademicWeek;
use App\Models\User;
use App\Models\Classroom;
use App\Models\ElectiveGrade;
use App\Models\StudentInformation;
use App\Models\Timetable;
use App\Models\TeacherClassLesson;
use App\Models\AcademicSemester;

class AcademicHelper
{

    public function academicYear()
    {
        $rd = null;
        $branch = self::currentBranch();
        try {
            $year = AcademicSemester::where('is_default', 1)
                ->where('branch_id', $branch)
                ->first();
            $rd = $year->academic_year_id;
        } catch (Throwable $th) {
            $rd = 'Academic Year settings is invalid!';
        }
        return $rd;
    }

    public function branchAcademicYear($branch)
    {
        $rd = null;
        try {
            $year = AcademicSemester::where('is_default', 1)
                ->where('branch_id', $branch)
                ->first();
            $rd = $year->academic_year_id;
        } catch (Throwable $th) {
            $rd = 'Academic Year settings is invalid!';
        }
        return $rd;
    }

    public function getAcademicYearInfo()
    {
        try {
            $year = AcademicYear::where('is_active', 1)->first();
        } catch (Throwable $th) {
            return 'Academic Year settings is invalid!';
        }
        return $year;
    }

    public function currentSemester()
    {
        $branch = self::currentBranch();
        $today = date('Y-m-d');
        $semesterInfo = AcademicSemester::where('is_default', 1)
            ->where('branch_id', $branch)
            ->first();
        return $semesterInfo->academic_semester;
    }

    public function currentWeek()
    {
        $branch = self::currentBranch();
        $today = date('Y-m-d');
        $weekInfo = AcademicWeek::where('start_date', '<=', $today)
            ->where('end_date', '>=', $today)
            ->where('branch_id', $branch)
            ->first();

        return $weekInfo->week;
    }

    public function branchCurrentWeek($branch)
    {
        $today = date('Y-m-d');
        $weekInfo = AcademicWeek::where('start_date', '<=', $today)
            ->where('end_date', '>=', $today)
            ->where('branch_id', $branch)
            ->first();

        return $weekInfo->week;
    }

    public function weekDayDate($week, $weekDay)
    {
        $branch = self::currentBranch();
        self::academicYear();
        $week = AcademicWeek::where('academic_year_id', self::academicYear())
            ->where('week', $week)
            ->where('branch_id', $branch)
            ->first();

        $newDate = null;
        if (!$week) {
            throw new Exception('Week settigs is invalid!');
        }

        $startDate = $week->start_date;
        $addCount = $weekDay - 1;
        $newDate = date('Y-m-d', strtotime($startDate . ' + ' . $addCount . ' days'));
        return $newDate;
    }

    public function currentBranch()
    {
        // Check if user is authenticated
        if (!\Auth::check() || !\Auth::user()) {
            return 0; // Return default branch ID when no user is authenticated
        }

        $userId = \Auth::user()->id;
        $currentBranchId = 0;

        $checkActiveBranch = \DB::table('users_branches')->where('user_id', $userId)->where('is_active', 1)->first();

        if ($checkActiveBranch) {
            $currentBranchId = $checkActiveBranch->branch_id;
        } else {
            $currentBranchId = \Auth::user()->branch_id ?? 0;
        }
        return $currentBranchId;
    }

    public function mobileCurrentBranch($userId)
    {
        $currentBranchId = 0;

        $checkActiveBranch = \DB::table('users_branches')->where('user_id', $userId)->where('is_active', 1)->first();

        if ($checkActiveBranch) {
            $currentBranchId = $checkActiveBranch->branch_id;
        } else {
            // For mobile API, get branch from the user record directly instead of Auth
            $user = \App\Models\User::find($userId);
            $currentBranchId = $user ? $user->branch_id : 0;
        }
        return $currentBranchId;
    }

    public function getBranchInfo()
    {
        // Check if user is authenticated
        if (!\Auth::check() || !\Auth::user()) {
            return null; // Return null when no user is authenticated
        }

        $userId = \Auth::user()->id;
        return \DB::table('users_branches')
            ->leftJoin('branches', 'branches.branch_id', 'users_branches.branch_id')
            ->where('user_id', $userId)
            ->where('is_active', 1)
            ->first();
    }

    public function branchStudents($branchId)
    {
        $academicYearId = self::academicYear();

        $list = StudentInformation::leftJoin('students_classroom', function ($join) use ($academicYearId, $branchId) {
            $join->on('students_classroom.student_id', 'student_information.id');
            $join->where('students_classroom.academic_year_id', $academicYearId);
            $join->where('students_classroom.branch_id', $branchId);
        })
            ->leftJoin('classrooms', 'classrooms.classroom_id', 'students_classroom.classroom_id')
            ->where('student_information.branch_id', $branchId)
            ->where('student_information.student_status', 1)
            ->orderBy('name')
            ->get();
        return $list;
    }
    public function branchStudentsAY($branchId, $academicYearId)
    {

        $list = StudentInformation::leftJoin('students_classroom', function ($join) use ($academicYearId, $branchId) {
            $join->on('students_classroom.student_id', 'student_information.id');
            $join->where('students_classroom.academic_year_id', $academicYearId);
            $join->where('students_classroom.branch_id', $branchId);
        })
            ->leftJoin('classrooms', 'classrooms.classroom_id', 'students_classroom.classroom_id')
            ->where('student_information.branch_id', $branchId)
            ->where('student_information.student_status', 1)
            ->orderBy('name')
            ->get();
        return $list;
    }

    public function branchUsers($branchId)
    {
        return User::where('branch_id', $branchId)
            ->where('user_status', 1)
            ->where('user_type', 'staff')
            ->get();
    }

    public function branchUsersOpt($branchId)
    {
        $rd = '';
        $users = User::where('branch_id', $branchId)
            ->where('user_status', 1)
            ->where('user_type', 'staff')
            ->get();

        foreach ($users as $key => $user) {
            $rd .= '<option value="' . $user->id . '">' . $user->name . '</option>';
        }
        return $rd;
    }

    public function branchClassrooms_()
    {
        $classrooms = Classroom::where('academic_year_id', self::academicYear())
            ->where('branch_id', self::currentBranch())
            ->where('status', 1)
            // [Modified: 05-05-2025]
            // Sort classroom names by the first numeric value found
            // to ensure natural numeric order like: 1, 2, 3, ..., 10, 11, 12
            ->orderByRaw('CAST(REGEXP_SUBSTR(classroom_name, "[0-9]+") AS UNSIGNED)')
            ->get();

        return $classrooms;
    }
    public function branchClassrooms()
    {
        $classrooms = Classroom::where('academic_year_id', self::academicYear())
            ->where('branch_id', self::currentBranch())
            ->where('status', 1)
            ->get()
            ->sortBy(function ($classroom) {
                preg_match('/\d+/', $classroom->classroom_name, $matches);
                return isset($matches[0]) ? (int) $matches[0] : 0;
            })
            ->values(); // Reindex the collection

        return $classrooms;
    }


    public function mobileBranchClassrooms($userId)
    {
        $branch = self::mobileCurrentBranch($userId);
        $classrooms = Classroom::where('academic_year_id', self::branchAcademicYear($branch))
            ->where('branch_id', $branch)
            ->where('status', 1)
            ->get();

        return $classrooms;
    }

    public function branchElectiveGrades()
    {
        return $grades = ElectiveGrade::where('academic_year_id', self::academicYear())
            ->where('branch_id', self::currentBranch())
            ->get();
    }

    public function branchElectiveGradesHTML()
    {
        $rd = '<option></option>';

        $grades = ElectiveGrade::where('academic_year_id', self::academicYear())
            ->where('branch_id', self::currentBranch())
            ->get();

        foreach ($grades as $key => $grade) {
            $rd .= '<option value="' . $grade->grade_id . '">' . $grade->grade_name . '</option>';
        }

        return $rd;
    }

    public function getClassroomStudents($classroomId)
    {
        return Classroom::find($classroomId)->students;
    }

    public function userTimetable($userId)
    {

        return $timetable = Timetable::with('elective_grade', 'subject')->select('subject_id', 'grade_id')
            ->where('academic_year_id', self::academicYear())
            ->where('branch_id', self::currentBranch())
            ->where('user_id', $userId)
            ->groupBy('subject_id', 'grade_id')
            ->get();
    }

    public function homeroomClassrooms($userId)
    {
        return $classrooms = Classroom::where('academic_year_id', self::academicYear())
            ->where('branch_id', self::currentBranch())
            ->where('homeroom_teacher_id', $userId)
            ->get();
    }

    public function teacherSubjects($userId)
    {
        return $subjects = Timetable::select('subject_id')
            ->where('academic_year_id', self::academicYear())
            ->where('branch_id', self::currentBranch())
            ->where('user_id', $userId)
            ->groupBy('subject_id')
            ->get();
    }

    public function teacherGrades($userId)
    {
        return $subjects = Timetable::select('grade_id')
            ->where('academic_year_id', self::academicYear())
            ->where('branch_id', self::currentBranch())
            ->where('user_id', $userId)
            ->groupBy('grade_id')
            ->get();
    }

    public function teacherTCLClassrooms($userId)
    {
        return $classrooms = TeacherClassLesson::select('class_id')
            ->where('academic_year_id', self::academicYear())
            ->where('branch_id', self::currentBranch())
            ->where('teacher_id', $userId)
            ->groupBy('class_id')
            ->get();
    }

    public function currentTerm() {
        $today = date('Y-m-d');
        $term = AcademicSemester::where('academic_year_id', self::academicYear())
                                ->where('branch_id', self::currentBranch())
                                ->where('start_date', '<=', $today)
                                ->where('end_date', '>=', $today)
                                ->first();
        return $term;
    }

    public function academicYearList() {
       return AcademicYear::select('academic_year_id', 'academic_year')->where('is_active', '!=', 1)
        ->orderBy('academic_year_id', 'desc')
        ->get();
        
    }
    public function tclBranchUsers($branchId){
        $users = User::where('user_status', 1)
            ->where('user_type', 'staff')
            ->whereHas('userBranches', function ($query) use ($branchId) {
                $query->where('branch_id', $branchId);
            })
            ->get();
        return $users;
    }
}
