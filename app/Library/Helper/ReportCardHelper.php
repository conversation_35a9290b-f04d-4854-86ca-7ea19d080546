<?php

namespace App\Library\Helper;

use App\Models\Dictionary;
use App\Models\AcademicYear;
use App\Models\AcademicWeek;
use App\Models\SettingsAssessment;
use App\Models\SummativeAssessmentData;
use App\Models\FormativeAssessmentData;
use App\Models\SummativeAssessment;
use App\Models\ElectiveGrade;
use App\Models\ScoreTemplateData;

class ReportCardHelper
{
    // public function calculateSubjectAverage($data)
    // {
    //     $subjectAverage = 0;
    //     $allScoreData = array();
    //     $typeIdArray = array();
    //     $totalAveragePoint = null;

    //     $isNA = "0";
    //     $assessmentCounter = 0;
    //     $typeData = '';
    //     $typePercentageSum = 0;
    //     if ($data['print_summative'] == 1) {
    //         foreach ($data['summative_assessments'] as $key => $assessment) {
    //             $assessmentCounter++;
    //             $newTempSummativeArray = array(
    //                 'type_id' => $assessment->type_id,
    //                 'percentage' => $assessment->type_percentage,
    //                 'score' => $assessment->score_percentage
    //             );

    //             array_push($allScoreData, $newTempSummativeArray);
    //             array_push($typeIdArray, $assessment->type_id);
    //         }
    //     }

    //     if ($data['print_formative'] == 1) {
    //         foreach ($data['formative_assessments'] as $key => $assessment) {
    //             $formativeAssessmentAverage = 0;
    //             $assessmentCounter++;

    //             if ($assessment->type_id > 0) {
    //                 $currentFormativePercent = $assessment->type_percentage;
    //                 $currentFormativeScore = 0;

    //                 $tmp1 = $assessment->t1;
    //                 $tmp2 = $assessment->t2;
    //                 $tmp3 = $assessment->t3;
    //                 $tmp4 = $assessment->t4;

    //                 if ($tmp1 > 0) {
    //                     $currentFormativeScore = $tmp1;
    //                 } else if ($tmp2 > 0) {
    //                     $currentFormativeScore = $tmp2;
    //                 } else if ($tmp3 > 0) {
    //                     $currentFormativeScore = $tmp3;
    //                 } else if ($tmp4 > 0) {
    //                     $currentFormativeScore = $tmp4;
    //                 } else {
    //                     $currentFormativeScore = 0;
    //                 }

    //                 $newTempFormativeArray = array(
    //                     'type_id' => $assessment->type_id,
    //                     'percentage' => $currentFormativePercent,
    //                     'score' => $currentFormativeScore
    //                 );

    //                 array_push($allScoreData, $newTempFormativeArray);
    //                 array_push($typeIdArray, $assessment->type_id);
    //             }
    //         }
    //     }
    //     //--------------------------------------------------------------
    //     //---  CALCULATE SUBJECT AVERAGE--------------------------------
    //     //--------------------------------------------------------------
    //     usort($allScoreData, function ($a, $b) {
    //         return $a['type_id'] <=> $b['type_id'];
    //     });

    //     $scoreObject = json_decode(json_encode((object) $allScoreData), FALSE);
    //     $typeIdArray = array_unique($typeIdArray);
    //     $tempTypeAveragePercentArray = [];
    //     foreach ($typeIdArray as $key => $tempTypeId) {
    //         // $typeInfo = \DB::table('academic_summative_score_template_data')
    //         //     ->where('data_id', $tempTypeId)
    //         //     ->first();

    //         $typeTitle = "Subject Report";
    //         $isTempNA = null;
    //         $isTempNACounter = 0;
    //         $flag = $tempTypeId;

    //         $filtered = array_filter($allScoreData, function ($var) use ($flag) {
    //             return ($var["type_id"] == $flag);
    //         });

    //         $tempPercent = 0;
    //         $tempCount = count($filtered);
    //         $tempAverageSum = 0;

    //         foreach ($filtered as $key => $filter) {
    //             if (is_numeric($filter["score"])) {
    //                 $tempAverageSum += $filter["score"];
    //                 $tempPercent = $filter["percentage"];
    //             } else {
    //                 $isTempNACounter++;
    //             }
    //         }
    //         $typePercentageSum += $tempPercent;
    //         $tempTypeAverage = $tempAverageSum / $tempCount;
    //         $tempTypeAveragePercent = ($tempTypeAverage / 100) * $tempPercent;
    //         // $tempTypeAveragePercentArray[$tempPercent] = $tempTypeAveragePercent;
    //         // $totalAveragePoint += $tempTypeAveragePercent;
    //         $tempTypeAveragePercentArray[$tempTypeId] = [
    //             'averagePercent' => $tempTypeAveragePercent,
    //             'percentage' => $tempPercent,
    //         ];
    //         $isTempNA = $isTempNACounter > 0 ? "N\A" : $tempTypeAverage;
    //         $typeData .= $typeTitle . "|" . $isTempNA . "|*|";
    //     }
    //     if ($typePercentageSum < 100) {
    //         $remainPercentage = 100 - $typePercentageSum;
    //         // if (count($tempTypeAveragePercentArray) > 0) {
    //         //     $smallestSum = min(array_column($tempTypeAveragePercentArray, 'averagePercent'));
    //         //     // $smallestIndex  = array_search($smallestSum, $tempTypeAveragePercentArray);
    //         //     $smallestIndex = array_search($smallestSum, array_column($tempTypeAveragePercentArray, 'averagePercent'));
    //         //     $smallestTypeId  = array_keys($tempTypeAveragePercentArray)[$smallestIndex];
    //         //     // dd($smallestTypeId);
    //         //     $smallestPercent = $tempTypeAveragePercentArray[$smallestTypeId]['percentage'];
    //         //     $totalRemainPercentage = $smallestPercent  + $remainPercentage;
    //         //     $newAddedPoint = ($totalRemainPercentage * $smallestSum) / ($smallestPercent == 0 ? 1 : $smallestPercent);
    //         //     $tempTypeAveragePercentArray[$smallestTypeId]['averagePercent'] = $newAddedPoint;
    //         // }
    //         $totalAveragePoint = array_sum(array_column($tempTypeAveragePercentArray, 'averagePercent'));
    //     } else {
    //         // dd($tempTypeAveragePercentArray);
    //         $totalAveragePoint = array_sum(array_column($tempTypeAveragePercentArray, 'averagePercent'));
    //     }
    //     $sumTotalLetter = '';
    //     $totalAveragePoint = round($totalAveragePoint, 0);

    //     if ($totalAveragePoint >= 85) {
    //         $sumTotalLetter = 'A';
    //     } else if ($totalAveragePoint >= 70 && $totalAveragePoint <= 84) {
    //         $sumTotalLetter = 'B';
    //     } else if ($totalAveragePoint >= 60 && $totalAveragePoint <= 69) {
    //         $sumTotalLetter = 'C';
    //     } else if ($totalAveragePoint >= 50 && $totalAveragePoint <= 59) {
    //         $sumTotalLetter = 'D';
    //     } else if ($totalAveragePoint > 40 && $totalAveragePoint <= 49) {
    //         $sumTotalLetter = 'E';
    //     } else {
    //         $sumTotalLetter = 'U';
    //     }

    //     if ($assessmentCounter == 0) $isNA = "1";
    //     return $totalAveragePoint . "/*/" . $isNA . "/*/" . $sumTotalLetter . "/*/" . $typeData . "/*/" . $typePercentageSum;
    // }


    // Used tn the Assessment Calcualtion 
    public function calculateSubjectAverage($data)
    {
        $subjectAverage = 0;
        $allScoreData = array();
        $typeIdArray = array();

        $finalAllScoreData = array();
        $finalTypeIdArray = array();

        $final_assessments = [];
        $normal_assessments = [];

        $totalAveragePoint = null;

        $isNA = "0";
        $assessmentCounter = 0;
        $typeData = '';
        $typePercentageSum = 0;

        $finalIsNA = "0";
        $finalAssessmentCounter = 0;
        $finalTypeData = '';
        $finalTypePercentageSum = 0;
        if ($data['print_summative'] == 1) {
            // Filter Final and Normal Assessments
            foreach ($data['summative_assessments'] as $assessment) {
                if ($assessment->assessment->is_final_exam == 1) {
                    $final_assessments[] = $assessment;
                } else {
                    $normal_assessments[] = $assessment;
                }
            }
            // Normal Assessment 
            foreach ($normal_assessments as $key => $normal_assessment) {
                $assessmentCounter++;
                $newTempSummativeArray = array(
                    'type_id' => $normal_assessment->type_id,
                    'percentage' => $normal_assessment->type_percentage,
                    'score' => $normal_assessment->score_percentage,
                    'template_id' => $normal_assessment->template_id
                );

                array_push($allScoreData, $newTempSummativeArray);
                array_push($typeIdArray, $normal_assessment->type_id);
            }
            // Final Assessment
            foreach ($final_assessments as $key => $final_assessment) {
                $finalAssessmentCounter++;
                $newFinalTempSummativeArray = array(
                    'type_id' => $final_assessment->type_id,
                    'percentage' => $final_assessment->type_percentage,
                    'score' => $final_assessment->score_percentage,
                    'template_id' => $final_assessment->template_id
                );

                array_push($finalAllScoreData, $newFinalTempSummativeArray);
                array_push($finalTypeIdArray, $final_assessment->type_id);
            }
        }

        if ($data['print_formative'] == 1) {
            foreach ($data['formative_assessments'] as $key => $assessment) {
                $formativeAssessmentAverage = 0;
                $assessmentCounter++;

                if ($assessment->type_id > 0) {
                    $currentFormativePercent = $assessment->type_percentage;
                    $currentFormativeScore = 0;

                    $tmp1 = $assessment->t1;
                    $tmp2 = $assessment->t2;
                    $tmp3 = $assessment->t3;
                    $tmp4 = $assessment->t4;

                    if ($tmp1 > 0) {
                        $currentFormativeScore = $tmp1;
                    } else if ($tmp2 > 0) {
                        $currentFormativeScore = $tmp2;
                    } else if ($tmp3 > 0) {
                        $currentFormativeScore = $tmp3;
                    } else if ($tmp4 > 0) {
                        $currentFormativeScore = $tmp4;
                    } else {
                        $currentFormativeScore = 0;
                    }

                    $newTempFormativeArray = array(
                        'type_id' => $assessment->type_id,
                        'percentage' => $currentFormativePercent,
                        'score' => $currentFormativeScore
                    );

                    array_push($allScoreData, $newTempFormativeArray);
                    array_push($typeIdArray, $assessment->type_id);
                }
            }
        }
        //--------------------------------------------------------------
        //---  CALCULATE SUBJECT AVERAGE--------------------------------
        //--------------------------------------------------------------
        usort($allScoreData, function ($a, $b) {
            return $a['type_id'] <=> $b['type_id'];
        });
        usort($finalAllScoreData, function ($a, $b) {
            return $a['type_id'] <=> $b['type_id'];
        });

        $scoreObject = json_decode(json_encode((object) $allScoreData), FALSE);
        $typeIdArray = array_unique($typeIdArray);
        $tempTypeAveragePercentArray = [];

        $finalScoreObject = json_decode(json_encode((object) $finalAllScoreData), FALSE);
        $finalTypeIdArray = array_unique($finalTypeIdArray);
        $finalTempTypeAveragePercentArray = [];
        // Calculate Normal
        foreach ($typeIdArray as $key => $tempTypeId) {
            $typeTitle = "Subject Report";
            $isTempNA = null;
            $isTempNACounter = 0;
            $flag = $tempTypeId;
            $filtered = array_filter($allScoreData, function ($var) use ($flag) {
                return ($var["type_id"] == $flag);
            });


            $tempPercent = 0;
            $tempCount = count($filtered);
            $tempAverageSum = 0;
            $tempId = 0;

            foreach ($filtered as $key => $filter) {
                if (is_numeric($filter["score"])) {
                    $tempAverageSum += $filter["score"];
                    $tempPercent = $filter["percentage"];
                    $tempId = $filter["template_id"];
                } else {
                    $isTempNACounter++;
                }
            }

            $totalPercentage = ScoreTemplateData::where('summative_template_id', $tempId)
                ->where('is_final_exam_percentage', 0)
                ->sum('percentage');


            $typePercentageSum += $tempPercent;
            $tempTypeAverage = $tempAverageSum / ($tempCount == 0 ? 1 : $tempCount);
            $tempTypeAveragePercent = ($tempTypeAverage / 100) * $tempPercent;
            $tempTypeAveragePercentArray[$tempTypeId] = [
                'averagePercent' => $tempTypeAveragePercent,
                'percentage' => $tempPercent,
                'totalPercentage' => $totalPercentage,
            ];
            $isTempNA = $isTempNACounter > 0 ? "N\A" : $tempTypeAverage;
            $typeData .= $typeTitle . "|" . $isTempNA . "|*|";
        }
        // Calculate Final
        foreach ($finalTypeIdArray as $key => $finalTempTypeId) {
            $finalTypeTitle = "Subject Report";
            $isFinalTempNA = null;
            $isFinalTempNACounter = 0;
            $finalFlag = $finalTempTypeId;

            $finalFiltered = array_filter($finalAllScoreData, function ($var) use ($finalFlag) {
                return ($var["type_id"] == $finalFlag);
            });

            $finalTempPercent = 0;
            $finalTempCount = count($finalFiltered);
            $finalTempAverageSum = 0;
            $finalTempId = 0;

            foreach ($finalFiltered as $key => $finalFilter) {
                if (is_numeric($finalFilter["score"])) {
                    $finalTempAverageSum += $finalFilter["score"];
                    $finalTempPercent = $finalFilter["percentage"];
                    $finalTempId = $finalFilter["template_id"];
                } else {
                    $isFinalTempNACounter++;
                }
            }

            $finalTotalPercentage = ScoreTemplateData::where('summative_template_id', $finalTempId)
                ->where('is_final_exam_percentage', 1)
                ->sum('percentage');


            $finalTypePercentageSum += $finalTempPercent;
            $finalTempTypeAverage = $finalTempAverageSum / ($finalTempCount == 0 ? 1 : $finalTempCount);
            $finalTempTypeAveragePercent = ($finalTempTypeAverage / 100) * $finalTempPercent;
            $finalTempTypeAveragePercentArray[$finalTempTypeId] = [
                'averagePercent' => $finalTempTypeAveragePercent,
                'percentage' => $finalTempPercent,
                'totalPercentage' => $finalTotalPercentage,
            ];
            $isFinalTempNA = $isFinalTempNACounter > 0 ? "N\A" : $finalTempTypeAverage;
            $typeData .= $finalTypeTitle . "|" . $isFinalTempNA . "|*|";
        }
        $totalAveragePoint = array_sum(array_column($tempTypeAveragePercentArray, 'averagePercent'));
        $finalTotalAveragePoint = array_sum(array_column($finalTempTypeAveragePercentArray, 'averagePercent'));
        $totalAveragePercentage = round(array_values(array_column($tempTypeAveragePercentArray, 'totalPercentage'))[0] ?? 0, 0);
        $finalTotalAveragePercentage = round(array_values(array_column($finalTempTypeAveragePercentArray, 'totalPercentage'))[0] ?? 0, 0);

        $sumTotalLetter = '';
        $averagePoint =  $totalAveragePoint + $finalTotalAveragePoint;
        $averagePercentage =  $totalAveragePercentage + $finalTotalAveragePercentage;
        // dd($averagePoint);
        $averagePoint = round($averagePoint, 0);
        if ($averagePoint >= 85) {
            $sumTotalLetter = 'A';
        } else if ($averagePoint >= 70 && $averagePoint <= 84) {
            $sumTotalLetter = 'B';
        } else if ($averagePoint >= 60 && $averagePoint <= 69) {
            $sumTotalLetter = 'C';
        } else if ($averagePoint >= 50 && $averagePoint <= 59) {
            $sumTotalLetter = 'D';
        } else if ($averagePoint > 40 && $averagePoint <= 49) {
            $sumTotalLetter = 'E';
        } else {
            $sumTotalLetter = 'U';
        }

        if ($assessmentCounter == 0) $isNA = "1";
        return $averagePoint . "/*/" . $isNA . "/*/" . $sumTotalLetter . "/*/" . $typeData . "/*/" . $typePercentageSum . "/*/" . $averagePercentage;
    }


    // public  function getLetterGrade($score)
    // {
    //     $letterGrade = '';
    //     $score = round($score, 0);
    //     if ($score >= 90) {
    //         $letterGrade = 'A*';
    //     } else if ($score >= 80 && $score < 90) {
    //         $letterGrade = 'A';
    //     } else if ($score >= 70 && $score < 80) {
    //         $letterGrade = 'B';
    //     } else if ($score >= 60 && $score < 70) {
    //         $letterGrade = 'C';
    //     } else if ($score >= 50 && $score < 60) {
    //         $letterGrade = 'D';
    //     } else if ($score >= 40 && $score < 50) {
    //         $letterGrade = 'E';
    //     } else {
    //         $letterGrade = 'U';
    //     }

    //     return $letterGrade;
    // }
    public  function getLetterGrade($score, $percentage)
    {
        if ($percentage != null && $percentage != 0) {
            $score = ($score * 100) / $percentage;
        }
        $letterGrade = '';
        $score = round($score, 0);
        if ($score >= 90) {
            $letterGrade = 'A*';
        } else if ($score >= 80 && $score < 90) {
            $letterGrade = 'A';
        } else if ($score >= 70 && $score < 80) {
            $letterGrade = 'B';
        } else if ($score >= 60 && $score < 70) {
            $letterGrade = 'C';
        } else if ($score >= 50 && $score < 60) {
            $letterGrade = 'D';
        } else if ($score >= 40 && $score < 50) {
            $letterGrade = 'E';
        } else {
            $letterGrade = 'U';
        }

        return $letterGrade;
    }

    // Used in the House System Calculation
    public function calculateStudentTotalAverage($data)
    {
        $totalScore = 0;
        $totalLines = 0;
        $totalSubjects = 0;
        $subjects = ElectiveGrade::select(
            'subjects.subject_id',
            'subjects.subject_name',
            'academic_elective_grade.grade_id',
            'subjects.subject_type'
        )
            ->leftJoin('academic_elective_grade_students', 'academic_elective_grade.grade_id', 'academic_elective_grade_students.grade_id')
            ->leftJoin('subjects', 'subjects.subject_id', 'academic_elective_grade.subject_id')
            ->where('academic_elective_grade_students.student_id', $data['student_id'])
            ->where('academic_elective_grade.academic_year_id', $data['academicYearId'])
            ->groupBy(
                'academic_elective_grade.subject_id',
                'subjects.subject_id',
                'subjects.subject_name',
                'academic_elective_grade.grade_id'
            )
            ->get();

        foreach ($subjects as $subject) {
            $totalSubjects++;
            $summativeAssessments = SummativeAssessmentData::leftJoin('academic_summative_assessments', 'academic_summative_assessments.assessment_id', 'academic_summative_assessments_data.assessment_id')
                ->whereBetween('academic_summative_assessments.date', [$data['start_date'], $data['finish_date']])
                ->where('academic_summative_assessments_data.student_id', $data['student_id'])
                ->where('academic_summative_assessments.subject_id', $subject->subject_id)
                ->where('academic_summative_assessments.academic_year_id', $data['academicYearId'])
                ->get();

            $formativeAssessments = FormativeAssessmentData::select(
                'academic_formative_assessments.assessment_name as assessment_name',
                'academic_formative_assessments.t1 as tt1',
                'academic_formative_assessments.t2 as tt2',
                'academic_formative_assessments.t3 as tt3',
                'academic_formative_assessments.t4 as tt4',
                'academic_formative_assessments_data.t1 as t1',
                'academic_formative_assessments_data.t2 as t2',
                'academic_formative_assessments_data.t3 as t3',
                'academic_formative_assessments_data.t4 as t4',
                'academic_formative_assessments_data.type_id as type_id',
                'academic_formative_assessments_data.type_percentage as type_percentage'
            )
                ->leftJoin('academic_formative_assessments', 'academic_formative_assessments.formative_assessment_id', 'academic_formative_assessments_data.formative_assessment_id')
                ->whereBetween('academic_formative_assessments.date', [$data['start_date'], $data['finish_date']])
                ->where('academic_formative_assessments_data.student_id', $data['student_id'])
                ->where('academic_formative_assessments.subject_id', $subject->subject_id)
                ->where('academic_formative_assessments.academic_year_id', $data['academicYearId'])
                ->get();

            $result = self::calculateSubjectAverage([
                'student_id'    => $data['student_id'],
                'subject_id'    => $subject->subject_id,
                'start_date'    => $data['start_date'],
                'end_date'      => $data['finish_date'],
                'academic_year_id'  => $data['academicYearId'],
                'print_summative'   => 1,
                'print_formative'   => 1,
                'summative_assessments' => $summativeAssessments,
                'formative_assessments' => $formativeAssessments,
            ]);

            $resultArr = explode("/*/", $result);
            $totalScore += $resultArr[0];
            // echo $data['student_id']." -> SubjectID:".$subject->subject_id." SCORE: ".$resultArr[0]."\r\n";
        }

        if ($totalSubjects > 0) {
            $totalScore = $totalScore / $totalSubjects;
        } else {
            $totalScore = 0;
        }

        // echo "---------------\r\nTOTAL SCORE:".$totalScore;
        return $totalScore;
    }

    // function calculateSubjectSummativeStrand($data)
    // {
    //     //$studentId, $subjectId, $startDate, $endDate, $strandId + $gradeID
    //     //get $studentId, $subjectId, $startDate, $endDate, $gradeID data
    //     //calculate grades for each strand, convert to letter grade and push to strands array
    //     //calculate final grade and convert to letter grade, save as final
    //     //return back strands array and final grade

    //     $strandRows = "";
    //     $strandGrades = array();
    //     $result = "";

    //     $checkForSameSubjects = ElectiveGrade::select('academic_elective_grade.grade_id')
    //         ->leftJoin('academic_elective_grade_students', 'academic_elective_grade.grade_id', 'academic_elective_grade_students.grade_id')
    //         ->where('academic_elective_grade_students.student_id', $data['student_id'])
    //         ->where('academic_elective_grade.academic_year_id', $data['academic_year_id'])
    //         ->where('academic_elective_grade.subject_id', $data['subject_id'])
    //         ->get();
    //     $gradeIDs = $checkForSameSubjects->toArray();

    //     $strandTypes = SummativeAssessment::leftJoin('skills_strands', 'skills_strands.skill_strand_id', 'academic_summative_assessments.strand')
    //         ->whereIn('grade_id', $gradeIDs)
    //         ->groupBy('academic_summative_assessments.strand')
    //         ->get();
    //     foreach ($strandTypes as $strand) {
    //         $totalSummativeStrandScore = 0;
    //         $summativeAssessments = SummativeAssessmentData::selectRaw('AVG(score_percentage) scoreAvg, type_id, type_percentage,  template_id')
    //             ->leftJoin('academic_summative_assessments', 'academic_summative_assessments.assessment_id', 'academic_summative_assessments_data.assessment_id')
    //             ->whereBetween('academic_summative_assessments.date', [$data['start_date'], $data['end_date']])
    //             ->where('academic_summative_assessments_data.student_id', $data['student_id'])
    //             ->where('academic_summative_assessments.subject_id', $data['subject_id'])
    //             ->where('academic_summative_assessments.strand', '=', $strand->skill_strand_id)
    //             ->whereNotNull('academic_summative_assessments_data.score')
    //             ->groupBy('template_id', 'type_id')
    //             ->get();
    //         if (count($summativeAssessments) > 0) {
    //             $tmplTypes = array();
    //             $tmplTypePercentages = array();
    //             $tmplTypeAverages = array();
    //             $totalPercentage = 0;
    //             foreach ($summativeAssessments as $key => $assessment) {
    //                 $tmplTypes[] = $assessment->type_id;
    //                 $tmplTypePercentages[$assessment->type_id] = $assessment->type_percentage;
    //                 $tmplTypeAverages[$assessment->type_id] = $assessment->scoreAvg;
    //                 $totalPercentage += $assessment->type_percentage;
    //             }
    //             $tempTypeAveragePercentArray = [];
    //             foreach ($tmplTypes as $asmType) {
    //                 if ($tmplTypeAverages[$asmType] > 0) {
    //                     $totalSummativeStrandScore = ($tmplTypeAverages[$asmType] / 100) * $tmplTypePercentages[$asmType];
    //                     // $tempTypeAveragePercentArray[$tmplTypePercentages[$asmType]] = $totalSummativeStrandScore;
    //                     $tempTypeAveragePercentArray[$asmType] = [
    //                         'averagePercent' => $totalSummativeStrandScore,
    //                         'percentage' => $tmplTypePercentages[$asmType],
    //                     ];
    //                 }
    //             }


    //             // if ($totalPercentage < 100) {
    //             //     $remainPercentage = 100 - $totalPercentage;
    //             //     if (count($tempTypeAveragePercentArray) > 0) {
    //             //         $smallestSum  = min($tempTypeAveragePercentArray);
    //             //         $smallestIndex  = array_search($smallestSum, $tempTypeAveragePercentArray);
    //             //         $totalRemainPercentage = $smallestIndex  + $remainPercentage;
    //             //         $newAddedPoint = ($totalRemainPercentage * $smallestSum) / $smallestIndex;
    //             //         $tempTypeAveragePercentArray[$smallestIndex] = $newAddedPoint;
    //             //     }
    //             //     $totalSummativeStrandScore = array_sum($tempTypeAveragePercentArray);
    //             // } else {
    //             //     $totalSummativeStrandScore = array_sum($tempTypeAveragePercentArray);
    //             // }
    //             if ($totalPercentage < 100) {
    //                 $remainPercentage = 100 - $totalPercentage;
    //                 // if (count($tempTypeAveragePercentArray) > 0) {
    //                 //     $smallestSum = min(array_column($tempTypeAveragePercentArray, 'averagePercent'));
    //                 //     // $smallestIndex  = array_search($smallestSum, $tempTypeAveragePercentArray);
    //                 //     $smallestIndex = array_search($smallestSum, array_column($tempTypeAveragePercentArray, 'averagePercent'));
    //                 //     $smallestTypeId  = array_keys($tempTypeAveragePercentArray)[$smallestIndex];
    //                 //     // dd($smallestTypeId);
    //                 //     $smallestPercent = $tempTypeAveragePercentArray[$smallestTypeId]['percentage'];
    //                 //     $totalRemainPercentage = $smallestPercent  + $remainPercentage;
    //                 //     $newAddedPoint = ($totalRemainPercentage * $smallestSum) / $smallestPercent;
    //                 //     $tempTypeAveragePercentArray[$smallestTypeId]['averagePercent'] = $newAddedPoint;
    //                 // }
    //                 $totalSummativeStrandScore = array_sum(array_column($tempTypeAveragePercentArray, 'averagePercent'));
    //             } else {
    //                 // dd($tempTypeAveragePercentArray);
    //                 $totalSummativeStrandScore = array_sum(array_column($tempTypeAveragePercentArray, 'averagePercent'));
    //             }

    //             $strandRows .= $strand->value . "/_/" . self::getLetterGrade($totalSummativeStrandScore) . "/_/" . $totalSummativeStrandScore . "/x/";
    //             $strandGrades[] = round($totalSummativeStrandScore, 0);
    //         }
    //     }
    //     $subjectFinalScore = 0;
    //     if (count($strandGrades) > 0) {
    //         $subjectFinal = self::getLetterGrade(array_sum($strandGrades) / count($strandGrades));
    //         $subjectFinalScore = array_sum($strandGrades) / count($strandGrades);
    //     } else {
    //         $subjectFinal = 0;
    //     }
    //     return $strandRows . "/*/" . $subjectFinal . "/*/" . $subjectFinalScore;
    // }

    // Used in the term and final Report Card Calculation
    function calculateSubjectSummativeStrand($data)
    {
        //$studentId, $subjectId, $startDate, $endDate, $strandId + $gradeID
        //get $studentId, $subjectId, $startDate, $endDate, $gradeID data
        //calculate grades for each strand, convert to letter grade and push to strands array
        //calculate final grade and convert to letter grade, save as final
        //return back strands array and final grade

        $strandRows = "";
        $strandGrades = array();
        $finalStrandGrades = array();
        $result = "";

        $checkForSameSubjects = ElectiveGrade::select('academic_elective_grade.grade_id')
            ->leftJoin('academic_elective_grade_students', 'academic_elective_grade.grade_id', 'academic_elective_grade_students.grade_id')
            ->where('academic_elective_grade_students.student_id', $data['student_id'])
            ->where('academic_elective_grade.academic_year_id', $data['academic_year_id'])
            ->where('academic_elective_grade.subject_id', $data['subject_id'])
            ->get();
        $gradeIDs = $checkForSameSubjects->toArray();

        $strandTypes = SummativeAssessment::leftJoin('skills_strands', 'skills_strands.skill_strand_id', 'academic_summative_assessments.strand')
            ->whereIn('grade_id', $gradeIDs)
            ->orderBy('is_final_exam','desc')
            ->groupBy('academic_summative_assessments.strand')
            ->get();
            // dd($data);
            $initialFinalExamScore =0;
            $initialFinalExamPercentage =0;
            $initialFinalExamScorePercentage =0;
        foreach ($strandTypes as $strand) {
            $is_final_exam = 0;
            if ($strand->is_final_exam == 1) {
                $is_final_exam = 1;
            }
            $totalSummativeStrandScore = 0;
            $finalSummativeStrandScore = 0;
            $summativeAssessments = SummativeAssessmentData::selectRaw('AVG(score_percentage) scoreAvg, type_id, type_percentage,  template_id ,is_final_exam')
                ->leftJoin('academic_summative_assessments', 'academic_summative_assessments.assessment_id', 'academic_summative_assessments_data.assessment_id')
                ->whereBetween('academic_summative_assessments.date', [$data['start_date'], $data['end_date']])
                ->where('academic_summative_assessments_data.student_id', $data['student_id'])
                ->where('academic_summative_assessments.subject_id', $data['subject_id'])
                ->where('academic_summative_assessments.strand', '=', $strand->skill_strand_id)
                // ->whereNotNull('academic_summative_assessments_data.score')
                ->groupBy('template_id', 'type_id')
                ->get();

            if (count($summativeAssessments) > 0) {
                $tmplTypes = array();
                $tmplTypePercentages = array();
                $tmplTypeAverages = array();
                $tmplTypeIsFinal = array();
                $totalPercentage = 0;
                $finalTmplTypes = array();
                $finalTmplTypePercentages = array();
                $finalTmplTypeAverages = array();
                $finalTmplTypeIsFinal = array();
                foreach ($summativeAssessments as $key => $assessment) {
                    if ($assessment->is_final_exam == 1) {
                        $finalTmplTypes[] = $assessment->type_id;
                        $finalTmplTypePercentages[$assessment->type_id] = $assessment->type_percentage;
                        $finalTmplTypeAverages[$assessment->type_id] = $assessment->scoreAvg;
                        $finalTmplTypeIsFinal[$assessment->type_id] = $assessment->template_id;
                    } else {
                        $tmplTypes[] = $assessment->type_id;
                        $tmplTypePercentages[$assessment->type_id] = $assessment->type_percentage;
                        $tmplTypeAverages[$assessment->type_id] = $assessment->scoreAvg;
                        $tmplTypeIsFinal[$assessment->type_id] = $assessment->template_id;
                    }
                    $totalPercentage += $assessment->type_percentage;
                }
                $tempTypeAveragePercentArray = [];
                $finalTempTypeAveragePercentArray = [];
                foreach ($tmplTypes as $asdasd => $asmType) {
                                    
                    $totalPercentage = ScoreTemplateData::where('summative_template_id', $tmplTypeIsFinal[$asmType])
                        ->where('is_final_exam_percentage', 0)
                        ->sum('percentage');
                    // if ($tmplTypeAverages[$asmType] > 0) {
                    $totalSummativeStrandScore = ($tmplTypeAverages[$asmType] / 100) * $tmplTypePercentages[$asmType];
                    $tempTypeAveragePercentArray[$asmType] = [
                        'averagePercent' => $totalSummativeStrandScore,
                        'percentage' => $tmplTypePercentages[$asmType],
                        'is_final_exam' => $is_final_exam,
                        'totalPercentage' => $totalPercentage,
                    ];
                    // }
                    
                }
                
                foreach ($finalTmplTypes as $finalAsmType) {
                    $finalTotalPercentage = ScoreTemplateData::where('summative_template_id', $finalTmplTypeIsFinal[$finalAsmType])
                        ->where('is_final_exam_percentage', 1)
                        ->sum('percentage');
                    // if ($finalTmplTypeAverages[$finalAsmType] > 0) {
                    $finalSummativeStrandScore = ($finalTmplTypeAverages[$finalAsmType] / 100) * $finalTmplTypePercentages[$finalAsmType];
                    $finalTempTypeAveragePercentArray[$finalAsmType] = [
                        'averagePercent' => $finalSummativeStrandScore,
                        'percentage' => $finalTmplTypePercentages[$finalAsmType],
                        'is_final_exam' => $is_final_exam,
                        'totalPercentage' => $finalTotalPercentage,
                    ];
                    // }
                    // dd($TempScoreData);
                }

                if (!empty($tempTypeAveragePercentArray)) {
                    $totalSummativeStrandScore =  round(array_sum(array_column($tempTypeAveragePercentArray, 'averagePercent')), 0);
                    $totalSummativeStrandPercentage =  round(array_column($tempTypeAveragePercentArray, 'totalPercentage')[0], 0);
                }
                if (!empty($finalTempTypeAveragePercentArray)) {
                    $finalSummativeStrandScore =  round(array_sum(array_column($finalTempTypeAveragePercentArray, 'averagePercent')), 0);
                    $finalSummativeStrandPercentage =  round(array_column($finalTempTypeAveragePercentArray, 'totalPercentage')[0], 0);
                    $initialFinalExamScore = $finalSummativeStrandScore;
                    $initialFinalExamPercentage = $finalSummativeStrandPercentage;
                    $initialFinalExamScorePercentage = ($finalSummativeStrandScore/($finalSummativeStrandPercentage == 0 ? 1 : $finalSummativeStrandPercentage)) *100;
                    $finalStrandGrades[] = round($finalSummativeStrandScore, 0);
                }
                if ($totalSummativeStrandScore > 0) {
                    $totalSummativeStrandScore      = $totalSummativeStrandScore + (isset($initialFinalExamScore) ? $initialFinalExamScore : 0 );
                    $totalSummativeStrandPercentage = $totalSummativeStrandPercentage + (isset($initialFinalExamPercentage) ? $initialFinalExamPercentage : 0 );
                    $strandGrades[] = round($totalSummativeStrandScore, 0);
                    $strandRows .= $strand->value . "/_/" . self::getLetterGrade($totalSummativeStrandScore, $totalSummativeStrandPercentage) . "/_/" . $totalSummativeStrandScore ."/_/" . $totalSummativeStrandPercentage . "/x/";
                } else {
                    if(!empty($tempTypeAveragePercentArray)){
                    // $totalSummativeStrandScore      = $totalSummativeStrandScore + (isset($initialFinalExamScore) ? $initialFinalExamScore : 0 );
                    $totalSummativeStrandScore = (($initialFinalExamScorePercentage * $totalSummativeStrandPercentage) / 100) + (isset($initialFinalExamScore) ? $initialFinalExamScore : 0 );
                    $totalSummativeStrandPercentage = $totalSummativeStrandPercentage + (isset($initialFinalExamPercentage) ? $initialFinalExamPercentage : 0 );
                    $strandGrades[] = round($totalSummativeStrandScore, 0);
                    $strandRows .= $strand->value . "/_/" . self::getLetterGrade($totalSummativeStrandScore, $totalSummativeStrandPercentage) . "/_/" . $totalSummativeStrandScore ."/_/" . $totalSummativeStrandPercentage . "/x/";
                    }else{
                        // $strandRows .= $strand->value . "/_/" . self::getLetterGrade($finalSummativeStrandScore, $finalSummativeStrandPercentage) . "/_/" . $finalSummativeStrandScore . "/x/";
                    }
                // dd($strandRows);
                }
            }
        }
        //    dd($totalSummativeStrandPercentage);
        $subjectFinalScore = 0;
        if (count($strandGrades) > 0 || count($finalStrandGrades) > 0) {
            if (count($strandGrades) > 0) {
                $subjectScore = array_sum($strandGrades) / count($strandGrades);
            } else {
                $subjectScore = 0;
            }
            $finalSubjectScore  = array_sum($finalStrandGrades);
            $subjectFinalScore = round($subjectScore , 0);
            $subjectFinal = self::getLetterGrade($subjectFinalScore, 100);
        } else {
            $subjectFinal = 0;
        }
        return $strandRows . "/*/" . $subjectFinal . "/*/" . $subjectFinalScore ;
    }


    // function calculateSubjectFinal($data)
    // {
    //     //$studentId, $subjectId, $startDate, $endDate, $strandId + $gradeID
    //     //get $studentId, $subjectId, $startDate, $endDate, $gradeID data
    //     //calculate grades for each strand, convert to letter grade and push to strands array
    //     //calculate final grade and convert to letter grade, save as final
    //     //return back strands array and final grade

    //     $strandRows = "";
    //     $strandGrades = array();
    //     $result = "";

    //     $strandTypes = SummativeAssessment::leftJoin('skills_strands', 'skills_strands.skill_strand_id', 'academic_summative_assessments.strand')
    //         ->where('grade_id', $data['grade_id'])
    //         ->groupBy('academic_summative_assessments.strand')
    //         ->get();
    //     foreach ($strandTypes as $strand) {
    //         $totalSummativeStrandScore = 0;
    //         $summativeAssessments = SummativeAssessmentData::selectRaw('AVG(score_percentage) scoreAvg, type_id, type_percentage,  template_id')
    //             ->leftJoin('academic_summative_assessments', 'academic_summative_assessments.assessment_id', 'academic_summative_assessments_data.assessment_id')
    //             ->whereBetween('academic_summative_assessments.date', [$data['start_date'], $data['end_date']])
    //             ->where('academic_summative_assessments_data.student_id', $data['student_id'])
    //             ->where('academic_summative_assessments.subject_id', $data['subject_id'])
    //             ->where('academic_summative_assessments.strand', '=', $strand->skill_strand_id)
    //             ->whereNotNull('academic_summative_assessments_data.score')
    //             ->groupBy('template_id', 'type_id')
    //             ->get();
    //         if (count($summativeAssessments) > 0) {
    //             $tmplTypes = array();
    //             $tmplTypePercentages = array();
    //             $tmplTypeAverages = array();
    //             $totalPercentage = 0;
    //             foreach ($summativeAssessments as $key => $assessment) {
    //                 $tmplTypes[] = $assessment->type_id;
    //                 $tmplTypePercentages[$assessment->type_id] = $assessment->type_percentage;
    //                 $tmplTypeAverages[$assessment->type_id] = $assessment->scoreAvg;
    //                 $totalPercentage += $assessment->type_percentage;
    //             }
    //             $tempTypeAveragePercentArray = [];
    //             foreach ($tmplTypes as $asmType) {
    //                 if ($tmplTypeAverages[$asmType] > 0) {
    //                     $tempTypeAveragePercent = ($tmplTypeAverages[$asmType] / 100) * $tmplTypePercentages[$asmType];
    //                     // $tempTypeAveragePercentArray[$tmplTypePercentages[$asmType]] = $totalSummativeStrandScore;
    //                     $tempTypeAveragePercentArray[$asmType] = [
    //                         'averagePercent' => $tempTypeAveragePercent,
    //                         'percentage' => $tmplTypePercentages[$asmType],
    //                     ];
    //                 }
    //             }

    //             // if ($totalPercentage < 100) {
    //             //     $remainPercentage = 100 - $totalPercentage;
    //             //     if (count($tempTypeAveragePercentArray) > 0) {
    //             //         $smallestSum  = min($tempTypeAveragePercentArray);
    //             //         $smallestIndex  = array_search($smallestSum, $tempTypeAveragePercentArray);
    //             //         $totalRemainPercentage = $smallestIndex  + $remainPercentage;
    //             //         $newAddedPoint = ($totalRemainPercentage * $smallestSum) / $smallestIndex;
    //             //         $tempTypeAveragePercentArray[$smallestIndex] = $newAddedPoint;
    //             //     }
    //             //     $totalSummativeStrandScore = array_sum($tempTypeAveragePercentArray);
    //             // } else {
    //             //     $totalSummativeStrandScore = array_sum($tempTypeAveragePercentArray);
    //             // }
    //             if ($totalPercentage < 100) {
    //                 $remainPercentage = 100 - $totalPercentage;
    //                 // if (count($tempTypeAveragePercentArray) > 0) {
    //                 //     $smallestSum = min(array_column($tempTypeAveragePercentArray, 'averagePercent'));
    //                 //     $smallestIndex = array_search($smallestSum, array_column($tempTypeAveragePercentArray, 'averagePercent'));
    //                 //     $smallestTypeId  = array_keys($tempTypeAveragePercentArray)[$smallestIndex];
    //                 //     $smallestPercent = $tempTypeAveragePercentArray[$smallestTypeId]['percentage'];
    //                 //     $totalRemainPercentage = $smallestPercent  + $remainPercentage;
    //                 //     $newAddedPoint = ($totalRemainPercentage * $smallestSum) / $smallestPercent;
    //                 //     $tempTypeAveragePercentArray[$smallestTypeId]['averagePercent'] = $newAddedPoint;
    //                 // }
    //                 $totalSummativeStrandScore = array_sum(array_column($tempTypeAveragePercentArray, 'averagePercent'));
    //             } else {
    //                 $totalSummativeStrandScore = array_sum(array_column($tempTypeAveragePercentArray, 'averagePercent'));
    //             }
    //             $strandGrades[] = $totalSummativeStrandScore;
    //         }
    //     }
    //     $subjectFinalLetter = '';
    //     if (count($strandGrades) > 0) {
    //         $subjectFinal = array_sum($strandGrades) / count($strandGrades);
    //         $subjectFinalLetter = self::getLetterGrade($subjectFinal);
    //     } else {
    //         $subjectFinal = 0;
    //     }
    //     return $subjectFinal . "/*/" . $subjectFinalLetter;
    // }

    // Used in the Subject Report Card Calculation
    function calculateSubjectFinal($data)
    {
        //$studentId, $subjectId, $startDate, $endDate, $strandId + $gradeID
        //get $studentId, $subjectId, $startDate, $endDate, $gradeID data
        //calculate grades for each strand, convert to letter grade and push to strands array
        //calculate final grade and convert to letter grade, save as final
        //return back strands array and final grade

        $strandRows = "";
        $strandGrades = array();
        $finalStrandGrades = array();
        $result = "";
        $initialFinalExamScore =0;
        $initialFinalExamPercentage =0;
        $initialFinalExamScorePercentage =0;
        $strandTypes = SummativeAssessment::leftJoin('skills_strands', 'skills_strands.skill_strand_id', 'academic_summative_assessments.strand')
            ->where('grade_id', $data['grade_id'])
            ->orderBy('is_final_exam','desc')
            ->groupBy('academic_summative_assessments.strand')
            ->get();

        foreach ($strandTypes as $strand) {
            $is_final_exam = 0;
            if ($strand->is_final_exam == 1) {
                $is_final_exam = 1;
            }
            $totalSummativeStrandScore = 0;
            $finalSummativeStrandScore = 0;
            $summativeAssessments = SummativeAssessmentData::selectRaw('AVG(score_percentage) scoreAvg, type_id, type_percentage,  template_id, is_final_exam')
                ->leftJoin('academic_summative_assessments', 'academic_summative_assessments.assessment_id', 'academic_summative_assessments_data.assessment_id')
                ->whereBetween('academic_summative_assessments.date', [$data['start_date'], $data['end_date']])
                ->where('academic_summative_assessments_data.student_id', $data['student_id'])
                ->where('academic_summative_assessments.subject_id', $data['subject_id'])
                ->where('academic_summative_assessments.strand', '=', $strand->skill_strand_id)
                // ->whereNotNull('academic_summative_assessments_data.score')
                ->groupBy('template_id', 'type_id')
                ->get();
            if (count($summativeAssessments) > 0) {
                $tmplTypes = array();
                $tmplTypePercentages = array();
                $tmplTypeAverages = array();
                $tmplTypeIsFinal = array();
                $totalPercentage = 0;
                $finalTmplTypes = array();
                $finalTmplTypePercentages = array();
                $finalTmplTypeAverages = array();
                $finalTmplTypeIsFinal = array();
                foreach ($summativeAssessments as $key => $assessment) {
                    if ($assessment->is_final_exam == 1) {
                        $finalTmplTypes[] = $assessment->type_id;
                        $finalTmplTypePercentages[$assessment->type_id] = $assessment->type_percentage;
                        $finalTmplTypeAverages[$assessment->type_id] = $assessment->scoreAvg;
                        $finalTmplTypeIsFinal[$assessment->type_id] = $assessment->template_id;
                    } else {
                        $tmplTypes[] = $assessment->type_id;
                        $tmplTypePercentages[$assessment->type_id] = $assessment->type_percentage;
                        $tmplTypeAverages[$assessment->type_id] = $assessment->scoreAvg;
                        $tmplTypeIsFinal[$assessment->type_id] = $assessment->template_id;
                    }
                    $totalPercentage += $assessment->type_percentage;
                }
                $tempTypeAveragePercentArray = [];
                $finalTempTypeAveragePercentArray = [];
                foreach ($tmplTypes as $asmType) {
                    // if ($tmplTypeAverages[$asmType] > 0) {
                    $totalPercentage = ScoreTemplateData::where('summative_template_id', $tmplTypeIsFinal[$asmType])
                        ->where('is_final_exam_percentage', 0)
                        ->sum('percentage');
                    $totalSummativeStrandScore = ($tmplTypeAverages[$asmType] / 100) * $tmplTypePercentages[$asmType];
                    $tempTypeAveragePercentArray[$asmType] = [
                        'averagePercent' => $totalSummativeStrandScore,
                        'percentage' => $tmplTypePercentages[$asmType],
                        'is_final_exam' => $is_final_exam,
                        'totalPercentage' => $totalPercentage
                    ];
                    // }
                }
                foreach ($finalTmplTypes as $finalAsmType) {
                    // if ($finalTmplTypeAverages[$finalAsmType] > 0) {
                    $finalTotalPercentage = ScoreTemplateData::where('summative_template_id', $finalTmplTypeIsFinal[$finalAsmType])
                        ->where('is_final_exam_percentage', 1)
                        ->sum('percentage');
                    $finalSummativeStrandScore = ($finalTmplTypeAverages[$finalAsmType] / 100) * $finalTmplTypePercentages[$finalAsmType];
                    $finalTempTypeAveragePercentArray[$finalAsmType] = [
                        'averagePercent' => $finalSummativeStrandScore,
                        'percentage' => $finalTmplTypePercentages[$finalAsmType],
                        'is_final_exam' => $is_final_exam,
                        'totalPercentage' => $finalTotalPercentage,
                    ];
                    // }
                }
                if (!empty($tempTypeAveragePercentArray)) {
                    $totalSummativeStrandScore =  round(array_sum(array_column($tempTypeAveragePercentArray, 'averagePercent')), 0);
                    $totalSummativeStrandPercentage =  round(array_column($tempTypeAveragePercentArray, 'totalPercentage')[0], 0);
                    
                }
                if (!empty($finalTempTypeAveragePercentArray)) {
                    $finalSummativeStrandScore =  round(array_sum(array_column($finalTempTypeAveragePercentArray, 'averagePercent')), 0);
                    $finalTotalSummativeStrandPercentage =  round(array_column($finalTempTypeAveragePercentArray, 'totalPercentage')[0], 0);
                    $initialFinalExamScore = $finalSummativeStrandScore;
                    $initialFinalExamPercentage = $finalTotalSummativeStrandPercentage;
                    $initialFinalExamScorePercentage = ($finalSummativeStrandScore/($finalTotalSummativeStrandPercentage == 0 ? 1 : $finalTotalSummativeStrandPercentage)) *100;
                    $finalStrandGrades[] = round($finalSummativeStrandScore, 0);
                }
                if ($totalSummativeStrandScore > 0) {
                    $totalSummativeStrandScore      = $totalSummativeStrandScore + (isset($initialFinalExamScore) ? $initialFinalExamScore : 0 );
                    $totalSummativeStrandPercentage = $totalSummativeStrandPercentage + (isset($initialFinalExamPercentage) ? $initialFinalExamPercentage : 0 );
                    $strandGrades[] = round($totalSummativeStrandScore, 0);
                    $strandRows .= $strand->value . "/_/" . self::getLetterGrade($totalSummativeStrandScore, $totalSummativeStrandPercentage) . "/_/" . $totalSummativeStrandScore . "/x/";
                } else {
                    if(!empty($tempTypeAveragePercentArray)){
                    $totalSummativeStrandScore = (($initialFinalExamScorePercentage * $totalSummativeStrandPercentage) / 100) + (isset($initialFinalExamScore) ? $initialFinalExamScore : 0 );
                    $totalSummativeStrandPercentage = $totalSummativeStrandPercentage + (isset($initialFinalExamPercentage) ? $initialFinalExamPercentage : 0 );
                    $strandGrades[] = round($totalSummativeStrandScore, 0);
                    $strandRows .= $strand->value . "/_/" . self::getLetterGrade($totalSummativeStrandScore, $totalSummativeStrandPercentage) . "/_/" . $totalSummativeStrandScore . "/x/";
                    }else{
                    $strandRows .= $strand->value . "/_/" . self::getLetterGrade($finalSummativeStrandScore, $finalTotalSummativeStrandPercentage) . "/_/" . $finalSummativeStrandScore . "/x/";
                    }
            }
            }
        }
        $subjectFinalScore = 0;
        if (count($strandGrades) > 0 || count($finalStrandGrades) > 0) {
            if (count($strandGrades) > 0) {
                $subjectScore = array_sum($strandGrades) / count($strandGrades);
            } else {
                $subjectScore = 0;
            }
            $finalSubjectScore  = array_sum($finalStrandGrades);
            $subjectFinalScore = round($subjectScore , 0);
            $subjectFinalLetter = self::getLetterGrade($subjectFinalScore, 100);
        } else {
            $subjectFinalLetter = 0;
        }

        return $subjectFinalScore . "/*/" . $subjectFinalLetter;
    }

    // KG report card functions
    public function getStudentSubjectFormativeAssessments($subjectId, $studentId, $academicYearId, $startDate, $endDate)
    {
        // dd($subjectId, $studentId, $academicYearId, $startDate, $endDate);
        $studentAssessments = \DB::table('academic_formative_assessments as afa')
            ->join('academic_formative_assessments_data as afad', 'afa.formative_assessment_id', '=', 'afad.formative_assessment_id')
            ->join('academic_learning_areas as ala', 'afa.uid', '=', 'ala.uid') // Adding join on academic_learning_areas
            ->leftJoin('academic_summative_score_template_data as asstd', 'asstd.data_id', '=', 'afad.type_id')
            ->select(
                'afad.student_id',
                'afa.uid',
                \DB::raw('MAX(asstd.title) as title'),
                \DB::raw('MAX(ala.name) as assessment_name'), // Getting assessment name from academic_learning_areas
                \DB::raw('MAX(afa.date) as date'),
                \DB::raw("
            MAX(
                CASE 
                    WHEN afad.t1 IS NOT NULL AND afad.t1 != '' THEN 5
                    WHEN afad.t2 IS NOT NULL AND afad.t2 != '' THEN 4
                    WHEN afad.t3 IS NOT NULL AND afad.t3 != '' THEN 3
                    WHEN afad.t4 IS NOT NULL AND afad.t4 != '' THEN 2
                    ELSE 0
                END
            ) as highest_rank
        ")
            )
            ->where('afa.academic_year_id', $academicYearId)
            ->where('afa.subject_id', $subjectId)
            ->whereBetween('afa.date', [$startDate, $endDate])
            ->where('afad.student_id', $studentId)
            ->groupBy('afad.student_id', 'afa.uid') // Grouping by student_id and uid
            ->orderBy('highest_rank', 'desc') // Highest rank first
            ->orderBy('date', 'desc') // Tie-breaker if ranks match
            ->get();

        // Next, map the data to the desired format
        $result = $studentAssessments->map(function ($assessment) {
            return [
                'assessment_name' => $assessment->assessment_name ?? 'Unknown',
                'grade' => $this->determineGrade($assessment->highest_rank),  // This can be further optimized if necessary
                'type' => $assessment->title ?? '-', // Directly accessing 'title' from the join
            ];
        });

        // Group by the assessment type for a structured response
        return $result->groupBy('type');
    }

    public function determineGrade($highestRank)
    {
        if ($highestRank == 5) {
            return "Strong"; // Advanced
        } elseif ($highestRank == 4) {
            return "Satisfactory"; // Expected Level
        } elseif ($highestRank == 3) {
            return "Needs Improvement";  // Meeting Expected Level
        } elseif ($highestRank == 2) {
            return "Not Taught";  // Below Expected Level
        } 
        return null; // Default if none of the conditions are met
    }
}
