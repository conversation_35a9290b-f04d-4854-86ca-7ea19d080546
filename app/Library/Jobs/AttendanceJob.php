<?php

namespace App\Library\Jobs;

use App\Library\Repository\MobileNotificationRepository;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Carbon\Carbon;
use Exception;
use App\Models\User;
use App\Library\Helper\AcademicHelper;
use App\Models\UserAttendanceReport;
use App\Models\AcademicCalendar;
use App\Models\AttendanceGroupMember;
use App\Models\AttendanceGroupTime;
use App\Models\AttendanceLog;

class AttendanceJob {

    protected AcademicHelper $ah;

    public function __construct(AcademicHelper $ah, MobileNotificationRepository $mobileNotification)
    {
        $this->mobileNotification = $mobileNotification;
        $this->ah = $ah;
    }
    // public function __construct(AcademicHelper $ah) {
    //     $this->ah = $ah;
    // }

    function calculateLate($entrance, $mustEnter) {
        $startDate = new \DateTime($entrance);
        $mustDate = new \DateTime($mustEnter);
        $diff =  ($startDate->getTimestamp() - $mustDate->getTimestamp())/60;
        return floor($diff);
    }

    function calculateEarly($exit, $mustExit) {
        $diff = '';
        try {
          $exitDate = new \DateTime($exit);
          $mustDate = new \DateTime($mustExit);
          $diff =  ($mustDate->getTimestamp() - $exitDate->getTimestamp())/60;
        } catch (\Exception $e) {
          $diff = 0;
        }
        return floor($diff);
    }

    public function checkStaffAttendance($today = null) {
        $ah = new AcademicHelper();
        $academicYearId = $ah->academicYear();

        if ($today == '') {
            $today = date("Y-m-d");
        }

        $weekShortName = date('D', strtotime($today));
        $weekDayNumber = date('N', strtotime($today));

        echo "Date: ".$today."\r\nWeekShortName :".$weekShortName."\r\nWeekDay:".$weekDayNumber."\r\nTime: ".date("H:i")."\r\n";

        UserAttendanceReport::where('date', $today)->delete();

        if ($weekShortName != 'Sun') {
            echo "Start Calc. \r\n-----------------------------------------\r\n";
            $isHoliday = AcademicCalendar::where('date', $today)->first();
            if(!$isHoliday) {
                echo "Today is not holiday.\r\n";

                $users = User::where('user_status', 1)
                             ->where('user_type', 'staff')
                             ->get();

                foreach ($users as $user) {
                  echo "User:".$user->id." Branch:".$user->branch_id;
                  $userGroupInfo = AttendanceGroupMember::where('user_id', $user->id)
                                                        ->orderBy('group_member_id', 'desc')
                                                        ->first();

                  if($userGroupInfo) {
                    echo " Group:".$userGroupInfo->group->group_name." gID:".$userGroupInfo->group_id."\r\n";
                   
                    $groupTime = AttendanceGroupTime::where('weekday', $weekDayNumber)
                                                   ->where('group_id', $userGroupInfo->group_id)
                                                   ->first();

                    if($groupTime) {
                      $statusMessage = "User do not have input or output data";
                      $reportStatus = "";
                      $isAbsent = 0;
                      $isExcused = 0;
                      $lateMinute = 0;
                      $earlyLeaveMinute = 0;
                      $isLate = 0;
                      $isEarly = 0;

                      echo " _| Entrance:".$groupTime->entrance." Exit:".$groupTime->exit;

                      $userData = AttendanceLog::selectRaw(
                                              'MIN(time) as entrance_time, MAX(time) as exit_time'
                                            )
                                        ->where('date', $today)
                                        ->where('attendance_id', $userGroupInfo->group_member_id)
                                        ->groupBy('date')
                                        ->first();

                      if($userData) {
                        echo " \r\n __uEntrance:".$userData->entrance_time." >> uExit:".$userData->exit_time;

                        $mustEnter = $groupTime->entrance;
                        $mustExit = $groupTime->exit;
                        $entranceTime = $userData->entrance_time;
                        $exitTime = $userData->exit_time;

                        if(!$entranceTime) {
                          $isAbsent = 1;
                          $isExcused = 0;
                          $lateMinute = 0;
                          $earlyLeaveMinute = 0;
                          $isLate = 1;
                        } else {
                          $lateMinute = self::calculateLate($today." ".$entranceTime, $today." ".$mustEnter);
                          // dd($lateMinute);
                          if ($exitTime) {
                            $earlyLeaveMinute = self::calculateEarly($today." ".$mustExit, $today." ".$exitTime);
                            if ($earlyLeaveMinute > 2) {
                              $isEarly = 1;
                              $reportStatus = "Early leave. ";
                            }
                          } else {
                            $statusMessage = "No EXIT";
                          }
                        }

                        if ($lateMinute < 0) { $lateMinute = 0;   }
                        if ($earlyLeaveMinute < 0) { $earlyLeaveMinute = 0;   }
                        if ($lateMinute > 2) { $reportStatus = $reportStatus." Late."; }

                        if($entranceTime == $exitTime) {
                          $exitTime = null;
                        }

                        $newRecord = UserAttendanceReport::create([
                          'branch_id'             => $user->branch_id,
                          'user_id'               => $user->id,
                          'date'                  => $today,
                          'must_enter'            => $mustEnter,
                          'must_exit'             => $mustExit,
                          'entrance_time'         => $entranceTime,
                          'exit_time'             => $exitTime,
                          'is_late'               => $isLate,
                          'is_early'              => $isEarly,
                          'late_minutes'          => $lateMinute,
                          'early_leave_minutes'   => $earlyLeaveMinute,
                          'is_absent'             => $isAbsent,
                          'is_excused'            => $isExcused,
                          'late_info'             => $statusMessage,
                          'report_status'         => $reportStatus
                        ]);

                        if($newRecord) {
                          echo "\r\n |__________ New Record added for: ".$user->id." RuID:".$newRecord->attendance_log_id." _____|\r\n";
                        } else {
                          echo "\r\n *** CAN NOT ADD RECORd *** \r\n";
                        }

                      } else {
                        echo " ::No user DATA \r\n";
                      }
                    } else {
                      echo " ::GroupTime no set\r\n";
                    }
                    echo "\r\n";
                  } else {
                    echo " ::Invalid Attendance Settings\r\n";
                  }

                }
            } else { echo "Today is HOLIDAY \r\n"; }
        } else { echo "Today is SUNDAY \r\n"; }

    }


}