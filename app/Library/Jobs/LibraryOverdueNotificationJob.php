<?php

namespace App\Library\Jobs;

use App\Models\LibraryIssue;
use Carbon\Carbon;

class LibraryOverdueNotificationJob
{

    public function sendLibraryOverdueNotification($ah, $mobileNotification)
    {
        $today = Carbon::today();
        $books = LibraryIssue::selectRaw('library_issues.*, library_books.*, users.name as user_name, students_classroom.classroom_id, classrooms.*, DATEDIFF(?, should_return_date) as total_overdue', [$today])
            ->leftJoin('library_books', 'library_issues.book_id', '=', 'library_books.book_id')
            ->leftJoin('users', 'library_issues.user_id', '=', 'users.id')
            ->leftJoin('students_classroom', 'library_issues.user_id', '=', 'students_classroom.student_id')
            ->leftJoin('classrooms', 'students_classroom.classroom_id', '=', 'classrooms.classroom_id')
            ->whereRaw('should_return_date < ? AND is_returned = 0', [$today])
            ->where('library_books.status', 1)
            // ->where('library_books.branch_id', 6)
            ->where('students_classroom.academic_year_id', $ah->academicYear())
            ->where('users.user_type', 'student')
            ->orderBy('users.id')
            ->get('user_id');
        $userDetails = $books->map(function ($book) {
            return [
                'user_id'               => $book->user_id,
                'title'                 => $book->title,
                'user_name'             => $book->user_name,
                'should_return_date'    => $book->should_return_date,
                'user_type'             => $book->user->user_type,
            ];
        });
        foreach ($userDetails as $detail) {
            $msj = $detail['user_name'] . "'s borrowed book ( " . $detail['title'] . ' ) is overdue and return date is ' . $detail['should_return_date'];
            // echo $msj;
            // return 0;
            // $mobileNotification->sendSingle([
            //     'title'             => 'Library',
            //     'message'           => $msj,
            //     'student'           => $detail['user_id'],
            //     'type'              => 'library',
            //     'user_type'         =>  $detail['user_type'],
            // ]);
            $mobileNotification->sendRealTime([
                'title'             => 'Library',
                'message'           => $msj,
                'student'           => $detail['user_id'],
                'type'              => 'library',
                'user_type'         =>  $detail['user_type'],
            ]);
        }
    }
}
