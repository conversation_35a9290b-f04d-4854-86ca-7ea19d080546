<?php

namespace App\Library\Repository;

use App\Models\User;

use App\Models\UserBranch;
use Illuminate\Support\Str;
use App\Library\Helper\Email;
use App\Models\StudentFamily;
use App\Models\StudentDetail;
use App\Models\StudentContract;
use Illuminate\Support\Facades\DB;
use App\Library\Helper\GeneralHelper;
use App\Library\Helper\AcademicHelper;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use \Exception;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;

class StudentRepository
{

    public function archivedStudents()
    {
        return $users = User::where('user_type', 'student')
            ->where('user_status', 0)
            ->get();
    }

    public function restoreStudent($studentId)
    {
        $user = User::find($studentId);
        $user->user_status = 1;
        $user->save();
    }

    public function archiveStudent($data)
    {
        $student = User::find($data['studentId']);
        $student->user_status = 0;
        $student->save();
    }


    public function getIdCardInfo($id)
    {
        $rd = '';
        $academicHelper = new AcademicHelper();
        $academicYearId = $academicHelper->academicYear();

        $info = User::leftJoin('students_classroom', function ($join) use ($academicYearId) {
            $join->on('students_classroom.student_id', 'users.id')
                ->where('students_classroom.academic_year_id', $academicYearId);
        })
            ->leftJoin('classrooms', 'classrooms.classroom_id', 'students_classroom.classroom_id')
            ->where('users.id', $id)
            ->first();

        if ($info) {
            $rd .= $info->classroom_name . "*-*"; //0
            $rd .= $info->branch->branch_name . "*-*"; //1
            $rd .= $info->student_details->first_name . " " . $info->student_details->middle_name . " " . $info->student_details->last_name . "*-*"; //2
            $rd .= $info->photo . "*-*"; //3
            $rd .= $info->branch->branch_principal . "*-*"; //4
            $rd .= $info->branch->branch_address . "*-*"; //5
            $rd .= $info->branch->branch_city . "*-*"; //6
            $rd .= $info->branch->branch_phone . "*-*"; //7
            $rd .= $info->branch->branch_website . "*-*"; //8
            $rd .= $info->branch->branch_email . "*-*"; //9
            $rd .= $info->id . "*-*"; //10
            $rd .= $info->id . "*-*"; //11
            $rd .= $info->id . "*-*"; //12
            $rd .= $info->id . "*-*"; //13
            $rd .= $info->id . "*-*"; //14
        }
        return $rd;
    }

    public function create($data)
    {
        $academicHelper = new AcademicHelper();
        $generalHelper = new GeneralHelper();
        $branchId = $academicHelper->currentBranch();

        DB::beginTransaction();
        try {
            $uid = uniqid('', true);
            $email = $data['email'] == '' ? uniqid() . "@nomail.yet" : $data['email'];
            $password = $generalHelper->generatePassword(8);

            $student = new User();
            $student->name = $data['first_name'] . " " . $data['middle_name'] . " " . $data['last_name'];
            $student->password = \Hash::make($password);
            $student->username = $uid;
            $student->email = $email;
            $student->mobile_phone = $data['mobile_phone'];
            $student->branch_id = $branchId;
            $student->user_type = 'student';
            $student->uid = $uid;
            $student->user_status = 1;
            $student->data_sibling = $data['inputSibling'];
            $student->data_school = $data['inputSchool'];
            $student->rfid = $data['rfid'];
            $student->save();

            $newUser = User::where('uid', $uid)->first();
            $newUserId = $newUser->id;
            User::where('uid', $uid)->update(['username' => $newUserId]);

            if (isset($data['photo'])) {
                $filePath = "data/studentFiles/" . $newUserId;
                if (!\File::exists($filePath)) {
                    $result = \File::makeDirectory($filePath, 0755, true);
                }

                $photo = $data['photo'];
                $extension = $photo->getClientOriginalExtension();
                $imageUid = $uid;
                $photoName = $filePath . "/student_photo_" . $imageUid . "." . $extension;

                $newUser = User::find($newUserId);
                $newUser->photo = "/" . $photoName;
                $newUser->save();
                $photo->move($filePath, "/student_photo_" . $imageUid . "." . $extension);
            }

            $detail = new StudentDetail();
            $detail->enrollment_date = $data['enrollment_date'];
            $detail->student_id = $newUserId;
            $detail->first_time_password = '';
            $detail->student_number = $data['student_no'];
            $detail->first_name = $data['first_name'];
            $detail->middle_name = $data['middle_name'];
            $detail->last_name = $data['last_name'];
            $detail->gender = $data['gender'];
            $detail->birth_date = $data['birth_date'];
            $detail->nationality = $data['nationality'];
            $detail->national_id = $data['national_id'];
            $detail->passport_id = $data['passport_id'];
            $detail->current_address = $data['current_address'];
            $detail->reg_entry_date_start = $data['entry_date_start'];
            $detail->reg_entry_date_end = $data['entry_date_end'];
            $detail->reg_current_curriculum = $data['current_curriculum'];
            $detail->reg_last_grade_completed = $data['last_grade_completed'];
            $detail->reg_current_grade = ''; //--remove
            $detail->reg_grade_apply_for = $data['grade_apply_for'];
            $detail->reg_returning_student = $data['returning_student'];
            $detail->reg_grades_years_attended = ''; //--remove
            $detail->reg_who_pay_school_fee = $data['who_pay_school_fee'];
            $detail->reg_how_did_you_hear_about_us = $data['how_did_you_hear_about_us'];
            $detail->reg_length_of_stay = ''; //--remove
            $detail->lang_student_primary = $data['student_primary_language'];
            $detail->lang_student_other = $data['student_other_language'];
            $detail->lang_father_primary = $data['father_primary_language'];
            $detail->lang_father_other = $data['father_other_language'];
            $detail->lang_mother_primary = $data['mother_primary_language'];
            $detail->lang_mother_other = $data['mother_other_language'];
            $detail->lang_spoken_at_home = $data['language_spoken_at_home'];
            $detail->lang_has_enrolled_esl_support_program = $data['enrolled_esl_support_programme'];
            $detail->lang_has_ever_been_enrolled_esl_program = $data['ever_enrolled_esl_support_programme'];
            $detail->tags = $data['tags'];
            $detail->additional_attached_report_card = $data['attached_report_card'];
            $detail->additional_has_skipped_grade = $data['has_skipped_grade'];
            $detail->additional_skipped_grade = $data['skipped_grade'];
            $detail->additional_has_gifted_programme = $data['has_gifted_programme'];
            $detail->additional_gifted_programme = $data['gifted_programme'];
            $detail->additional_has_repeated_grade = $data['has_repeated_grade'];
            $detail->additional_repeated_grade = $data['repeated_grade'];
            $detail->additional_has_learning_difficulties = $data['has_learning_difficulties'];
            $detail->additional_learning_difficulties = $data['learning_difficulties'];
            $detail->additional_has_learning_behaviour_difficulties = $data['has_learning_behaviour_difficulties'];
            $detail->additional_learning_behaviour_difficulties = $data['learning_behaviour_difficulties'];
            $detail->form_of_stay = $data['form_of_stay'];
            $detail->form_of_stay_other = $data['form_of_stay_other'];
            //$detail->reg_requested_curriculum = $data['curriculum'];
            $detail->save();

            //-- Insert father details ---
            $father = new StudentFamily();
            $father->student_id = $newUserId;
            $father->family_type            = 'father';
            $father->name                   = $data['father_name'];
            $father->nationality            = $data['father_nationality'];
            $father->passport_number        = $data['father_passport_number'];
            $father->passport_country       = $data['father_passport_country'];
            $father->passport_expiry_date   = $data['father_passport_expiry_date'];
            $father->employer               = $data['father_employer'];
            $father->position               = $data['father_position'];
            $father->business_address       = $data['father_business_address'];
            $father->business_phone         = $data['father_business_phone'];
            $father->business_email         = $data['father_business_email'];
            $father->email                  = $data['father_email'];
            $father->mobile                 = $data['father_mobile_phone'];
            $father->address                = $data['father_address'];
            $father->save();

            //-- Insert mother details ---
            $mother = new StudentFamily();
            $mother->student_id             = $newUserId;
            $mother->family_type            = 'mother';
            $mother->name                   = $data['mother_name'];
            $mother->nationality            = $data['mother_nationality'];
            $mother->passport_number        = $data['mother_passport_number'];
            $mother->passport_country       = $data['mother_passport_country'];
            $mother->passport_expiry_date   = $data['mother_passport_expiry_date'];
            $mother->employer               = $data['mother_employer'];
            $mother->position               = $data['mother_position'];
            $mother->business_address       = $data['mother_business_address'];
            $mother->business_phone         = $data['mother_business_phone'];
            $mother->business_email         = $data['mother_business_email'];
            $mother->email                  = $data['mother_email'];
            $mother->mobile                 = $data['mother_mobile_phone'];
            $mother->address                = $data['mother_address'];
            $mother->save();

            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    public function update($data)
    {
        DB::beginTransaction();
        try {
            $student = User::find($data['student_id']);
            $student->name = $data['first_name'] . " " . $data['middle_name'] . " " . $data['last_name'];
            $student->email = $data['email'];
            $student->mobile_phone = $data['mobile_phone'];
            $student->data_sibling = $data['inputSibling'];
            $student->data_school = $data['inputSchool'];
            $student->rfid = $data['rfid'];

            if (isset($data['photo'])) {
                $uid = uniqid('', true);
                $filePath = "data/studentFiles/" . $data['student_id'];
                if (!\File::exists($filePath)) {
                    $result = \File::makeDirectory($filePath, 0755, true);
                }

                $photo = $data['photo'];
                $extension = $photo->getClientOriginalExtension();
                $imageUid = $uid;
                $photoName = $filePath . "/student_photo_" . $imageUid . "." . $extension;
                $student->photo = "/" . $photoName;
                $photo->move($filePath, "/student_photo_" . $imageUid . "." . $extension);
            }
            $student->save();

            $detail = StudentDetail::find($data['student_id']);
            $detail->enrollment_date                                = $data['enrollment_date'];
            $detail->first_name                                     = $data['first_name'];
            $detail->middle_name                                    = $data['middle_name'];
            $detail->last_name                                      = $data['last_name'];
            $detail->student_number                                 = $data['student_no'];
            $detail->gender                                         = $data['gender'];
            $detail->birth_date                                     = $data['birth_date'];
            $detail->nationality                                    = $data['nationality'];
            $detail->national_id                                    = $data['national_id'];
            $detail->passport_id                                    = $data['passport_id'];
            $detail->current_address                                = $data['current_address'];
            $detail->reg_entry_date_start                           = $data['entry_date_start'];
            $detail->reg_entry_date_end                             = $data['entry_date_end'];
            $detail->reg_current_curriculum                         = $data['current_curriculum'];
            $detail->reg_last_grade_completed                       = $data['last_grade_completed'];
            $detail->reg_grade_apply_for                            = $data['grade_apply_for'];
            $detail->reg_returning_student                          = $data['returning_student'];
            $detail->reg_who_pay_school_fee                         = $data['who_pay_school_fee'];
            $detail->reg_how_did_you_hear_about_us                  = $data['how_did_you_hear_about_us'];
            $detail->lang_student_primary                           = $data['student_primary_language'];
            $detail->lang_student_other                             = $data['student_other_language'];
            $detail->lang_father_primary                            = $data['father_primary_language'];
            $detail->lang_father_other                              = $data['father_other_language'];
            $detail->lang_mother_primary                            = $data['mother_primary_language'];
            $detail->lang_mother_other                              = $data['mother_other_language'];
            $detail->lang_spoken_at_home                            = $data['language_spoken_at_home'];
            $detail->lang_has_enrolled_esl_support_program          = $data['enrolled_esl_support_programme'];
            $detail->lang_has_ever_been_enrolled_esl_program        = $data['ever_enrolled_esl_support_programme'];
            $detail->tags                                           = $data['tags'];
            $detail->additional_attached_report_card                = $data['attached_report_card'];
            $detail->additional_has_skipped_grade                   = $data['has_skipped_grade'];
            $detail->additional_skipped_grade                       = $data['skipped_grade'];
            $detail->additional_has_gifted_programme                = $data['has_gifted_programme'];
            $detail->additional_gifted_programme                    = $data['gifted_programme'];
            $detail->additional_has_repeated_grade                  = $data['has_repeated_grade'];
            $detail->additional_repeated_grade                      = $data['repeated_grade'];
            $detail->additional_has_learning_difficulties           = $data['has_learning_difficulties'];
            $detail->additional_learning_difficulties               = $data['learning_difficulties'];
            $detail->additional_has_learning_behaviour_difficulties = $data['has_learning_behaviour_difficulties'];
            $detail->additional_learning_behaviour_difficulties     = $data['learning_behaviour_difficulties'];
            $detail->form_of_stay                                   = $data['form_of_stay'];
            $detail->form_of_stay_other                             = $data['form_of_stay_other'];
            //$detail->reg_requested_curriculum                       = $data['curriculum'];
            $detail->save();

            //-- Update father details ---
            StudentFamily::where('student_id', $data['student_id'])
                ->where('family_type', 'father')
                ->delete();
            $father = new StudentFamily();
            $father->student_id             = $data['student_id'];
            $father->family_type            = 'father';
            $father->name                   = $data['father_name'];
            $father->nationality            = $data['father_nationality'];
            $father->passport_number        = $data['father_passport_number'];
            $father->passport_country       = $data['father_passport_country'];
            $father->passport_expiry_date   = $data['father_passport_expiry_date'];
            $father->employer               = $data['father_employer'];
            $father->position               = $data['father_position'];
            $father->business_address       = $data['father_business_address'];
            $father->business_phone         = $data['father_business_phone'];
            $father->business_email         = $data['father_business_email'];
            $father->email                  = $data['father_email'];
            $father->mobile                 = $data['father_mobile_phone'];
            $father->address                = $data['father_address'];
            $father->save();

            //-- Update mother details ---
            StudentFamily::where('student_id', $data['student_id'])
                ->where('family_type', 'mother')
                ->delete();
            $mother = new StudentFamily();
            $mother->student_id             = $data['student_id'];
            $mother->family_type            = 'mother';
            $mother->name                   = $data['mother_name'];
            $mother->nationality            = $data['mother_nationality'];
            $mother->passport_number        = $data['mother_passport_number'];
            $mother->passport_country       = $data['mother_passport_country'];
            $mother->passport_expiry_date   = $data['mother_passport_expiry_date'];
            $mother->employer               = $data['mother_employer'];
            $mother->position               = $data['mother_position'];
            $mother->business_address       = $data['mother_business_address'];
            $mother->business_phone         = $data['mother_business_phone'];
            $mother->business_email         = $data['mother_business_email'];
            $mother->email                  = $data['mother_email'];
            $mother->mobile                 = $data['mother_mobile_phone'];
            $mother->address                = $data['mother_address'];
            $mother->save();

            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    public function changePassword($data)
    {
        $student = User::find($data['studentId']);
        $student->password = \Hash::make($data['password']);
        $student->save();
    }

    public function transferStudent($data)
    {
        $student = User::find($data['studentId']);
        $student->branch_id = $data['branchId'];
        $student->save();
    }

    public function createContract($data)
    {
        $check = StudentContract::where('student_id', $data['studentId'])
            ->where('academic_year_id', $data['regYear'])
            ->where('contract_type', $data['regType'])
            ->first();

        if ($check) {
            throw new Exception('Same contract alreadt exists!');
        }

        $contract = StudentContract::create([
            'uuid'      => (string) Str::uuid(),
            'academic_year_id'  => $data['regYear'],
            'student_id'        => $data['studentId'],
            'contract_type'     => $data['regType']
        ]);

        return $contract->uuid;
    }

    public function updateContract($data)
    {
        $contract = StudentContract::find($data["contractId"]);

        if ($data['signatureDataParent'] != '') {
            $contract->parent_signature         = $data['signatureDataParent'];
        }

        if ($data['signatureDataOfficer'] != '') {
            $contract->officer_signature         = $data['signatureDataOfficer'];
        }

        $contract->parent_name              = $data['parentName'];
        $contract->opt_registration_form    = isset($data['optRegForm']) ? 1 : 0;
        $contract->opt_health               = isset($data['optHealthForm']) ? 1 : 0;
        $contract->opt_sibling_information  = isset($data['optStudentInformation']) ? 1 : 0;
        $contract->opt_programme_selection  = isset($data['optProgramme']) ? 1 : 0;
        $contract->opt_payment_agreement    = isset($data['optPaymentAgreement']) ? 1 : 0;
        $contract->opt_fee_policy           = isset($data['optTuitionFee']) ? 1 : 0;
        $contract->opt_parent_agreement     = isset($data['optParentAgreement']) ? 1 : 0;
        $contract->opt_coc_student          = isset($data['optCOCStudent']) ? 1 : 0;
        $contract->opt_coc_parent           = isset($data['optCOCParent']) ? 1 : 0;
        $contract->opt_device_policy        = isset($data['optDevicePolicy']) ? 1 : 0;
        $contract->save();
    }

    public function changeNewPassword($data)
    {
        $user = User::find(Auth::user()->id);

        if ($user) {
            if (Hash::check($data['oldPassword'], $user->password)) {
                $user->password = Hash::make($data['password']);
                $user->save();
                return ['success' => true]; // Return success indicator
            } else {
                return ['success' => false, 'message' => 'The old password is incorrect.'];
            }
        }

        return ['success' => false, 'message' => 'User not found.'];
    }
}
