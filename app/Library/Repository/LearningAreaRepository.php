<?php
namespace App\Library\Repository;

use App\Library\Helper\AcademicHelper;
use App\Models\LearningArea;

class LearningAreaRepository{

    public function store($data){
        $ah = new AcademicHelper();

        $learning_area = new LearningArea();
        $learning_area->academic_year_id = $ah->academicYear();
        $learning_area->branch_id = $ah->currentBranch();
        $learning_area->grades = json_encode($data['grade'], true);
        $learning_area->name = $data['learning_area'];
        $learning_area->uid = uniqid();
        $learning_area->created_by = \Auth::user()->id;
        $learning_area->save(); 
        
        return $learning_area;
    }

    public function destroy($id){
        $learning_area = LearningArea::find($id);
        $learning_area->delete();

        return true;
    }

    public function update($data){
        $learning_area = LearningArea::find($data['id']);
        $learning_area->name = $data['learning_area'];
        $learning_area->grades = json_encode($data['grade'], true);
        $learning_area->updated_by = \Auth::user()->id;
        $learning_area->save(); 

        return $learning_area;
    }
}