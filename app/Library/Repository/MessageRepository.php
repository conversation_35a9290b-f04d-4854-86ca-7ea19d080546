<?php

namespace App\Library\Repository;

use App\Models\Chat;
use App\Models\User;
use App\Models\ChatUser;
use Illuminate\Support\Str;
use App\Models\ChatMessage;
use Illuminate\Support\Facades\DB;
use App\Library\Helper\AcademicHelper;
use App\Library\Repository\EmailRepository;
use App\Library\Repository\NotificationRepository;
use App\Library\Repository\MobileNotificationRepository;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Exception;



class MessageRepository
{

    protected AcademicHelper $ah;
    protected EmailRepository $email;
    protected NotificationRepository $notification;
    protected MobileNotificationRepository $mobileNotification;

    public function __construct(AcademicHelper $ah, EmailRepository $email, NotificationRepository $notification, MobileNotificationRepository $mobileNotification)
    {
        $this->ah = $ah;
        $this->email = $email;
        $this->notification = $notification;
        $this->mobileNotification = $mobileNotification;
    }

    public function createConversation($data)
    {
        $rd = '';
        DB::beginTransaction();

        try {
            $newChat = Chat::create([
                'chat_uuid'     => Str::uuid(),
                'chat_topic'    => $data['topic'],
                'created_by'    => \Auth::user()->id,
                'chat_status'   => 1
            ]);

            if ($newChat) {
                ChatUser::updateOrCreate([
                    'chat_id'   => $newChat->chat_id,
                    'user_id'   => \Auth::user()->id
                ]);

                foreach ($data['members'] as $member) {
                    ChatUser::updateOrCreate([
                        'chat_id'   => $newChat->chat_id,
                        'user_id'   => $member
                    ]);

                    if ($member != \Auth::user()->id) {
                        $this->notification->create([
                            'detail_id'             => $newChat->chat_id,
                            'description'           => $newChat->chat_uuid,
                            'user'                  => $member,
                            'type'                  => 'message',
                            'notification_content'  => \Auth::user()->name . ' created new conversation with you.'
                        ]);

                        $checkMemberStudent = User::where('id', $member)
                            ->where('user_type', 'student')
                            ->first();
                        if ($checkMemberStudent && $member != \Auth::user()->id) {
                            // $this->mobileNotification->sendSingle([
                            //     'title'             => 'New Message',
                            //     'message'           => \Auth::user()->name . ' created new conversation with ' . $checkMemberStudent->name . '.',
                            //     'student'           => $member,
                            //     'type'              => 'message',
                            //     'user_type'         => 'student',
                            // ]);
                            $this->mobileNotification->sendRealTime([
                                'title'             => 'New Message',
                                'message'           => \Auth::user()->name . ' created new conversation with ' . $checkMemberStudent->name . '.',
                                'student'           => $member,
                                'type'              => 'message',
                                'user_type'         => 'student',
                            ]);
                        }
                    }
                }
                $rd = $newChat->chat_uuid;
            }
            DB::commit();
        } catch (Exception $e) {
            DB::rollback();
            throw new Exception('Something bad happaned!');
        }

        return $rd;
    }

    public function send($data)
    {
        DB::beginTransaction();
        try {
            $chat = Chat::where('chat_uuid', $data['chat_uuid'])->first();

            ChatMessage::create([
                'chat_id'   => $chat->chat_id,
                'user_id'   => \Auth::user()->id,
                'message'   => $data['chat_message']
            ]);

            foreach ($chat->members as $member) {
                $this->notification->create([
                    'detail_id'             => $chat->chat_id,
                    'description'           => $chat->chat_uuid,
                    'user'                  => $member->user_id,
                    'type'                  => 'message',
                    'notification_content'  => \Auth::user()->name . ' sent message to <strong>' . $chat->chat_topic . '</strong> conversation.'
                ]);

                // Send mobile notification to all members (students and staff) except sender
                if ($member->user_id != \Auth::user()->id) {
                    $this->mobileNotification->sendRealTime([
                        'title'         => 'New Message',
                        'message'       => \Auth::user()->name . ' sent message to ' . $chat->chat_topic . ' conversation.',
                        'student'       => $member->user_id, // This field name is used for any user type
                        'type'          => 'message',
                        'user_type'     => $member->user->user_type,
                        'priority'      => 'normal',
                        'category'      => 'messaging'
                    ]);
                }
            }

            DB::commit();
        } catch (Exception $e) {
            DB::rollback();
            throw new Exception($e->getMessage());
        }
    }

    public function deleteConversation($uuid)
    {
        $chat = Chat::where('chat_uuid', $uuid)
            ->where('created_by', \Auth::user()->id)
            ->first();

        $chat->chat_status = 0;
        $chat->save();
    }

    public function leaveConversation($uuid)
    {
        $chat = Chat::where('chat_uuid', $uuid)
            ->first();

        ChatUser::where('chat_id', $chat->chat_id)
            ->where('user_id', \Auth::user()->id)
            ->delete();

        foreach ($chat->members as $member) {
            $this->notification->create([
                'detail_id'             => $chat->chat_id,
                'description'           => $chat->chat_uuid,
                'user'                  => $member->user_id,
                'type'                  => 'message',
                'notification_content'  => \Auth::user()->name . ' left conversation <strong>' . $chat->chat_topic . '</strong>.'
            ]);
        }
    }
}
