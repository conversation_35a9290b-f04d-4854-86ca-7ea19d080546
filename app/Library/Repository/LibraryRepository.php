<?php

namespace App\Library\Repository;

use Illuminate\Support\Facades\DB;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use App\Library\Helper\AcademicHelper;
use App\Models\LibraryAuthor;
use App\Models\LibraryBook;
use App\Models\LibraryCategory;
use App\Models\LibraryEbook;
use App\Models\LibraryIssue;
use App\Models\LibraryType;
use App\Models\User;
use Carbon\Carbon;
use DataTables;
use Exception;
use Illuminate\Validation\ValidationException;
use <PERSON><PERSON><PERSON>\SimpleXLSX;

class LibraryRepository
{

    protected AcademicHelper $ah;
    protected MobileNotificationRepository $mobileNotification;

    public function __construct(AcademicHelper $ah, MobileNotificationRepository $mobileNotification)
    {
        $this->ah = $ah;
        $this->mobileNotification = $mobileNotification;
    }
    public function returnBook($issueId)
    {
        $issue = LibraryIssue::find($issueId);
        $issue->is_returned = 1;
        $issue->return_date = date("Y-m-d");
        $issue->save();
    }

    public function processBookList()
    {
        $books = LibraryBook::where('branch_id', $this->ah->currentBranch())
            ->where('status', 1)
            ->get();
        return Datatables::of($books)->addIndexColumn()
            ->addColumn('book_id', function ($row) {
                return $row->book_id;
            })
            ->addColumn('title', function ($row) {
                return $row->title;
            })
            ->addColumn('barcode', function ($row) {
                return $row->barcode;
            })
            ->addColumn('language', function ($row) {
                return $row->language;
            })
            ->addColumn('year', function ($row) {
                return $row->publication_year;
            })
            ->addColumn('publisher', function ($row) {
                return $row->publisher;
            })
            ->addColumn('isbn', function ($row) {
                return $row->isbn;
            })
            ->rawColumns(['title', 'category', 'barcode', 'language', 'year', 'publisher', 'isbn'])
            ->make(true);
    }

    public function storeBook($data)
    {
        foreach ($data['barcode'] as $key => $barcode) {
            if ($barcode != '') {
                $book = LibraryBook::updateOrCreate(
                    [
                        'book_id' => $data['book_id']
                    ],
                    [
                        'branch_id'                         => $this->ah->currentBranch(),
                        'publication_type_id'               => $data['type'],
                        'barcode'                           => $barcode,
                        'title'                             => $data['title'],
                        'page_count'                        => $data['pages'],
                        'author_id'                         => $data['author'],
                        'corporate_author_id'               => $data['corporate_author'] ?? 0,
                        'category_id'                       => $data['category'] ?? null,
                        'status'                            => 1,
                        'language'                          => $data['language'],
                        'location'                          => $data['location'],
                        'publication_year'                  => $data['year'],
                        'publisher'                         => $data['publisher'],
                        'isbn'                              => $data['isbn'],
                        'is_allowed_outside_library'        => $data['is_allowed'],
                        'place'                             => $data['place'],
                        'physical_description'              => $data['physical_description'],
                        'series'                            => $data['series'],
                        'notes'                             => $data['notes'],
                        'subject_heading'                   => $data['subject_heading'],
                        'number_of_copies'                  => $data['number_of_copies'],
                        'keywords'                          => $data['keywords'],
                        'call_number'                       => $data['call_number'],
                        'summary'                           => $data['summary'],
                        'reading_level'                     => $data['reading_level'],
                        'price'                             => $data['price'],
                    ]
                );

                if (isset($data['cover_photo'])) {
                    $uid = uniqid('', true);
                    $filePath = "data/library/" . $this->ah->currentBranch() . "/book_covers";

                    if (!\File::exists($filePath)) {
                        $result = \File::makeDirectory($filePath, 0755, true);
                    }

                    $photo = $data['cover_photo'];
                    $extension = $photo->getClientOriginalExtension();
                    $photoName = $filePath . "/cover_photo_" . $uid . "." . $extension;
                    $photo->move($filePath, "/cover_photo_" . $uid . "." . $extension);

                    $book->cover_photo = "/" . $photoName;
                    $book->save();
                }
            }
        }
    }

    public function deleteBook($bookId)
    {
        $book = LibraryBook::find($bookId);
        $issue = LibraryIssue::where('book_id', $bookId)
            ->where('is_returned', 0)
            ->first();
        if ($issue) {
            throw new \ErrorException('You can not delete issued book!');
        } else {
            $book->status = 0;
            $book->save();
        }
    }
    public function deleteBulkBook($request)
    {
        $books = LibraryBook::whereIn('book_id', $request->bookIds)->get();
        // return $books;
        DB::beginTransaction();
        try {
            foreach ($books as $key => $book) {
                if ($book != '') {
                    $issue = LibraryIssue::where('book_id', $book->book_id)
                        ->where('is_returned', 0)
                        ->first();
                    if ($issue) {
                        throw new \ErrorException('You can not delete issued book!');
                    } else {
                        $book->status = 0;
                        $book->save();
                    }
                }
            }
            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }
    public function deleteBulkAuthor($request)
    {
        $authors = LibraryAuthor::whereIn('author_id', $request->authorIds)->get();
        DB::beginTransaction();
        foreach ($authors as $author) {
            try {
                $authorBooks = $author->books->pluck('book_id')->toArray();;
                $books = LibraryBook::whereIn('book_id', $authorBooks)->get();
                foreach ($books as $key => $book) {
                    if ($book != '') {
                        $issue = LibraryIssue::where('book_id', $book->book_id)
                            ->where('is_returned', 0)
                            ->first();
                        if ($issue) {
                            throw new \ErrorException('You can not delete issued book!');
                        }
                    }
                }
                LibraryBook::where('author_id', $author->author_id)->delete();
                $author->delete();
                DB::commit();
            } catch (Exception $e) {
                DB::rollBack();
                throw $e;
            }
        }
    }

    public function createAuthor($name)
    {
        LibraryAuthor::create([
            'branch_id'     => $this->ah->currentBranch(),
            'author_name'   => $name
        ]);
    }

    public function deleteAuthor($id)
    {
        $author = LibraryAuthor::find($id);
        if ($author->books->count() > 0) {
            $books = '<table class="table table-hover gs-7">';
            foreach ($author->books as $key => $book) {
                $books .= '<tr><td style="text-align: left">' . $book->title . '</td><td>' . $book->barcode . '</td></tr>';
            }
            $books .= '</table>';
            throw new \ErrorException('You can not delete this author because of assigned books!<br/><hr/>' . $books);
        } else {
            LibraryBook::where('author_id', $id)->delete();
            $author->delete();
        }
    }

    public function createIssue($data)
    {
        $issuableCount = 2;
        $book = LibraryBook::where('book_id', $data['bookId'])->first();
        $user = User::where('id', $data['userId'])
            ->where('user_status', 1)
            // ->where('user_type', 'staff')
            ->first();
        $issueBookCount = LibraryIssue::where('user_id', $data['userId'])
            // ->where('should_return_date', '>', Carbon::now())
            ->where('is_returned', 0)
            ->where('issue_type', $data['issueType'])
            ->get()
            ->count();
        $issueBook = LibraryIssue::where('book_id', $data['bookId'])
            ->where('is_returned', 0)
            ->get();
        // dd($issueBook);
        // return $issueBookCount;
        if ($data['issueType'] == 'classroom') {
            $issuableCount = 25;
        } else if ($user->user_type == 'staff') {
            $issuableCount = 5;
        }
        if (isset($book->category) && $book->category->category_name == "Teacher's Guide/Resource" &&  $user->user_type != 'staff') {
            throw new \ErrorException("You cannot Issue This because This is Only For Teacher and Staff!<br/><hr/>");
        } else if ($issueBookCount < $issuableCount) {
            if (count($issueBook) < 1) {
                LibraryIssue::create([
                    'branch_id'          => $this->ah->currentBranch(),
                    'user_id'            => $data['userId'],
                    'book_id'            => $data['bookId'],
                    'issue_date'         => date("Y-m-d H:i:s"),
                    'should_return_date' => $data['returnDate'],
                    'is_returned'        => 0,
                    'issue_type'         => $data['issueType'],
                ]);
            }
            // $msj ="The Following Books have been issued to ". $user['name']." | (".$book['title'].") by (".$book['author']['author_name'].") | Due Date : (".$data['returnDate'].")";
            // // echo $msj;
            // // return 0;
            // $this->mobileNotification->sendSingle([
            //     'title'     => 'Library',
            //     'message'   => $msj,
            //     'student'   =>$data['userId'],
            //     'type'      => 'library'
            // ]);
            if($user->user_type == 'student') {
            $msj ="The Following Books have been issued to ". $user['name']." | (".$book['title'].") by (".$book['author']['author_name'].") | Due Date : (".$data['returnDate'].")";
            // echo $msj;
            // return 0;
            $this->mobileNotification->sendRealTime([
                'title'     => 'Library',
                'message'   => $msj,
                'student'   =>$data['userId'],
                'type'      => 'library',
                'user_type'      => 'student'
            ]);
        }
        } else {
            throw new \ErrorException("You cannot Issue the Book because the User's Issue Limit has been Exceeded!<br/><hr/>");
        }
    }

    public function processBulkBookFile($excelFile)
    {
        $rd = '';

        if (isset($excelFile)) {
            $filePath = "data/library/tempUploads/" . $this->ah->currentBranch() . "_" . \Auth::user()->id . "/";
            if (!\File::exists($filePath)) {
                $result = \File::makeDirectory($filePath, 0755, true);
            }

            $uid = uniqid('', true);

            // $file = $excelFile;
            $extension = $excelFile->getClientOriginalExtension();
            $fileName = $filePath . "/file_" . $uid . "." . $extension;
            $excelFile->move($filePath, "/file_" . $uid . "." . $extension);

            $totalRows = 0;

            if ($xlsx = SimpleXLSX::parse($fileName)) {
                foreach ($xlsx->rows() as $r => $row) {
                    if ($totalRows > 0) {
                        $counter = 0;
                        $rd .= '<tr class="libraryBookRow">';
                        foreach ($row as $c => $cell) {
                            if ($counter == 1) {
                                $rd .= '<td class="libraryIsbn">' . $cell . '</td>';
                            } else if ($counter == 2) {
                                $rd .= '<td class="libraryCallNumber">' . $cell . '</td>';
                            } else if ($counter == 3) {
                                $rd .= '<td class="libraryAuthor">' . $cell . '</td>';
                            } else if ($counter == 4) {
                                $rd .= '<td class="libraryCorporateAuthor">' . $cell . '</td>';
                            } else if ($counter == 5) {
                                $rd .= '<td class="libraryTitle">' . $cell . '</td>';
                            } else if ($counter == 6) {
                                $rd .= '<td class="libraryPlace">' . $cell . '</td>';
                            } else if ($counter == 7) {
                                $rd .= '<td class="libraryPublisher">' . $cell . '</td>';
                            } else if ($counter == 8) {
                                $rd .= '<td class="libraryPublishYear">' . $cell . '</td>';
                            } else if ($counter == 9) {
                                $rd .= '<td class="libraryPages">' . $cell . '</td>';
                            } else if ($counter == 10) {
                                $rd .= '<td class="libraryDimension">' . $cell . '</td>';
                            } else if ($counter == 11) {
                                $rd .= '<td class="librarySeries">' . $cell . '</td>';
                                // } else if($counter == 11) {
                                // $rd .= '<td class="libraryReadingSeries">'.$cell.'</td>';
                            } else if ($counter == 12) {
                                $rd .= '<td class="libraryReadingLevel">' . $cell . '</td>';
                            } else if ($counter == 13) {
                                $rd .= '<td class="libraryNote">' . $cell . '</td>';
                            } else if ($counter == 14) {
                                $rd .= '<td class="librarySummary">' . $cell . '</td>';
                            } else if ($counter == 15) {
                                $rd .= '<td class="librarySubjectHeading">' . $cell . '</td>';
                            } else if ($counter == 16) {
                                $rd .= '<td class="libraryLocation">' . $cell . '</td>';
                            } else if ($counter == 17) {
                                $rd .= '<td class="libraryNumberOfCopies">' . $cell . '</td>';
                            } else if ($counter == 18) {
                                $rd .= '<td class="libraryKeywords">' . $cell . '</td>';
                            } else if ($counter == 19) {
                                $rd .= '<td class="libraryBarcode">' . (string)$cell . '</td>';
                            } else if ($counter == 20) {
                                $rd .= '<td class="libraryType">' . $cell . '</td>';
                            } else if ($counter == 21) {
                                $rd .= '<td class="libraryCategory">' . $cell . '</td>';
                            } else if ($counter == 22) {
                                $rd .= '<td class="libraryLanguage">' . $cell . '</td>';
                            } else if ($counter == 23) {
                                $rd .= '<td class="libraryIsAllowedOutside">' . $cell . '</td>';
                            } else if ($counter == 24) {
                                $rd .= '<td class="libraryBookPrice">' . $cell . '</td>';
                                $rd .= '<td><a href="#" class="btnDeleteRow"><i class="fas fa-trash"></i></a></td>';
                            }
                            $counter++;
                        }
                        $rd .= '</tr>';
                    }
                    $totalRows++;
                }
            } else {
                echo SimpleXLSX::parseError();
            }
        }
        return $rd;
    }
    public function processBulkAuthorFile($excelFile)
    {
        $rd = '';

        if (isset($excelFile)) {
            $filePath = "data/library/tempUploads/" . $this->ah->currentBranch() . "_" . \Auth::user()->id . "/";
            if (!\File::exists($filePath)) {
                $result = \File::makeDirectory($filePath, 0755, true);
            }

            $uid = uniqid('', true);

            // $file = $excelFile;
            $extension = $excelFile->getClientOriginalExtension();
            $fileName = $filePath . "/file_" . $uid . "." . $extension;
            $excelFile->move($filePath, "/file_" . $uid . "." . $extension);

            $totalRows = 0;

            if ($xlsx = SimpleXLSX::parse($fileName)) {
                foreach ($xlsx->rows() as $r => $row) {
                    if ($totalRows > 0) {
                        $counter = 0;
                        $rd .= '<tr class="libraryAuthorRow">';
                        foreach ($row as $c => $cell) {
                            if ($counter == 0) {
                                $rd .= '<td class="libraryAuthorNo">' . $cell . '</td>';
                            } else if ($counter == 1) {
                                $rd .= '<td class="libraryAuthorName">' . $cell . '</td>';
                                $rd .= '<td><a href="#" class="btnDeleteRow"><i class="fas fa-trash"></i></a></td>';
                            }
                            $counter++;
                        }
                        $rd .= '</tr>';
                    }
                    $totalRows++;
                }
            } else {
                echo SimpleXLSX::parseError();
            }
        }
        return $rd;
    }
    public function storeBulkAuthor($request)
    {
        $arr = explode("|*|", $request['form_author_data']);
        // return response()->json($arr);
        DB::beginTransaction();
        try {
            foreach ($arr as $key => $item) {
                if ($item != '') {
                    // $temp = explode("/*/", $item);
                    // $catInfo = InventoryCategory::where('category_name', $temp[0])->first();

                    LibraryAuthor::create([
                        'branch_id'         => $this->ah->currentBranch(),
                        'author_name'    => $item,
                    ]);
                }
            }

            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }
    public function storeBulkBook($request)
    {
        $arr = explode("|*|", $request['form_book_data']);
        DB::beginTransaction();
        try {
            foreach ($arr as $key => $item) {
                if ($item != '') {
                    $temp = explode("/*/", $item);
                    if ($temp[19] == "") {
                        $temp[19] = "Book";
                    }
                    // $catInfo = InventoryCategory::where('category_name', $temp[0])->first();
                    $author = LibraryAuthor::select('author_id')->where('author_name', $temp[2])->first();
                    $corporate_author = LibraryAuthor::select('author_id')->where('author_name', $temp[3])->first();
                    $publication_type = LibraryType::select('publication_id')->where('publication_name', $temp[19])->first();
                    $category = LibraryCategory::select('category_id')->where('category_name', $temp[20])->first();
                    LibraryBook::create([
                        'branch_id'                         => $this->ah->currentBranch(),
                        'isbn'                              => $temp[0],
                        'call_number'                       => $temp[1],
                        'author_id'                         => $author ? $author->author_id : null,
                        'corporate_author_id'               => $corporate_author ? $corporate_author->author_id : 0,
                        'title'                             => $temp[4],
                        'place'                             => $temp[5],
                        'publisher'                         => $temp[6],
                        'publication_year'                  => $temp[7],
                        'page_count'                        => $temp[8],
                        'physical_description'              => $temp[9],
                        'series'                            => $temp[10],
                        'reading_level'                     => $temp[11],
                        'notes'                             => $temp[12],
                        'summary'                           => $temp[13],
                        'subject_heading'                   => $temp[14],
                        'location'                          => $temp[15],
                        'number_of_copies'                  => $temp[16],
                        'keywords'                          => $temp[17],
                        'barcode'                           => $temp[18],
                        'publication_type_id'               => $publication_type ? $publication_type->publication_id : null,
                        'category_id'                       => $category ? $category->category_id : null,
                        'language'                          => $temp[21],
                        'is_allowed_outside_library'        => ($temp[22] == ('Yes' || 'yes') || empty($temp[22]))  ? 1 : 0,
                        'price'                             => $temp[23],
                        'status'                            => 1,
                    ]);
                }
            }

            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    public function getAllBook($request)
    {
        $author = $request->author ?? null;
        $title = $request->title ?? null;
        $keyword = $request->keyword ?? null;
        $barcode_isbn = $request->barcode_isbn ?? null;
        $books = \DB::table('library_books')
            ->select(
                'library_books.*',
                'library_authors.*',
                'library_category.*',
                'library_publication_type.*',
                'latest_issue.issue_id',
                'latest_issue.issue_date',
                'latest_issue.is_returned',
                'latest_issue.should_return_date',
                DB::raw('(SELECT COUNT(*) FROM library_issues WHERE library_issues.book_id = library_books.book_id) as total_issues')
            )
            ->leftJoin('library_authors', 'library_authors.author_id', 'library_books.author_id')
            ->leftJoin('library_category', 'library_category.category_id', 'library_books.category_id')
            ->leftJoin('library_publication_type', 'library_publication_type.publication_id', 'library_books.publication_type_id')
            ->leftJoin(
                DB::raw('(SELECT * FROM library_issues li1
            WHERE li1.issue_id = (SELECT MAX(li2.issue_id) FROM library_issues li2
                            WHERE li2.book_id = li1.book_id)) latest_issue'),
                'latest_issue.book_id',
                '=',
                'library_books.book_id'
            )
            ->where('library_books.status', 1)
            ->where('library_books.branch_id', $this->ah->currentBranch())
            ->when($author, function ($query) use ($author) {
                return $query->where(DB::raw('UPPER(library_authors.author_name)'), 'like', '%' . strtoupper($author) . '%')->where('library_books.status', 1)->where('library_books.branch_id', $this->ah->currentBranch());
            })
            // filter with title
            ->when($title, function ($query) use ($title) {
                return $query->where(DB::raw('UPPER(library_books.title)'), 'like', '%' . strtoupper($title) . '%')->where('library_books.status', 1)->where('library_books.branch_id', $this->ah->currentBranch());
            })
            // filter with keywords
            ->when($keyword, function ($query) use ($keyword) {
                return $query->where(DB::raw('UPPER(library_books.keywords)'), 'like', '%' . strtoupper($keyword) . '%')->where('library_books.status', 1)->where('library_books.branch_id', $this->ah->currentBranch());
            })
            // filter with keywords
            ->when($barcode_isbn, function ($query) use ($barcode_isbn) {
                return $query->where('library_books.barcode', $barcode_isbn)->orWhere('library_books.isbn', $barcode_isbn)->where('library_books.status', 1)->where('library_books.branch_id', $this->ah->currentBranch());
            });
            // ->orderBy('book_id', 'desc')
            // ->get();
        // return $books;
        // dd($books[0]);
        $dt = Datatables::of($books)->addIndexColumn()
         ->orderColumn('total_issues', 'total_issues desc')
            ->addColumn('photo', function ($row) {
                return $row->cover_photo ? "<img src='" . $row->cover_photo . "' width='50' height='50' class=' user-photo-zoom rounded'>" : "<p width='50' height='50' class='text-center'>-</p>";
            })
            ->addColumn('title', function ($row) {
                return $row->title ?? '-';
            })
            ->addColumn('author', function ($row) {
                return $row->author_name ?? '-';
            })
            ->addColumn('subject_heading', function ($row) {
                return $row->subject_heading ?? '-';
            })
            ->addColumn('isbn', function ($row) {
                return $row->isbn ?? '-';
            })
            ->addColumn('call_number', function ($row) {
                return $row->call_number ?? '-';
            })
            ->addColumn('barcode', function ($row) {
                return $row->barcode ?? '-';
            })
            ->addColumn('total_issues', function ($row) {
                return $row->total_issues ?? '-';
            })
            ->addColumn('status', function ($row) {
                if ($row->issue_id) {
                    if ($row->issue_date != null) {
                        if ($row->is_returned == null) {
                            if ($row->should_return_date < Carbon::now()) {
                                return '<span style="color: rgba(149, 149, 149, 0.8); font-weight:bold;"> Overdue </span>';
                            }
                            return '<span style="color: rgba(223, 82, 67 , 0.8); font-weight:bold;"> Issued </span>';
                            // return 'Issued';
                        } else {
                            return '<span style="color: rgba(71, 169, 112, 0.8); font-weight:bold;"> Available </span>';
                        }
                    }else{
                        return '<span style="color: rgba(71, 169, 112, 0.8); font-weight:bold;"> Undefined </span>';
                    }
                } else {
                    return '<span style="color: rgba(71, 169, 112, 0.8); font-weight:bold;"> Available </span>';
                }
            })
            ->addColumn('book_id', function ($row) {
                return $row->book_id ?? '-';
            })
            ->addColumn('book_check_id', function ($row) {
                return $row->book_id ?? '-';
            })
            ->addColumn('price', function ($row) {
                return $row->price ?? '-';
            })
            ->rawColumns(['photo', 'status'])
            ->make(true);

        return $dt;
    }

    public function renewBook($issueId)
    {
        $issueBook = LibraryIssue::where('issue_id', $issueId)->first();
        if ($issueBook) {
            $should_return_date = Carbon::parse($issueBook->should_return_date);
            $issueBook->update([
                'should_return_date' => $should_return_date->addDays(14),
                'is_renewed' => 1
            ]);
        }
        return $issueBook;
    }
    public function showAllBook($request)
    {
        $academicHelper = new AcademicHelper();
        $issues = LibraryIssue::selectRaw('library_issues.book_id, COUNT(*) as total')
            ->join('library_books', 'library_issues.book_id', '=', 'library_books.book_id')
            ->where('library_books.status', 1)
            ->where('library_issues.branch_id', $academicHelper->currentBranch())
            ->groupBy('library_issues.book_id')
            ->orderByDesc('total')
            ->take(3)
            ->get();
        $topBookIds = $issues->pluck('book_id')->toArray();
        $topBooks = LibraryBook::leftJoin('library_authors', 'library_authors.author_id', 'library_books.author_id')
            ->whereIn('book_id', $topBookIds)
            ->get();
        return view('partial_view.app.library.all_book', [
            'topBooks' => $topBooks
        ]);
    }
    public function showOverdueStudent($request)
    {
        $today = Carbon::today();
        $student = $request->student ?? null;
        $classList = $request->classList ?? [];
        $books = LibraryIssue::selectRaw('library_issues.*, library_books.*, users.name as user_name, students_classroom.classroom_id, classrooms.*, DATEDIFF(?, should_return_date) as total_overdue', [$today])
            ->leftJoin('library_books', 'library_issues.book_id', '=', 'library_books.book_id')
            ->leftJoin('users', 'library_issues.user_id', '=', 'users.id')
            // ->leftJoin('students_classroom', 'library_issues.user_id', '=', 'students_classroom.student_id')
            ->leftJoin('students_classroom', function ($join) {
                $join->on('students_classroom.student_id', '=', 'users.id')
                    ->where('students_classroom.academic_year_id', $this->ah->academicYear());
            })
            ->leftJoin('classrooms', 'students_classroom.classroom_id', '=', 'classrooms.classroom_id')
            ->whereRaw('should_return_date < ? AND is_returned = 0', [$today])
            ->where('library_books.status', 1)
            ->where('library_books.branch_id', $this->ah->currentBranch())
            // ->where('users.user_type', 'student')
            ->when(count($classList) > 0, function ($query) use ($classList) {
                return $query->where('students_classroom.academic_year_id', $this->ah->academicYear())->whereIn('classrooms.classroom_id', $classList);
            })
            ->when($student, function ($query) use ($student) {
                return $query->where('users.id', $student);
            })
            ->orderBy('users.id')
            ->get();
        // return $books;
        $dt = Datatables::of($books)->addIndexColumn()
            ->addColumn('name', function ($row) {
                return $row->user_name;
            })
            ->addColumn('classroom_name', function ($row) {
                return $row->classroom_name ?? ' - ';
            })
            ->addColumn('title', function ($row) {
                return $row->title;
            })
            ->addColumn('call_number', function ($row) {
                return $row->call_number;
            })
            ->addColumn('price', function ($row) {
                return $row->price;
            })
            ->addColumn('should_return_date', function ($row) {
                return $row->should_return_date;
            })
            ->addColumn('total_overdue', function ($row) {
                return $row->total_overdue;
            })
            ->make(true);

        return $dt;
    }

    public function getHomeIssueBook()
    {
        $branchId = $this->ah->currentBranch();

        $issues = LibraryIssue::with('book', 'user')->select('library_issues.*', 'student_information.name', 'classrooms.classroom_name')
            ->leftJoin('student_information', 'student_information.id', 'library_issues.user_id')
            ->leftJoin('students_classroom', function ($join) {
                $join->on('students_classroom.student_id', '=', 'student_information.id')
                    ->where('students_classroom.academic_year_id', $this->ah->academicYear());
            })
            ->leftJoin('classrooms', 'classrooms.classroom_id', 'students_classroom.classroom_id')
            ->where('library_issues.branch_id', $branchId)
            ->where('library_issues.is_returned', 0)
            ->orderBy('library_issues.created_at', 'desc')
            ->get();
        // return $issues;
        $dt = Datatables::of($issues)->addIndexColumn()
            ->addColumn('issue_id', function ($row) {
                return $row->issue_id ?? '-';
            })
            ->addColumn('user_id', function ($row) {
                return $row->user_id ?? '-';
            })
            ->addColumn('user', function ($row) {
                return $row->user->name ?? '-';
            })
            ->addColumn('year', function ($row) {
                return $row->classroom_name ?? '-';
            })
            ->addColumn('book', function ($row) {
                return $row->book->title ?? '-';
            })
            ->addColumn('barcode', function ($row) {
                return $row->book->barcode ?? '-';
            })
            ->addColumn('issue_date', function ($row) {
                return $row->issue_date ?? '-';
            })
            ->addColumn('status', function ($row) {
                $shouldReturnDate = $row->should_return_date;
                $status = "";

                $dayCount = strtotime($shouldReturnDate) - strtotime(date("Y-m-d"));
                $dayCount = $dayCount / 86400;

                if (strtotime($shouldReturnDate) < strtotime(date("Y-m-d"))) {
                    $dayCount = $dayCount * -1;
                    $status = "DELAYED " . $dayCount . " days";
                } else {
                    $status = $dayCount . " days remain";
                }
                return $status;
            })
            ->addColumn('issue_type', function ($row) {
                return $row->issue_type;
            })
            ->addColumn('action', function ($row) {
                // get only latest issue book
                if ($row->is_renewed == 0) {

                    return '<i class="fa-solid fa-rotate-right renewIssueBook"
            style="color: rgba(71, 169, 112);" title="Renew Issue Book"></i>';
                } else {
                    return '<i class="fa-solid fa-ban" style="color: rgba(149, 149, 149);"
            title="Cannot Renew Book"></i>';
                }
            })
            ->rawColumns(['action'])
            ->setRowClass(function ($row) {
                $shouldReturnDate = $row->should_return_date;
                $rowStyle = "";
                $dayCount = strtotime($shouldReturnDate) - strtotime(date("Y-m-d"));
                $dayCount = $dayCount / 86400;

                if (strtotime($shouldReturnDate) < strtotime(date("Y-m-d"))) {
                    $dayCount = $dayCount * -1;
                    $rowStyle = " activeRow ";
                }
                return $rowStyle;
            })
            ->make(true);

        return $dt;
    }
    public function deleteCover($book_id)
    {
        $book = LibraryBook::find($book_id);
        $photo = $book->cover_photo;
        if (\File::exists(public_path($photo))) {
            \File::delete(public_path($photo));
            $book->cover_photo = null;
            $book->save();
        }
    }
}

