<?php

namespace App\Library\Repository;

use Illuminate\Support\Facades\DB;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use App\Library\Helper\AcademicHelper;
use App\Models\ElectiveGrade;
use App\Models\LessonPlan;
use Illuminate\Support\Str;
use App\Models\Homework;
use App\Models\HomeworkDetail;
use Exception;
use Illuminate\Support\Facades\File;
use App\Library\Repository\EmailRepository;
use App\Library\Repository\NotificationRepository;
use App\Models\User;
use Carbon\Carbon;

class HomeworkRepository
{

    protected AcademicHelper $ah;
    protected EmailRepository $email;
    protected NotificationRepository $notification;
    protected MobileNotificationRepository $mobileNotification;

    public function __construct(AcademicHelper $ah, EmailRepository $email, NotificationRepository $notification, MobileNotificationRepository $mobileNotification)
    {
        $this->ah = $ah;
        $this->email = $email;
        $this->notification = $notification;
        $this->mobileNotification = $mobileNotification;
    }

    public function getGradeDetails($gradeId)
    {
        $rd = '';
        $grade = ElectiveGrade::find($gradeId);

        foreach ($grade->students as $student) {
            if (isset($student->student->name)) {
                $rd .= '<option selected value="' . $student->student_id . '">' . $student->student_id . ' ' . $student->student->name . '</option>';
            }
        }
        $rd .= '|*|';

        /*$plans = LessonPlan::where('user_id', \Auth::user()->id)
            ->where('subject_id', $grade->subject_id)
            ->where('academic_year_id', $this->ah->academicYear())
            ->where('branch_id', $this->ah->currentBranch())
            ->get();

        foreach ($plans as $plan) {
            $rd .= '<option value="' . $plan->lesson_plan_id . '"><small>[' . $plan->classroom->classroom_name . ']</small> ' . $plan->lesson_plan_unit . '</option>';
        }*/

        return $rd;
    }

    public function deleteHomework($homework_id)
    {
        DB::beginTransaction();
        try {
            $homework = Homework::where('homework_id', $homework_id)->first();

            // foreach ($homework->details as $key => $detail) {
            //     if (File::exists(public_path($detail->reply_file))) {
            //         File::delete($detail->reply_file);
            //     }
            // }

            // if (File::exists(public_path($homework->homework_file))) {
            //     File::delete($homework->homework_file);
            // }

            $homework->details()->delete();
            $homework->delete();
            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();
            throw new \Exception($e->getMessage());
        }
    }

    public function updateStudentReply($data)
    {

        DB::beginTransaction();
        try {
            $homework = Homework::where('homework_uid', $data['uuid'])->first();
            $detail = HomeworkDetail::where('homework_id', $homework->homework_id)
                ->where('student_id', \Auth::user()->id)
                ->first();
            $fileName = null;
            // dd($data);
            // if (isset($data['homework_file'])) {
            //     $filePath = "data/homeworkReplyFiles";
            //     if (!\File::exists($filePath)) {
            //         $result = \File::makeDirectory($filePath, 0755, true);
            //     }

            //     $file = $data['homework_file'];
            //     $extension = $file->getClientOriginalExtension();
            //     $fileName = $filePath . "/hmr_" . $detail->uuid . "." . $extension;
            //     $file->move($filePath, "/hmw_" . $detail->uuid . "." . $extension);
            // }

            $detail->reply_file = $data['homework_file'];
            $detail->reply_data = $data['homework_video'];
            if ($data['homework_file'] != null || $data['homework_video'] != null) {
                $detail->is_completed = 1;
            }
            if ($detail->sumitted_date == null) {
                $detail->sumitted_date = Carbon::now();
            }
            $detail->save();

            // Send notification to teacher when student submits homework
            $teacher = User::find($homework->user_id);
            $student = User::find($detail->student_id);

            if ($teacher && $student) {
                //-- Create WEB notification -------
                $this->notification->create([
                    'detail_id'             => $homework->homework_id,
                    'description'           => $homework->homework_uid,
                    'user'                  => $homework->user_id,
                    'type'                  => 'homework',
                    'notification_content'  => 'Student ' . $student->name . ' has submitted homework: ' . $homework->title
                ]);

                //-- Create Mobile Notification -------
                // $this->mobileNotification->sendSingle([
                //     'academic_year'         => $this->ah->academicYear(),
                //     'title'                 => 'Homework Submitted',
                //     'message'               => 'Student ' . $student->name . ' has submitted homework: ' . $homework->title,
                //     'student'               => $homework->user_id,
                //     'type'                  => 'homework_submitted',
                //     'user_type'             => 'staff',
                // ]);
                $this->mobileNotification->sendRealTime([
                    'academic_year'         => $this->ah->academicYear(),
                    'title'                 => 'Homework Submitted',
                    'message'               => 'Student ' . $student->name . ' has submitted homework: ' . $homework->title,
                    'student'               => $homework->user_id,
                    'type'                  => 'homework_submitted',
                    'user_type'             => 'staff',
                ]);
            }

            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();
            throw new \Exception($e->getMessage());
        }
    }

    public function storeTeacherReply($data)
    {

        DB::beginTransaction();
        try {
            $detail = HomeworkDetail::where('detail_id', $data['detail_id'])->first();
            $detail->teacher_comment = $data['reply'];
            $detail->save();

            $userInfo = User::find($detail->student_id);
            //--  Create WEB notification -------
            $this->notification->create([
                'detail_id'             => $detail->homework_id,
                'description'           => $detail->uuid,
                'user'                  => $detail->student_id,
                'type'                  => 'homework',
                'notification_content'  => 'You have new teacher comment on your homework assigment :' . $detail->homework->title
            ]);

            //-- Create Mobile Notification -------
            // $this->mobileNotification->sendSingle([
            //     'academic_year'         => $this->ah->academicYear(),
            //     'title'                 => 'New Homework',
            //     'message'               => 'You have new teacher comment on your homework assigment :' . $detail->homework->title,
            //     'student'               => $detail->student_id,
            //     'type'                  => 'homework',
            //     'user_type'             => 'student',
            // ]);
            $this->mobileNotification->sendRealTime([
                'academic_year'         => $this->ah->academicYear(),
                'title'                 => 'Teacher Feedback',
                'message'               => 'You have new teacher comment on your homework assignment: ' . $detail->homework->title,
                'student'               => $detail->student_id,
                'type'                  => 'homework_feedback',
                'user_type'             => 'student',
                'priority'              => 'high',
            ]);

            //-- Create Email
            // $messageContent = 'You have new teacher comment on your homework assigment :' . $detail->homework->title;
            // '<br/><br/>' .
            //     'Please login to app to check homework!';

            // $this->email->sendEmail([
            //     'receiver' => $userInfo->email,
            //     'subject' => 'Teacher comment on homework',
            //     'content' => $messageContent,
            //     'view' => 'homework'
            // ]);

            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    public function createHomework($data)
    {
        // return $data;
        DB::beginTransaction();
        try {
            // $email = new EmailRepository();
            $grade = ElectiveGrade::find($data['grade']);
            $uuid = Str::uuid();

            $homework = Homework::create([
                'academic_year_id'          => $this->ah->academicYear(),
                'branch_id'                 => $this->ah->currentBranch(),
                'user_id'                   => \Auth::user()->id,
                'title'                     => $data['title'],
                'homework_uid'              => $uuid,
                'grade_id'                  => $data['grade'],
                'subject_id'                => $grade->subject_id,
                'homework_data'             => $data['homework'],
                'homework_files'            => $data['homework_file'],
                'homework_video_links'      => $data['homework_video'],
                'deadline'                  => $data['due_date'],
            ]);



            foreach ($data['students'] as $student) {
                $userInfo = User::find($student);

                HomeworkDetail::create([
                    'homework_id'   => $homework->homework_id,
                    'student_id'    => $student,
                    'uuid'          => Str::uuid()
                ]);

                //--  Create WEB notification -------
                $this->notification->create([
                    'detail_id'             => $homework->homework_id,
                    'description'           => $homework->homework_uid,
                    'user'                  => $student,
                    'type'                  => 'homework',
                    'notification_content'  => 'You have new homework assigment by teacher ' . \Auth::user()->name
                ]);

                //-- Create Mobile Notification -------
                // $this->mobileNotification->sendSingle([
                //     'academic_year'             => $this->ah->academicYear(),
                //     'title'                     => 'New Homework Assignment',
                //     'message'                   => 'Dear student, you have been assigned new homework: ' . $data['title'],
                //     'student'                   => $student,
                //     'type'                      => 'homework_assigned',
                //     'user_type'                 => 'student',
                // ]);
                $this->mobileNotification->sendRealTime([
                    'academic_year'             => $this->ah->academicYear(),
                    'title'                     => 'New Homework Assignment',
                    'message'                   => 'Dear student, you have been assigned new homework: ' . $data['title'],
                    'student'                   => $student,
                    'type'                      => 'homework_assigned',
                    'user_type'                 => 'student',
                ]);

                //-- Create Email
                // $messageContent = \Auth::user()->name . ' assigned new homework for you.<br>';
                // 'Title: ' . $data['title'] . '<br/><br/>' .
                //     'Please login to app to check homework!';

                // $this->email->sendEmail([
                //     'receiver' => $userInfo->email,
                //     'subject' => 'New Homework assigment',
                //     'content' => $messageContent,
                //     'view' => 'homework'
                // ]);
            }
            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    public function updateHomework($data)
    {
        // dd($data);
        DB::beginTransaction();
        try {
            $homework = Homework::find($data['homework_id']);
            $homework->title = $data['title'];
            $homework->homework_files = $data['homework_file'];
            $homework->homework_video_links = $data['homework_video'];
            $homework->deadline = $data['due_date'];
            $homework->homework_data = $data['homework'];
            $homework->save();
            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }
    public function storeHomeworkComplete($data)
    {
        $homework = HomeworkDetail::find($data['detailId']);
        $homework->is_completed = $data['checkboxValue'];
        $homework->save();
    }
}
