<?php

namespace App\Library\Repository;

use Illuminate\Support\Facades\DB;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use App\Models\ExcludedSubject;
use App\Models\BellTime;
use App\Models\LBCAttribute;
use App\Models\LBCPoint;
use App\Models\LBCType;
use App\Models\SkillsStrands;
use App\Models\SettingsAssessment;
use App\Library\Helper\AcademicHelper;
use App\Models\AcademicSemester;
use App\Models\FormativeAssessment;
use App\Models\SummativeAssessment;
use Exception;

class AcademicSettingsRepository
{

    public function addExcludedSubject($data)
    {
        $ah = new AcademicHelper();
        $academicYearId = $ah->academicYear();
        $branchId = $ah->currentBranch();

        foreach ($data['classrooms'] as $classroom) {
            $check = ExcludedSubject::where('academic_year_id', $academicYearId)
                ->where('branch_id', $branchId)
                ->where('grade_id', $classroom)
                ->where('subject_id', $data['subjectId'])
                ->first();

            if (!$check) {
                $excluded = new ExcludedSubject();
                $excluded->academic_year_id = $academicYearId;
                $excluded->branch_id        = $branchId;
                $excluded->grade_id         = $classroom;
                $excluded->subject_id       = $data['subjectId'];
                $excluded->save();
            }
        }
    }

    public function deleteExcludedSubject($data)
    {
        ExcludedSubject::where('as_id', $data['subjectId'])->delete();
    }

    public function saveBellTimes($data)
    {
        $ah = new AcademicHelper();
        $academicYearId = $ah->academicYear();
        $branchId = $ah->currentBranch();

        BellTime::where('branch_id', $branchId)->delete();

        $arr = explode("|*|", $data['bellData']);

        foreach ($arr as $key => $item) {
            if ($item != '') {
                $tempArr = explode("/*/", $item);

                $startTime = null;
                $endTime = null;

                if ($tempArr[2] != '' && $tempArr[2] != '00:00' && $tempArr[2] != '00:00:00') {
                    $startTime = $tempArr[2];
                } else {
                    $startTime = '';
                }

                if ($tempArr[3] != '' && $tempArr[2] != '00:00' && $tempArr[3] != '00:00:00') {
                    $endTime = $tempArr[3];
                } else {
                    $startTime = '';
                }

                $bell = new BellTime();
                $bell->branch_id = $branchId;
                $bell->day       = $tempArr[0];
                $bell->session   = $tempArr[1];
                $bell->start     = $startTime == '' ? NULL : $startTime;
                $bell->stop      = $endTime == '' ? NULL : $endTime;
                $bell->save();
            }
        }
    }

    public function saveLbcAtribute($data)
    {
        $atr = LBCAttribute::find($data['id']);
        $atr->attribute_title = $data['title'];
        $atr->attribute_info = $data['info'];
        $atr->save();
    }

    public function saveLbcPoint($data)
    {
        $point = LBCPoint::find($data['id']);
        $point->point_title = $data['title'];
        $point->point_score = $data['score'];
        $point->save();
    }

    public function saveLbcType($data)
    {
        $type = LBCType::find($data['id']);
        $type->type_title = $data['title'];
        $type->save();
    }

    public function addStrandSkill($data)
    {
        $ah = new AcademicHelper();
        $branchId = $ah->currentBranch();
        $ss = new SkillsStrands();

        $ss->branch_id      = $branchId;
        $ss->type           = $data['type'];
        $ss->value          = $data['value'];
        $ss->save();
    }

    public function deleteStrandSkill($data)
    {
        SkillsStrands::where('skill_strand_id', $data['id'])->delete();
    }

    public function assignStrandSkill($data)
    {
        foreach ($data['grades'] as $grade) {
            foreach ($data['strandSkills'] as $element) {
                $assignedSS = new SettingsAssessment();
                $assignedSS->term           = $data['term'];
                $assignedSS->grade_id           = $grade;
                $assignedSS->skill_strand_id    = $element;
                $assignedSS->save();
            }
        }
    }

    public function deleteAssignedStrandSkill($data)
    {
        SettingsAssessment::where('setting_id', $data['id'])->delete();
    }
    public function lockAllAssessments()
    {
        $ah = new AcademicHelper();
        $current_year = $ah->academicYear();
        $current_branch = $ah->currentBranch();
        $currentSemester = AcademicSemester::where('academic_semester', $ah->currentSemester())
            ->where('branch_id', $current_branch)
            ->where('academic_year_id', $ah->academicYear())
            ->first();
        $is_locked = 1;
        $summativeAssessment = SummativeAssessment::where('academic_year_id', $current_year)
            ->where('branch_id', $current_branch)
            ->orderBy('date', 'desc')
            ->first();
        $formativeAssessment = FormativeAssessment::where('academic_year_id', $current_year)
            ->where('branch_id', $current_branch)
            ->orderBy('date', 'desc')
            ->first();
        if ($summativeAssessment->is_locked == 1 || $formativeAssessment->is_locked == 1) {
            $is_locked = 0;
        }
        SummativeAssessment::where('academic_year_id', $current_year)
            ->where('branch_id', $current_branch)
            ->whereBetween('date', [$currentSemester->start_date, $currentSemester->end_date])
            ->update(['is_locked' => $is_locked]);
        FormativeAssessment::where('academic_year_id', $current_year)
            ->where('branch_id', $current_branch)
            ->whereBetween('date', [$currentSemester->start_date, $currentSemester->end_date])
            ->update(['is_locked' => $is_locked]);
    }

    public function lockAssessmentsByDate($data)
    {
        $ah = new AcademicHelper();
        $current_year = $ah->academicYear();
        $current_branch = $ah->currentBranch();
        $currentSemester = AcademicSemester::where('academic_semester', $ah->currentSemester())
            ->where('branch_id', $current_branch)
            ->where('academic_year_id', $ah->academicYear())
            ->first();
        if ($data['date'] == null) {
            throw new Exception('Date input cannot be null or invalid date format.');
        }
        SummativeAssessment::where('academic_year_id', $current_year)
            ->where('branch_id', $current_branch)
            ->whereBetween('date', [$currentSemester->start_date, $data['date']])
            ->update(['is_locked' => $data['is_locked']]);
        FormativeAssessment::where('academic_year_id', $current_year)
            ->where('branch_id', $current_branch)
            ->whereBetween('date', [$currentSemester->start_date, $data['date']])
            ->update(['is_locked' => $data['is_locked']]);
    }
    public function addFinalMidExamPercentage($data)
    {
        // dd($data);
        if ($data['branch_id'] != null) {
            if ($data['type'] == 'mid') {
                DB::table('settings')
                    ->where('setting_type', 'final_mid_term')
                    ->where('setting_value_4', $data['branch_id'])
                    ->update(['setting_value_2' => $data['value']]);
            } else {
                DB::table('settings')
                    ->where('setting_type', 'final_mid_term')
                    ->where('setting_value_4', $data['branch_id'])
                    ->update(['setting_value_3' => $data['value']]);
            }
        }
    }
}
