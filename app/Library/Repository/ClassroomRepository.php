<?php

namespace App\Library\Repository;

use App\Exports\StudentPasswordsExport;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use App\Models\Classroom;
use App\Models\StudentClassroom;
use App\Models\TeacherClassLesson;
use App\Library\Helper\AcademicHelper;
use App\Library\Helper\GeneralHelper;
use App\Models\User;
use Maatwebsite\Excel\Facades\Excel;

class ClassroomRepository
{

    protected AcademicHelper $ah;

    public function __construct(AcademicHelper $ah)
    {
        $this->ah = $ah;
    }

    public function create($data)
    {

        DB::beginTransaction();

        try {
            $branchId = $this->ah->currentBranch();
            $academicYearId = $this->ah->academicYear();

            $classroom = new Classroom();
            $classroom->academic_year_id = $academicYearId;
            $classroom->branch_id = $branchId;
            $classroom->classroom_name = $data['classroom_name'];
            $classroom->homeroom_teacher_id = $data['homeroom_teacher'];
            $classroom->status = 1;
            $classroom->save();

            if ($data['classroom_import'] == 1) {
                $oldClassInfo = StudentClassroom::where('academic_year_id', $data['classroom_import_year'])
                    ->where('classroom_id', $data['classroom_import_classroom'])
                    ->get();

                foreach ($oldClassInfo as $key => $student) {
                    StudentClassroom::updateOrCreate(
                        [
                            'student_id' => $student->student_id,
                            'classroom_id' => $classroom->classroom_id
                        ],
                        [
                            'academic_year_id' => $academicYearId,
                            'branch_id'        => $branchId
                        ]
                    );
                }
            }

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            throw new \Exception($e->getMessage());
        }
    }

    public function update($data)
    {
        $classroom = Classroom::find($data['classroom_id']);
        $classroom->classroom_name = $data['classroom_name'];
        $classroom->homeroom_teacher_id = $data['homeroom_teacher'];

        $filePath = "img/teacherSignatures/";
        $photo = $data['signature'];
        $extension = $photo->getClientOriginalExtension();
        $photoName = $data['homeroom_teacher'] . "." . $extension;
        $photo->move($filePath, "/" . $photoName);
        $classroom->save();
    }

    public function delete($classroomId)
    {
        $academicHelper = new AcademicHelper();
        $branchId = $this->ah->currentBranch();
        $academicYearId = $this->ah->academicYear();

        $classroomInfo = Classroom::where('classroom_id', $classroomId)->where('branch_id', $branchId)->first();

        if ($classroomInfo) {
            TeacherClassLesson::where('class_id', $classroomId)->delete();
            Classroom::where('classroom_id', $classroomId)->where('branch_id', $branchId)->delete();
            StudentClassroom::where('classroom_id', $classroomId)->delete();
        }
    }

    public function getStudents($classroomId)
    {
        $classroom = Classroom::find($classroomId);
        return $classroom->students;
    }

    public function getStudentsHtmlTable($classroomId)
    {
        $rd = '';
        $classroom = Classroom::find($classroomId);
        $students = $classroom->students;
        foreach ($classroom->students as $key => $student) {
            $rd .=  '<tr>' .
                '<td style="padding-left: 10px;">' . $student->student_id . '</td>' .
                '<td>' . $student->student->name ?? 'N/A' . '</td>' .
                // '<td><img src="'.$student->student->photo.'" style="width: 25px;" /></td>'.
                '<td style="text-align: right; padding-right: 10px;"><a href="#" class="btnDeleteStudent" data-id="' . $student->sc_id . '"><i class="fas fa-trash"></i></a></td>' .
                '</tr>';
        }
        if ($students->count() == 0) {
            $rd = '<tr><td>No Students</td><td></td><td></td></tr>';
        }
        return $rd;
    }

    public function addStudent($data)
    {
        $academicHelper = new AcademicHelper();
        $branchId = $this->ah->currentBranch();
        $academicYearId = $this->ah->academicYear();

        $classroomInfo = Classroom::where('classroom_id', $data['classroomId'])->where('branch_id', $branchId)->first();

        if ($classroomInfo) {
            foreach ($data['students'] as $student) {
                StudentClassroom::updateOrCreate(
                    [
                        'student_id' => $student,
                        'classroom_id' => $data['classroomId']
                    ],
                    [
                        'academic_year_id' => $academicYearId,
                        'branch_id'        => $branchId,
                        'student_id'       => $student
                    ]
                );
            }
        }
    }

    public function deleteStudent($data)
    {
        StudentClassroom::where('classroom_id', $data['classroomId'])->where('sc_id', $data['studentId'])->delete();
    }

    public function yearClassrooms($yearId)
    {
        $rd = '';
        $classrooms = Classroom::where('academic_year_id', $yearId)->where('branch_id', $this->ah->currentBranch())->get();

        foreach ($classrooms as $classroom) {
            $rd .= '<option value="' . $classroom->classroom_id . '">' . $classroom->classroom_name . '</option>';
        }
        return $rd;
    }
    public function resetPasswordClassroom($request)
    {
        $generalHelper = new GeneralHelper();
        $todayDate = date("Y-m-d");
        $class_students = StudentClassroom::leftJoin('users', 'users.id', 'students_classroom.student_id')
            ->leftJoin('student_attendance', function ($join) use ($todayDate) {
                $join->on('student_attendance.student_id', 'students_classroom.student_id')
                    ->where('student_attendance.date', $todayDate);
            })
            ->where('students_classroom.classroom_id', $request['classroomId'])
            ->where('users.user_status', 1)
            ->get();

        $data = collect();

        foreach ($class_students as $class_student) {
            $password = $generalHelper->generatePassword(8); // or Str::random(8);

            $student = User::find($class_student->id);
            $student->password = \Hash::make($password);
            $student->save();

            $data->push([
                'ID'     => $student->id,
                'Name'     => $student->name,
                'Classroom'     => $class_student->classroom->classroom_name,
                'Password' => $password,
            ]);
        }

        return Excel::download(new StudentPasswordsExport($data), 'student-passwords.xlsx');
    }
}
