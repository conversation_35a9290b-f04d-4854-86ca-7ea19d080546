<?php

namespace App\Library\Repository;

use App\Library\Helper\AcademicHelper;
use App\Library\Repository\MobileNotificationRepository;
use App\Models\AcademicYear;
use App\Models\Branch;
use App\Models\Classroom;
use App\Models\DetentionDutyList;
use App\Models\DetentionRecord;
use App\Models\DetentionTypeSetting;
use App\Models\DisciplineAwardRecord;
use App\Models\DisciplineAwardRemainingPoint;
use App\Models\DisciplineItem;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use App\Models\DisciplineRecord;
use App\Models\StudentClassroom;
use App\Models\StudentInformation;
use App\Models\UserRole;
use Carbon\Carbon;
use DataTables;
use Error;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class BpsRepository
{

    protected AcademicHelper $ah;
    protected MobileNotificationRepository $mobileNotification;

    public function __construct(AcademicHelper $ah, MobileNotificationRepository $mobileNotification)
    {
        $this->ah = $ah;
        $this->mobileNotification = $mobileNotification;
    }

    public function calculateBpsPoint($data, $student, $notiArray, $record)
    {
        // get last detention record
        $detentionRecord = DetentionRecord::where('student_id', $student)
            ->where('academic_year_id', $this->ah->academicYear())
            ->where('branch_id', $this->ah->currentBranch())
            // ->where('academic_semester',$this->ah->currentSemester())
            ->latest()->first();
        if ($detentionRecord == null) {
            // get discipline records for all semester
            $disciplineRecords = DisciplineRecord::with('item')
                ->where('student_id', $student)
                ->where('academic_year_id', $this->ah->academicYear())
                ->where('branch_id', $this->ah->currentBranch())
                ->where('item_type', 'dps')
                // ->where('academic_semester',$this->ah->currentSemester())
                ->get();
            // calculate total point
            $totalPoint = 0;
            foreach ($disciplineRecords as $disciplineRecord) {
                $totalPoint += $disciplineRecord->item->item_point;
            }
            if ($totalPoint <= -5) {
                //  intitialize count and dataArray
                $count = intdiv(abs($totalPoint), 5);
                $dataArray = [
                    'dps_record_id' => $record->discipline_record_id,
                    'student_id' => $student,
                    'academic_year_id' => $this->ah->academicYear(),
                    'branch_id' => $this->ah->currentBranch(),
                    'academic_semester' => $this->ah->currentSemester(),
                    'latest_point' => $totalPoint,
                    'date' => $data['date'],
                ];
                // Creating and Calculating new Detention Records
                $detention_type = DetentionTypeSetting::find(1);
                for ($i = 0; $i < $count; $i++) {
                    // Create LTD
                    $dataArray['detention_type'] =$detention_type->detention_type_setting_type;
                    $dataArray['system_note'] = $detention_type->detention_type_setting_full_title;
                    $dataArray['detention_type_id'] =$detention_type->detention_type_setting_id;
                    DetentionRecord::create($dataArray);
                    // $this->mobileNotification->sendSingle($notiArray);
                    $this->mobileNotification->sendBpsNotification($notiArray);

                    $detentionRecordCount = DetentionRecord::where('student_id', $student)
                        ->where('academic_year_id', $this->ah->academicYear())
                        ->where('branch_id', $this->ah->currentBranch())
                        // ->where('academic_semester',$this->ah->currentSemester())
                        ->where('detention_type_id', 1)
                        ->count();
                    // Create AS,Sd,OSSD
                    if ($detentionRecordCount >= 4) {
                        $quotient  = intdiv(abs($detentionRecordCount), 4);
                        $remainder = abs($detentionRecordCount) % 4;
                        if ($quotient % 3 == 1 && $remainder == 0) {
                            $other_detention_type = DetentionTypeSetting::find(2);
                            $dataArray['detention_type'] =$other_detention_type->detention_type_setting_type;
                            $dataArray['system_note'] = $other_detention_type->detention_type_setting_full_title;
                            $dataArray['detention_type_id'] =$other_detention_type->detention_type_setting_id;
                            DetentionRecord::create($dataArray);
                            // $this->mobileNotification->sendSingle($notiArray);
                            $this->mobileNotification->sendBpsNotification($notiArray);
                        }
                        if ($quotient % 3 == 2 && $remainder == 0) {
                            $other_detention_type = DetentionTypeSetting::find(3);
                            $dataArray['detention_type'] =$other_detention_type->detention_type_setting_type;
                            $dataArray['system_note'] = $other_detention_type->detention_type_setting_full_title;
                            $dataArray['detention_type_id'] =$other_detention_type->detention_type_setting_id;
                            DetentionRecord::create($dataArray);
                            // $this->mobileNotification->sendSingle($notiArray);
                            $this->mobileNotification->sendBpsNotification($notiArray);
                        }
                        if ($quotient % 3 == 0 && $remainder == 0) {
                            $other_detention_type = DetentionTypeSetting::find(4);
                            $dataArray['detention_type'] =$other_detention_type->detention_type_setting_type;
                            $dataArray['system_note'] = $other_detention_type->detention_type_setting_full_title;
                            $dataArray['detention_type_id'] =$other_detention_type->detention_type_setting_id;
                            DetentionRecord::create($dataArray);
                            // $this->mobileNotification->sendSingle($notiArray);
                            $this->mobileNotification->sendBpsNotification($notiArray);
                        }
                    }
                }
            }
        } else {
            // Calculate remaining point
            $remainingPoints = $detentionRecord->latest_point % 5;
            $totalPointForCalculation = 0;
            $totalPoint =  $detentionRecord->latest_point;
            // get Discipline Records
            $disciplineRecords = DisciplineRecord::with('item')
                ->where('student_id', $student)
                ->where('academic_year_id', $this->ah->academicYear())
                ->where('branch_id', $this->ah->currentBranch())
                // ->where('academic_semester',$this->ah->currentSemester())
                ->where('item_type', 'dps')
                ->where('discipline_record_id', '>', $detentionRecord->dps_record_id)
                ->get();
            // Get Total Point and Calculation Point
            foreach ($disciplineRecords as $disciplineRecord) {
                $totalPointForCalculation += $disciplineRecord->item->item_point;
                $totalPoint += $disciplineRecord->item->item_point;
            }
            $totalPointForCalculation += $remainingPoints;
            // if Calculation point greater than 5 , do the job
            if ($totalPointForCalculation <= -5) {
                $count = intdiv(abs($totalPointForCalculation), 5);
                $dataArray = [
                    'dps_record_id' => $record->discipline_record_id,
                    'student_id' => $student,
                    'academic_year_id' => $this->ah->academicYear(),
                    'branch_id' => $this->ah->currentBranch(),
                    'academic_semester' => $this->ah->currentSemester(),
                    'latest_point' => $totalPoint,
                    'date' => $data['date'],
                ];
                // Create Records with loop
                $detention_type = DetentionTypeSetting::find(1);
                $dataArray['detention_type'] =$detention_type->detention_type_setting_type;
                $dataArray['system_note'] = $detention_type->detention_type_setting_full_title;  
                $dataArray['detention_type_id'] =$detention_type->detention_type_setting_id;              
                for ($i = 0; $i < $count; $i++) {
                    DetentionRecord::create($dataArray);
                    $detentionRecordCount = DetentionRecord::where('student_id', $student)
                        ->where('academic_year_id', $this->ah->academicYear())
                        ->where('branch_id', $this->ah->currentBranch())
                        // ->where('academic_semester',$this->ah->currentSemester())
                        ->where('detention_type_id', 1)
                        ->count();
                    // Check for detention type
                    if ($detentionRecordCount >= 4) {
                        $quotient  = intdiv(abs($detentionRecordCount), 4);
                        $remainder = abs($detentionRecordCount) % 4;
                        if ($quotient % 3 == 1 && $remainder == 0) {
                            $other_detention_type = DetentionTypeSetting::find(2);
                            $dataArray['detention_type'] =$other_detention_type->detention_type_setting_type;
                            $dataArray['system_note'] = $other_detention_type->detention_type_setting_full_title;    
                            $dataArray['detention_type_id'] =$other_detention_type->detention_type_setting_id;  
                            DetentionRecord::create($dataArray);
                        }
                        if ($quotient % 3 == 2 && $remainder == 0) {
                            $other_detention_type = DetentionTypeSetting::find(3);
                            $dataArray['detention_type'] =$other_detention_type->detention_type_setting_type;
                            $dataArray['system_note'] = $other_detention_type->detention_type_setting_full_title; 
                            $dataArray['detention_type_id'] =$other_detention_type->detention_type_setting_id;  
                            DetentionRecord::create($dataArray);
                        }
                        if ($quotient % 3 == 0 && $remainder == 0) {
                            $other_detention_type = DetentionTypeSetting::find(4);
                            $dataArray['detention_type'] =$other_detention_type->detention_type_setting_type;
                            $dataArray['system_note'] = $other_detention_type->detention_type_setting_full_title; 
                            $dataArray['detention_type_id'] =$other_detention_type->detention_type_setting_id;  
                            DetentionRecord::create($dataArray);
                        }
                    }
                }
            }
        }
    }

    public function storeBps($data)
    {
        $case = "";
        $caseType = $data['case_type'] == 1 ? 'dps' : 'prs';

        if ($data['dps_case']) {
            $case = $data['dps_case'];
        } else {
            $case = $data['prs_case'];
        }
        foreach ($data['student'] as $key => $student) {
            $record = new DisciplineRecord();
            $record->academic_year_id  = $this->ah->branchAcademicYear($data['branch_id']);
            $record->branch_id         = $data['branch_id'];
            $record->user_id           = \Auth::user()->id ?? $data['user_id'];
            $record->student_id        = $student;
            $record->item_type         = $caseType;
            $record->item_id           = $case;
            $record->date              = $data['date'];
            $record->note              = $data['note'];
            $record->save();
            $award_point_record = DisciplineAwardRemainingPoint::where('student_id', $student)->where('academic_year_id', $this->ah->branchAcademicYear($data['branch_id']))->first();
            if (!$award_point_record) {
                $new_award_point_record = new DisciplineAwardRemainingPoint();
                $new_award_point_record->academic_year_id           = $this->ah->branchAcademicYear($data['branch_id']);
                $new_award_point_record->academic_semester           = $this->ah->currentSemester();
                $new_award_point_record->branch_id                  = $data['branch_id'];
                $new_award_point_record->student_id                 = $student;
                $new_award_point_record->remaining_point             = $record->item->item_point;
                $new_award_point_record->save();
            } else {
                $new_point = $award_point_record->remaining_point +  $record->item->item_point;
                $award_point_record->update([
                    'remaining_point' => $new_point
                ]);
            }
            $userInfo = User::where('id', $student)
                ->where('user_status', 1)
                ->first();

            $notiArray = [
                'academic_year' => $this->ah->branchAcademicYear($data['branch_id']),
                'title'             => 'Behavior record',
                'message'           => $userInfo->name . ' got Behavior record at: ' . $data['date'],
                'student'           => $student,
                'type'              => 'behavior',
                'user_type'         => $userInfo->user_type,
                'student_id'        => $student,
                'user_id'           => \Auth::user()->id ?? $data['user_id'],
                'item_type'         => ($caseType =='dps' ? 'negative' :'positive'),
                'item_title'        => $record->item->item_title,
                'item_point'        => $record->item->item_point,
                'id'                => $record->discipline_record_id,
                'date'              => $data['date'],
            ];
            // $this->mobileNotification->sendSingle($notiArray);
            $this->mobileNotification->sendBpsNotification($notiArray);
            $this->calculateBpsPoint($data, $student, $notiArray, $record);
        }
    }

    public function deleteBps($bpsId)
    {
        $bps_record = DisciplineRecord::with('item')->find($bpsId);
        $award_point_record = DisciplineAwardRemainingPoint::where('student_id', $bps_record->student_id)->where('academic_year_id', $bps_record->academic_year_id)->first();
        $new_point = $award_point_record->remaining_point -  $bps_record->item->item_point;
        $award_point_record->update([
            'remaining_point' => $new_point
        ]);
        DisciplineRecord::destroy($bpsId);
    }

    public function deleteDetentionRecord($detentionId)
    {
        DetentionRecord::destroy($detentionId);
    }

    public function branchRecords($request)
    {
        $academicYearId = $this->ah->academicYear();
        $branchId = $this->ah->currentBranch();
        $today = Carbon::now();
        $currentSemester = null;
        // Get from Request --start
        $studentId = $request->student ?? null;
        $caseType = $request->caseType ?? null;
        $classList = $request->classList ?? [];
        $dateType = $request->dateType;
        // --end
        $startDate = $request->startDate ?? null;
        $endDate = $request->endDate ?? null;
        // Filter DateType
        if ($dateType == 'term') {
            $currentSemester = $this->ah->currentSemester();
        }
        if ($dateType == 'week') {
            $startDate = $today->startOfWeek()->toDateString();
            $endDate = $today->endOfWeek()->toDateString();
        }
        if ($dateType == 'month') {
            $startDate = $today->startOfMonth()->toDateString();
            $endDate = $today->endOfMonth()->toDateString();
        }
        if ($dateType == 'over-all') {
            $currentSemester = null;
        }

        $records = \DB::table('discipline_bps_records')
            ->select(
                'classrooms.classroom_name',
                'discipline_bps_records.student_id',
                'discipline_bps_records.item_type',
                'users.name',
                'students_detail.first_name',
                'students_detail.middle_name',
                'students_detail.last_name',
                'discipline_bps_items.item_title',
                'discipline_bps_items.item_point',
                'discipline_bps_records.date',
                'discipline_bps_records.note',
                'discipline_bps_records.discipline_record_id',
                'discipline_bps_records.academic_year_id',
                'discipline_bps_records.academic_semester',
                'discipline_bps_records.branch_id',
                'discipline_bps_records.item_point as record_item_point',
            )
            ->leftJoin('discipline_bps_items', 'discipline_bps_items.discipline_item_id', 'discipline_bps_records.item_id')
            ->leftJoin('users', 'users.id', 'discipline_bps_records.user_id')
            ->leftJoin('students_detail', 'students_detail.student_id', 'discipline_bps_records.student_id')
            ->leftJoin('students_classroom', function ($join) use ($academicYearId, $branchId) {
                $join->on('students_classroom.student_id', 'discipline_bps_records.student_id')
                    ->where('students_classroom.branch_id', $branchId)
                    ->where('students_classroom.academic_year_id', $academicYearId);
            })
            ->leftJoin('classrooms', 'classrooms.classroom_id', 'students_classroom.classroom_id')
            // filter with class ids
            ->when(count($classList) > 0, function ($query) use ($classList) {
                return $query->whereIn('classrooms.classroom_id', $classList);
            })
            // filter with student id
            ->when($studentId, function ($query) use ($studentId) {
                return $query->where('discipline_bps_records.student_id', $studentId);
            })
            // filter with Case Type
            ->when($caseType, function ($query) use ($caseType) {
                return $query->where('discipline_bps_records.item_type', $caseType);
            })
            // filter with semiester id
            ->when($currentSemester, function ($query) use ($currentSemester) {
                return $query->where('discipline_bps_records.academic_semester', $currentSemester);
            })
            // Filter with Date
            ->when($startDate && $endDate, function ($query) use ($startDate, $endDate) {
                return $query->whereBetween('discipline_bps_records.date', [$startDate, $endDate]);
            })
            ->where('discipline_bps_records.status', 1)
            ->where('discipline_bps_records.academic_year_id', $academicYearId)
            ->where('discipline_bps_records.branch_id', $branchId)
            ->where('users.user_status', 1)
            ->orderBy('discipline_bps_records.discipline_record_id', 'desc')
            ->get();

        $dt = Datatables::of($records)->addIndexColumn()
            ->addColumn('student_id', function ($row) {
                return $row->student_id;
            })
            ->addColumn('name', function ($row) {
                return $row->name ?? '';
            })
            ->addColumn('student_name', function ($row) {
                return $row->first_name . " " . $row->middle_name . " " . $row->last_name;
            })
            ->addColumn('classroom_name', function ($row) {
                return $row->classroom_name;
            })
            ->addColumn('item_type', function ($row) {
                return $row->item_type;
            })
            ->addColumn('item_title', function ($row) {
                if ($row->item_point > 0) {
                    $row->row_style = 'color: rgba(88, 214, 141, 0.8);';
                }
                if ($row->item_point <= 0) {
                    $row->row_style = 'color: rgba(223, 82, 67 , 0.9);';
                }
                return '<span style="' . $row->row_style . '">' . $row->item_title . '</span>';
            })
            ->addColumn('item_point', function ($row) {
                if ($row->item_point == 0) {
                    return $row->record_item_point;
                }
                return $row->item_point;
            })
            ->addColumn('discipline_record_id', function ($row) {
                return $row->discipline_record_id;
            })
            ->addColumn('note', function ($row) {
                return $row->note;
            })
            ->addColumn('date', function ($row) {
                return $row->date;
            })
            ->rawColumns(['item_title'])
            ->make(true);

        return $dt;
    }

    public function branchRecordTotal($request)
    {
        $academicYearId = $this->ah->academicYear();
        $branchId = $this->ah->currentBranch();
        $today = Carbon::now();
        $currentSemester = null;
        // Get from Request --start
        $studentId = $request->student ?? null;
        $classList = $request->classList ?? [];
        $dateType = $request->dateType;
        // --end
        $startDate = $request->startDate ?? null;
        $endDate = $request->endDate ?? null;
        // Filter DateType
        if ($dateType == 'term') {
            $currentSemester = $this->ah->currentSemester();
        }
        if ($dateType == 'week') {
            $startDate = $today->startOfWeek()->toDateString();
            $endDate = $today->endOfWeek()->toDateString();
        }
        if ($dateType == 'month') {
            $startDate = $today->startOfMonth()->toDateString();
            $endDate = $today->endOfMonth()->toDateString();
        }
        if ($dateType == 'over-all') {
            $currentSemester = null;
        }
        $records = \DB::table('discipline_bps_records')
            ->select(
                'classrooms.classroom_name',
                'discipline_bps_records.student_id',
                'discipline_bps_records.item_type',
                'users.name',
                'students_detail.first_name',
                'students_detail.middle_name',
                'students_detail.last_name',
                'discipline_bps_items.item_title',
                'discipline_bps_items.item_point',
                'discipline_bps_records.date',
                'discipline_bps_records.note',
                'discipline_bps_records.discipline_record_id',
                DB::raw('SUM(CASE WHEN discipline_bps_records.item_type = "prs" THEN discipline_bps_items.item_point ELSE 0 END) as total_prs_point'),
                DB::raw('SUM(CASE WHEN discipline_bps_records.item_type = "dps" THEN discipline_bps_items.item_point ELSE 0 END) as total_dps_point'),
                DB::raw('(SUM(CASE WHEN discipline_bps_records.item_type = "prs" THEN discipline_bps_items.item_point ELSE 0 END) + SUM(CASE WHEN discipline_bps_records.item_type = "dps" THEN discipline_bps_items.item_point ELSE 0 END)) as total_point'),
            )
            ->leftJoin('discipline_bps_items', 'discipline_bps_items.discipline_item_id', 'discipline_bps_records.item_id')
            ->leftJoin('users', 'users.id', 'discipline_bps_records.user_id')
            ->leftJoin('students_detail', 'students_detail.student_id', 'discipline_bps_records.student_id')
            ->leftJoin('students_classroom', function ($join) use ($academicYearId, $branchId) {
                $join->on('students_classroom.student_id', 'discipline_bps_records.student_id')
                    ->where('students_classroom.branch_id', $branchId)
                    ->where('students_classroom.academic_year_id', $academicYearId);
            })
            ->leftJoin('classrooms', 'classrooms.classroom_id', 'students_classroom.classroom_id')
            // filter with class ids
            ->when(count($classList) > 0, function ($query) use ($classList) {
                return $query->whereIn('classrooms.classroom_id', $classList);
            })
            // filter with student id
            ->when($studentId, function ($query) use ($studentId) {
                return $query->where('discipline_bps_records.student_id', $studentId);
            })
            // filter with semiester id
            ->when($currentSemester, function ($query) use ($currentSemester) {
                return $query->where('discipline_bps_records.academic_semester', $currentSemester);
            })
            // Filter with Date
            ->when($startDate && $endDate, function ($query) use ($startDate, $endDate) {
                return $query->whereBetween('discipline_bps_records.date', [$startDate, $endDate]);
            })
            ->where('discipline_bps_records.status', 1)
            ->where('discipline_bps_records.academic_year_id', $academicYearId)
            ->where('discipline_bps_records.branch_id', $branchId)
            ->where('users.user_status', 1)
            ->groupBy('discipline_bps_records.student_id')
            ->orderBy('total_dps_point', 'desc')
            ->get();
        foreach ($records as $record) {
            $record->row_style = '';
            if ($record->total_dps_point >= 0) {
                $record->row_style = 'background-color:rgba(88, 214, 141, 0.8)';
            }
            // gray
            if ($record->total_dps_point <= -5 && $record->total_dps_point > -20) {
                $record->row_style = 'background-color: rgba(180, 180, 180 , 0.8)';
            }
            // yellow
            if ($record->total_dps_point <= -20 && $record->total_dps_point > -40) {
                $record->row_style = 'background-color: rgba(248, 218, 98, 0.8)';
            }
            // orange
            if ($record->total_dps_point <= -40 && $record->total_dps_point > -60) {
                $record->row_style = 'background-color: rgba(254, 168, 72, 0.8)';
            }
            // red
            if ($record->total_dps_point <= -60) {
                $record->row_style = 'background-color: rgba(223, 82, 67 , 0.9);';
            }
        }
        $dt = Datatables::of($records)->addIndexColumn()
            ->addColumn('student_id', function ($row) {
                return $row->student_id;
            })
            ->addColumn('student_name', function ($row) {
                return $row->first_name . " " . $row->middle_name . " " . $row->last_name;
            })
            ->addColumn('classroom_name', function ($row) {
                return $row->classroom_name;
            })
            ->addColumn('total_prs_point', function ($row) {
                return $row->total_prs_point;
            })
            ->addColumn('total_dps_point', function ($row) {
                return $row->total_dps_point;
            })
            ->addColumn('total_point', function ($row) {
                return $row->total_point;
            })
            ->addColumn('discipline_record_id', function ($row) {
                return $row->discipline_record_id;
            })
            ->editColumn('DT_RowAttr', function ($row) {
                return ['style' => $row->row_style];
            })
            ->make(true);

        return $dt;
    }

    public function getList($request)
    {
        $academicYearId = $this->ah->academicYear();
        $branchId = $this->ah->currentBranch();
        $today = Carbon::now();
        $currentSemester = null;
        // Get from Request --start
        $studentId = $request->student ?? null;
        $caseType = $request->caseType ?? null;
        $classList = $request->classList ?? [];
        $dateType = $request->dateType;
        // --end
        $startDate = $request->startDate ?? null;
        $endDate = $request->endDate ?? null;
        // Filter DateType
        if ($dateType == 'term') {
            $currentSemester = $this->ah->currentSemester();
        }
        if ($dateType == 'week') {
            $startDate = $today->startOfWeek()->toDateString();
            $endDate = $today->endOfWeek()->toDateString();
        }
        if ($dateType == 'month') {
            $startDate = $today->startOfMonth()->toDateString();
            $endDate = $today->endOfMonth()->toDateString();
        }
        if ($dateType == 'over-all') {
            $currentSemester = null;
        }

        $records = \DB::table('discipline_bps_records')
            ->select(
                'classrooms.classroom_name',
                'discipline_bps_records.student_id',
                'discipline_bps_records.item_type',
                'users.name',
                'discipline_bps_items.item_title',
                'discipline_bps_items.item_point',
                'discipline_bps_records.date',
                'discipline_bps_records.note',
                'discipline_bps_records.discipline_record_id',
                'discipline_bps_records.item_point as record_item_point',
            )
            ->leftJoin('users', 'users.id', 'discipline_bps_records.student_id')
            ->leftJoin('discipline_bps_items', 'discipline_bps_items.discipline_item_id', 'discipline_bps_records.item_id')
            ->leftJoin('students_classroom', function ($join) use ($academicYearId, $branchId) {
                $join->on('students_classroom.student_id', 'discipline_bps_records.student_id')
                    ->where('students_classroom.branch_id', $branchId)
                    ->where('students_classroom.academic_year_id', $academicYearId);
            })
            ->leftJoin('classrooms', 'classrooms.classroom_id', 'students_classroom.classroom_id')
            ->where('discipline_bps_records.status', 1)
            ->where('discipline_bps_records.academic_year_id', $academicYearId)
            ->where('discipline_bps_records.branch_id', $branchId)
            ->where('discipline_bps_records.user_id', \Auth::user()->id)
            // filter with Case Type
            ->when($caseType, function ($query) use ($caseType) {
                return $query->where('discipline_bps_records.item_type', $caseType);
            })
            // filter with student id
            ->when($studentId, function ($query) use ($studentId) {
                return $query->where('discipline_bps_records.student_id', $studentId);
            })
            // filter with semiester id
            ->when($currentSemester, function ($query) use ($currentSemester) {
                return $query->where('discipline_bps_records.academic_semester', $currentSemester);
            })
            // Filter with Date
            ->when($startDate && $endDate, function ($query) use ($startDate, $endDate) {
                return $query->whereBetween('discipline_bps_records.date', [$startDate, $endDate]);
            })
            // filter with class ids
            ->when(count($classList) > 0, function ($query) use ($classList) {
                return $query->whereIn('classrooms.classroom_id', $classList);
            })
            ->where('users.user_status', 1)
            ->orderBy('discipline_bps_records.discipline_record_id', 'desc')
            ->get();
        // return $records;

        return Datatables::of($records)->addIndexColumn()
            ->addColumn('discipline_record_id', function ($row) {
                return $row->discipline_record_id;
            })
            ->addColumn('name', function ($row) {
                return $row->name;
            })
            ->addColumn('student_id', function ($row) {
                return $row->student_id;
            })
            ->addColumn('item_type', function ($row) {
                return $row->item_type;
            })
            ->addColumn('item_title', function ($row) {
                return $row->item_title;
            })
            ->addColumn('item_point', function ($row) {
                if ($row->item_point == 0) {
                    return $row->record_item_point;
                }
                return $row->item_point;
            })
            ->addColumn('date', function ($row) {
                return $row->date;
            })
            ->addColumn('note', function ($row) {
                return $row->note;
            })
            ->make(true);
    }

    public function detentionBps($request)
    {
        $disciplineRecord = DisciplineRecord::find($request->discipline_record_id);
        if ($disciplineRecord) {
            $disciplineRecord->update([
                'detention_type' => $request->detention_type
            ]);
        }
        return $disciplineRecord;
    }

    public function changeDetentionRecordType($request)
    {
        $detentionRecord = DetentionRecord::find($request->detention_record_id);
        // $detentionTypeSetting = DetentionTypeSetting::find($request->detention_type);
        if ($detentionRecord) {
            $detentionRecord->update([
                'served_detention_type' => $request->detention_type
            ]);
        }
        return $detentionRecord;
    }

    public function serveBps($request)
    {
        $detentionRecord = DetentionRecord::find($request->recordId);
        if ($detentionRecord) {
            $detentionRecord->update([
                'is_served' => 1,
                'served_user_id'=>Auth::user()->id
            ]);
        }
        return $detentionRecord;
    }

    public function addBpsDetail($request)
    {
        return view('partial_view.app.discipline.add-detention-report', [
            'id' => $request->id
        ]);
    }

    public function detentionRecords($request)
    {
        $academicYearId = $this->ah->academicYear();
        $branchId = $this->ah->currentBranch();
        $today = Carbon::now();
        $currentSemester = null;
        // Get from Request --start
        $studentId = $request->student ?? null;
        $caseType = $request->caseType ?? null;
        $detentionType = $request->detentionType ?? null;
        $classList = $request->classList ?? [];
        $dateType = $request->dateType;
        // --end
        $startDate = $request->startDate ?? null;
        $endDate = $request->endDate ?? null;
        // Filter DateType
        if ($dateType == 'term') {
            $currentSemester = $this->ah->currentSemester();
        }
        if ($dateType == 'week') {
            $startDate = $today->startOfWeek()->toDateString();
            $endDate = $today->endOfWeek()->toDateString();
        }
        if ($dateType == 'month') {
            $startDate = $today->startOfMonth()->toDateString();
            $endDate = $today->endOfMonth()->toDateString();
        }
        if ($dateType == 'over-all') {
            $currentSemester = null;
        }
        $records = \DB::table('detention_records')
            ->leftJoin('discipline_bps_records', 'detention_records.dps_record_id', '=', 'discipline_bps_records.discipline_record_id')
            ->leftJoin('discipline_bps_items', 'discipline_bps_items.discipline_item_id', '=', 'discipline_bps_records.item_id')
            ->leftJoin('students_classroom', function ($join) use ($academicYearId, $branchId) {
                $join->on('students_classroom.student_id', '=', 'detention_records.student_id')
                    ->where('students_classroom.branch_id', '=', $branchId)
                    ->where('students_classroom.academic_year_id', '=', $academicYearId);
            })
            ->leftJoin('classrooms', 'classrooms.classroom_id', '=', 'students_classroom.classroom_id')
            ->leftJoin('students_detail', 'students_detail.student_id', '=', 'detention_records.student_id')
            ->leftJoin('users', 'users.id', '=', 'detention_records.served_user_id')
            ->select(
                'classrooms.classroom_name',
                'detention_records.student_id',
                'students_detail.first_name',
                'students_detail.middle_name',
                'students_detail.last_name',
                'detention_records.latest_point',
                'detention_records.date',
                'detention_records.served_detention_type',
                'detention_records.is_served',
                'detention_records.system_note',
                'detention_records.academic_year_id',
                'detention_records.academic_semester',
                'detention_records.branch_id',
                'detention_records.detention_record_id',
                'users.name',
            )
            // filter with class ids
            ->when(count($classList) > 0, function ($query) use ($classList) {
                return $query->whereIn('classrooms.classroom_id', $classList);
            })
            // filter with student id
            ->when($studentId, function ($query) use ($studentId) {
                return $query->where('detention_records.student_id', $studentId);
            })
            // filter with Case Type
            ->when($detentionType, function ($query) use ($detentionType) {
                return $query->where('detention_records.served_detention_type', $detentionType);
            })
            // filter with semiester id
            ->when($currentSemester, function ($query) use ($currentSemester) {
                return $query->where('detention_records.academic_semester', $currentSemester);
            })
            // Filter with Date
            ->when($startDate && $endDate, function ($query) use ($startDate, $endDate) {
                return $query->whereBetween('detention_records.date', [$startDate, $endDate]);
            })
            ->where('detention_records.academic_year_id', $academicYearId)
            ->where('detention_records.branch_id', $branchId)
            // ->where('users.user_status', 1)
            ->orderBy('detention_records.detention_record_id', 'desc')
            ->get();
        // return $records;

        $dt = Datatables::of($records)->addIndexColumn()
            ->addColumn('student_id', function ($row) {
                return $row->student_id;
            })
            ->addColumn('student_name', function ($row) {
                return $row->first_name . " " . $row->middle_name . " " . $row->last_name;
            })
            ->addColumn('classroom_name', function ($row) {
                return $row->classroom_name;
            })
            ->addColumn('latest_point', function ($row) {
                return $row->latest_point;
            })
            ->addColumn('detention_record_id', function ($row) {
                return $row->detention_record_id;
            })
            ->addColumn('name', function ($row) {
                return $row->name;
            })
            ->addColumn('system_note', function ($row) {

                return '<span style="color: rgba(223, 82, 67 , 0.9);">' . $row->system_note . '</span>';
            })
            ->addColumn('date', function ($row) {
                return $row->date;
            })

            ->addColumn('served_detention_type', function ($row) {
                // Check If Vice Principal or Not
                $isVicePrincipal  = 'false';
                $vicePrincipalIds = [52, 69, 70, 71, 72];
                $userRoles = UserRole::where('user_id', Auth::user()->id)->get();
                $userRoleIds = collect($userRoles)->pluck('role_id')->toArray();
                $matchingRoles = collect($vicePrincipalIds)->intersect($userRoleIds);
                if (count($matchingRoles) > 0) {
                    $isVicePrincipal = 'true';
                }
                // $detentionTypeArray = ['BC', 'LTD', 'AS', 'SD', 'OSSD'];
                $detentionTypeSettings = DetentionTypeSetting::all();
                $dropdown = '<select class="form-control detentionDropdown" data-control="select2" id="caseTypeSearch">';
                $dropdown .= '<option value=" " disable></option>';
                // Creating Dropdown with selected and disabled
                // foreach ($detentionTypeArray as $data) {
                //     $selected = '';
                //     $disabled = '';
                //     if ($row->served_detention_type == $data) {
                //         $selected = 'selected';
                //     }
                //     if ($isVicePrincipal == 'false') {
                //         if ($data == 'AS' || $data == 'SD' || $data == 'OSSD') {
                //             $disabled = 'disabled';
                //         }
                //     }
                //     $dropdown .= '<option value="' . $data . '"' . $selected . ' ' . $disabled . '>' . $data . '</option>';
                // }
                    foreach ($detentionTypeSettings as $data) {
                    $selected = '';
                    $disabled = '';
                    if ($row->served_detention_type == $data->detention_type_setting_id) {
                        $selected = 'selected';
                    }
                    if ($isVicePrincipal == 'false') {
                        if ($data->detention_type_setting_id != 1) {
                            $disabled = 'disabled';
                        }
                    }
                    $dropdown .= '<option value="' . $data->detention_type_setting_id . '"' . $selected . ' ' . $disabled . '>' . $data->detention_type_setting_label . '</option>';
                }
                $dropdown .= '</select>';
                return $dropdown;
            })
            ->addColumn('served', function ($row) {
                return $row->is_served ? '<i class="fa-solid fa-check ms-2 text-success h5"></i>' : ' ';
            })
            ->rawColumns(['served_detention_type', 'served', 'system_note'])
            ->make(true);

        return $dt;
    }
    public function getBehaviourAnalysis($request)
    {
        if ($request->has('year_id') && $request->year_id !== null) {
            $academicYearId = $request->year_id;
        } else {
            $academicYearId = $this->ah->academicYear();
        }
        $academic_years = AcademicYear::all();
        $classes = Classroom::where('academic_year_id', $academicYearId)
            ->where('status', 1)
            ->get();
        $branches = Branch::where('branch_status', 1)
            ->get();
        // dd($request->all());
        return view('app.head_office.behaviour_analysis', compact('classes', 'branches', 'academic_years', 'academicYearId'));
    }
    public function getBehaviourAnalysisChart($request)
    {
        // Filter Data
        if ($request->has('year_id') && $request->year_id !== null) {
            $academicYearId = $request->year_id;
        } else {
            $academicYearId = $this->ah->academicYear();
        }
        $academic_years = AcademicYear::all();
        $classes = Classroom::where('academic_year_id', $academicYearId)
            ->where('status', 1)
            ->get();
        $branches = Branch::where('branch_status', 1)
            ->get();

        // Get Student Count
        $student_count = StudentClassroom::where('academic_year_id', $academicYearId);
        if ($request->has('classroom_ids') && $request->classroom_ids !== null) {
            $classRoomId = explode(',', $request->input('classroom_ids'));
            $student_count->whereIn('classroom_id', $classRoomId);
        }
        $student_count = $student_count->get()->groupBy('branch_id');
        // Get Records
        $query = DisciplineRecord::select('discipline_bps_records.*', 'branches.branch_name', 'branches.branch_description', 'discipline_bps_items.item_point', 'students_classroom.*', 'classrooms.classroom_name')
            ->join('branches', 'branches.branch_id', 'discipline_bps_records.branch_id')
            ->join('discipline_bps_items', 'discipline_bps_items.discipline_item_id', 'discipline_bps_records.item_id')
            ->join('students_classroom', function ($join) use ($academicYearId) {
                $join->on('students_classroom.student_id', '=', 'discipline_bps_records.student_id')
                    ->where('students_classroom.academic_year_id', '=', $academicYearId);
            })
            ->join('classrooms', 'classrooms.classroom_id', 'students_classroom.classroom_id')
            ->where('discipline_bps_records.academic_year_id', $academicYearId)
            ->where('discipline_bps_records.status', 1);
        if ($request->has('startDate') && $request->has('endDate') && $request->startDate != null && $request->endDate != null) {
            $query->whereBetween('discipline_bps_records.date', [$request->startDate, $request->endDate]);
        }
        if ($request->has('classroom_ids') && $request->classroom_ids !== null) {
            $classRoomId = explode(',', $request->input('classroom_ids'));
            $query->whereIn('students_classroom.classroom_id', $classRoomId);
        }
        if ($request->has('branch_ids') && $request->branch_ids !== null) {
            $branch_ids = explode(',', $request->input('branch_ids'));
            $query->whereIn('discipline_bps_records.branch_id', $branch_ids);
        }
        $records = $query->get();
        $result = [];

        // Loop through each record and process it
        foreach ($records as $record) {
            $branchId = $record['branch_id'];
            if (!isset($result[$branchId])) {
                $result[$branchId] = [
                    'total_point' => 0,
                    'total_prs' => 0,
                    'total_dps' => 0,
                    'data_count' => 0,
                    'student_count' => isset($student_count[$branchId]) ? count($student_count[$branchId]) : 1,
                    'branch_name' => null,
                ];
            }
            $result[$branchId]['total_point'] += $record['item_point'];
            $result[$branchId]['data_count']++;
            if ($request->has('classroom_ids') && $request->classroom_ids !== null) {
                $result[$branchId]['branch_name'] = $record['branch_description'] . ' - ' . $record['classroom_name'];
            } else {
                $result[$branchId]['branch_name'] = $record['branch_description'];
            }
            if ($record['item_type'] === 'prs') {
                $result[$branchId]['total_prs'] += $record['item_point'];
            } elseif ($record['item_type'] === 'dps') {
                $result[$branchId]['total_dps'] += abs($record['item_point']);
            }
        }
        // return $result;

        return view('partial_view.app.head_office.behaviour_analysis_chart', compact('result', 'classes', 'branches', 'academic_years', 'academicYearId'));
    }
    public function getBranchClass($branchId, $yearId)
    {
        $rd = '';
        $classrooms = Classroom::where('academic_year_id', $yearId)
            ->where('branch_id', $branchId)
            ->where('status', 1)
            ->get();
        foreach ($classrooms as $key => $class) {
            $rd .= '<option value="' . $class->classroom_id . '" selected data-branch-id="' . $class->branch_id . '">[' . $class->classroom_name . '] [ ' . $class->branch->branch_description  . ' ]</option>';
        }
        return $rd;
    }
    public function getClassStudent($classIds)
    {
        $rd = '';
        $studentArray = [];
        if ($classIds != null) {
            foreach ($classIds as $classId) {
                $students = $this->ah->getClassroomStudents($classId);
                foreach ($students as $data) {
                    $studentArray[] = $data;
                }
            }
        }
        foreach ($studentArray as $key => $student) {
            $sName = $student->student->name ?? '';
            $rd .= '<option value="' . $student->student_id . '">[' . $student->student_id . '] ' . $sName  . ' [ ' . $student->classroom->classroom_name . ' ]</option>';
        }
        return $rd;
    }
    public function getBehaviourAnalysisChartByClass($data)
    {

        if ($data['year_id'] !== null) {
            $academicYearId = $data['year_id'];
        } else {
            $academicYearId = $this->ah->academicYear();
        }
        $query = DisciplineRecord::select('discipline_bps_records.*', 'branches.branch_name', 'branches.branch_description', 'discipline_bps_items.item_point', 'students_classroom.*', 'classrooms.classroom_name')
            ->join('branches', 'branches.branch_id', 'discipline_bps_records.branch_id')
            ->join('discipline_bps_items', 'discipline_bps_items.discipline_item_id', 'discipline_bps_records.item_id')
            ->join('students_classroom', function ($join) use ($academicYearId) {
                $join->on('students_classroom.student_id', '=', 'discipline_bps_records.student_id')
                    ->where('students_classroom.academic_year_id', '=', $academicYearId);
            })
            ->join('classrooms', 'classrooms.classroom_id', 'students_classroom.classroom_id')
            ->where('discipline_bps_records.academic_year_id', $academicYearId)
            ->where('discipline_bps_records.status', 1)
            ->where('discipline_bps_records.branch_id', $data['branch_id']);
        if (isset($data['startDate']) && isset($data['endDate']) && $data['startDate'] != null && $data['endDate'] != null) {
            $query->whereBetween('discipline_bps_records.date', [$data['startDate'], $data['endDate']]);
        }
        if ($data['classroom_ids'] !== null && $data['student_ids'] == null) {
            $classRoomIds = explode(',', $data['classroom_ids']);
            $query->whereIn('students_classroom.classroom_id', $classRoomIds);
            $results = $query->get()->groupby('classroom_id');
            $student_count = StudentClassroom::where('academic_year_id', $academicYearId)->whereIn('classroom_id', $classRoomIds)->get();
        } else if ($data['classroom_ids'] !== null && $data['student_ids'] != null) {
            $classRoomIds = explode(',', $data['classroom_ids']);
            $studentIds = explode(',', $data['student_ids']);
            $query->whereIn('students_classroom.classroom_id', $classRoomIds)->whereIn('students_classroom.student_id', $studentIds);
            $results = $query->get()->groupby('student_id');
        } else {
            $results = $query->get();
            return ' <div class="text-center">Please Search With Class or Student</div>';
        }

        $response_data = [];
        foreach ($results as $data_id => $result) {
            if (!isset($response_data[$data_id])) {
                if ($data['classroom_ids'] !== null && $data['student_ids'] == null) {
                    $class_data = Classroom::find($data_id);
                    $name = $class_data->classroom_name;
                    $count = count($class_data->students);
                } else if ($data['classroom_ids'] !== null && $data['student_ids'] != null) {
                    $student_data = StudentInformation::find($data_id);
                    $name = $student_data->name;
                    $count = 1;
                }
                $response_data[$data_id] = [
                    'total_point' => 0,
                    'total_prs' => 0,
                    'total_dps' => 0,
                    'data_count' => 0,
                    'student_count' => $count,
                    'name' => $name,
                ];
            }
            if ($data['classroom_ids'] !== null && $data['student_ids'] == null) {
                // $result[$branchId]['branch_name'] = $record['branch_description'] . ' - ' . $record['classroom_name'];
            } else if ($data['classroom_ids'] !== null && $data['student_ids'] != null) {
                // $result[$branchId]['branch_name'] = $record['branch_description'];
            }

            foreach ($result as $result_data) {
                $response_data[$data_id]['total_point'] +=  $result_data->item_point;
                if ($result_data->item_type === 'prs') {
                    $response_data[$data_id]['total_prs'] += $result_data->item_point;
                } elseif ($result_data->item_type === 'dps') {
                    $response_data[$data_id]['total_dps'] += abs($result_data->item_point);
                }
                $response_data[$data_id]['data_count']++;
            }
        }
        $student_count_for_no_records = StudentClassroom::where('academic_year_id', $academicYearId)->whereIn('classroom_id', $classRoomIds)->get()->groupBy('classroom_id');
        foreach ($student_count_for_no_records as $class_id => $data_class) {
            if (!isset($results[$class_id])) {
                $classroomData =  Classroom::find($class_id);
                $response_data[$class_id] = [
                    'total_point' => 0,
                    'total_prs' => 0,
                    'total_dps' => 0,
                    'data_count' => 0,
                    'student_count' => count($student_count_for_no_records[$class_id]),
                    'name' => $classroomData->classroom_name,
                ];
            }
        }
        // return response()->json($response_data);
        return view('partial_view.app.head_office.behaviour_analysis_chart_by_class', compact('data', 'response_data'));
    }

    public function branchAwards($request)
    {
        $academicYearId = $this->ah->academicYear();
        $branchId = $this->ah->currentBranch();
        $today = Carbon::now();
        $currentSemester = null;
        // Get from Request --start
        $studentId = $request->student ?? null;
        $caseType = $request->caseType ?? null;
        $classList = $request->classList ?? [];
        $dateType = $request->dateType;
        // --end
        $startDate = $request->startDate ?? null;
        $endDate = $request->endDate ?? null;
        // Filter DateType
        if ($dateType == 'term') {
            $currentSemester = $this->ah->currentSemester();
        }
        if ($dateType == 'week') {
            $startDate = $today->startOfWeek()->toDateString();
            $endDate = $today->endOfWeek()->toDateString();
        }
        if ($dateType == 'month') {
            $startDate = $today->startOfMonth()->toDateString();
            $endDate = $today->endOfMonth()->toDateString();
        }
        if ($dateType == 'over-all') {
            $currentSemester = null;
        }
        $award_records = \DB::table('discipline_award_remaining_points')
            ->select(
                'discipline_award_remaining_points.discipline_award_remaining_point_id',
                'discipline_award_remaining_points.academic_year_id',
                'discipline_award_remaining_points.branch_id',
                'discipline_award_remaining_points.student_id',
                'discipline_award_remaining_points.remaining_point',
                'classrooms.classroom_name',
                'students_detail.first_name',
                'students_detail.middle_name',
                'students_detail.last_name',
                \DB::raw('COUNT(discipline_award_records.discipline_award_record_id) AS award_count')
            )
            ->leftJoin('students_detail', 'students_detail.student_id', 'discipline_award_remaining_points.student_id')
            ->leftJoin('students_classroom', function ($join) use ($academicYearId, $branchId) {
                $join->on('students_classroom.student_id', 'discipline_award_remaining_points.student_id')
                    ->where('students_classroom.branch_id', $branchId)
                    ->where('students_classroom.academic_year_id', $academicYearId);
            })
            ->leftJoin('classrooms', 'classrooms.classroom_id', 'students_classroom.classroom_id')
            ->leftJoin('discipline_award_records', function ($join) {
                $join->on('discipline_award_records.discipline_award_remaining_point_id', '=', 'discipline_award_remaining_points.discipline_award_remaining_point_id');
            })
            ->when(count($classList) > 0, function ($query) use ($classList) {
                return $query->whereIn('classrooms.classroom_id', $classList);
            })
            ->when($studentId, function ($query) use ($studentId) {
                return $query->where('discipline_award_remaining_points.student_id', $studentId);
            })
            ->where('discipline_award_remaining_points.academic_year_id', $academicYearId)
            ->where('discipline_award_remaining_points.branch_id', $branchId)
            ->groupBy(
                'discipline_award_remaining_points.discipline_award_remaining_point_id',
                'discipline_award_remaining_points.academic_year_id',
                'discipline_award_remaining_points.branch_id',
                'discipline_award_remaining_points.student_id',
                'discipline_award_remaining_points.remaining_point',
                'classrooms.classroom_name',
                'students_detail.first_name',
                'students_detail.middle_name',
                'students_detail.last_name'
            )
            ->orderBy('discipline_award_remaining_points.remaining_point', 'desc')
            ->get();
        $dt = Datatables::of($award_records)->addIndexColumn()
            ->addColumn('student_id', function ($row) {
                return $row->student_id;
            })
            ->addColumn('student_name', function ($row) {
                return $row->first_name . " " . $row->middle_name . " " . $row->last_name;
            })
            ->addColumn('classroom_name', function ($row) {
                return $row->classroom_name;
            })
            ->addColumn('remaining_point', function ($row) {
                return $row->remaining_point;
            })
            ->addColumn('award_count', function ($row) {
                return $row->award_count;
            })
            ->addColumn('discipline_award_remaining_point_id', function ($row) {
                return $row->discipline_award_remaining_point_id;
            })
            ->make(true);

        return $dt;
    }
    public function storeBpsAward($request)
    {
        // dd($request->all());
        $data = $request->all();
        $remainingPointRecord = DisciplineAwardRemainingPoint::find($data['discipline_award_remaining_point_id']);
        $total_point = $remainingPointRecord->remaining_point - $data['gift_point'];
        if ($total_point < 0) {
            throw new \ErrorException('Gift Point Cannot Exceed the Remaining Point!');
        }
        $remainingPointRecord->update([
            'remaining_point' => $total_point
        ]);
        $award_record = new DisciplineAwardRecord();
        $award_record->discipline_award_remaining_point_id  = $data['discipline_award_remaining_point_id'];
        $award_record->academic_year_id                     = $this->ah->branchAcademicYear($data['branch_id']);
        $award_record->academic_semester                    = $this->ah->currentSemester();
        $award_record->student_id                           = $data['student_id'];
        $award_record->user_id                              = \Auth::user()->id;
        $award_record->branch_id                            = $data['branch_id'];
        $award_record->date                                 = $data['date'];
        $award_record->gift_point                           = $data['gift_point'];
        $award_record->gift_title                           = $data['gift_title'];
        $award_record->note                                 = $data['note'];
        $award_record->save();
    }
    public function deleteBpsAward($recordId)
    {
        $award_record = DisciplineAwardRecord::find($recordId);
        if ($award_record) {
            $remainingPointRecord = DisciplineAwardRemainingPoint::find($award_record->discipline_award_remaining_point_id);
            $total_point = $remainingPointRecord->remaining_point + $award_record->gift_point;
            $remainingPointRecord->update([
                'remaining_point' => $total_point
            ]);
            $award_record->delete();
        } else {
            throw new \ErrorException('Record not Found!');
        }
    }
    public function branchAwardRecordList($request)
    {
        $academicYearId = $this->ah->academicYear();
        $branchId = $this->ah->currentBranch();
        $today = Carbon::now();
        $currentSemester = null;
        // Get from Request --start
        $studentId = $request->student ?? null;
        $caseType = $request->caseType ?? null;
        $classList = $request->classList ?? [];
        $dateType = $request->dateType;
        // --end
        $startDate = $request->startDate ?? null;
        $endDate = $request->endDate ?? null;
        // Filter DateType
        if ($dateType == 'term') {
            $currentSemester = $this->ah->currentSemester();
        }
        if ($dateType == 'week') {
            $startDate = $today->startOfWeek()->toDateString();
            $endDate = $today->endOfWeek()->toDateString();
        }
        if ($dateType == 'month') {
            $startDate = $today->startOfMonth()->toDateString();
            $endDate = $today->endOfMonth()->toDateString();
        }
        if ($dateType == 'over-all') {
            $currentSemester = null;
        }
        $award_record_lists = \DB::table('discipline_award_records')
            ->select(
                'discipline_award_records.discipline_award_record_id',
                'discipline_award_records.discipline_award_remaining_point_id',
                'discipline_award_records.academic_year_id',
                'discipline_award_records.branch_id',
                'discipline_award_records.student_id',
                'discipline_award_records.gift_title',
                'discipline_award_records.gift_point',
                'discipline_award_records.date',
                'discipline_award_records.note',
                'classrooms.classroom_name',
                'students_detail.first_name',
                'students_detail.middle_name',
                'students_detail.last_name',
            )
            ->leftJoin('students_detail', 'students_detail.student_id', 'discipline_award_records.student_id')
            ->leftJoin('students_classroom', function ($join) use ($academicYearId, $branchId) {
                $join->on('students_classroom.student_id', 'discipline_award_records.student_id')
                    ->where('students_classroom.branch_id', $branchId)
                    ->where('students_classroom.academic_year_id', $academicYearId);
            })
            ->leftJoin('classrooms', 'classrooms.classroom_id', 'students_classroom.classroom_id')
            ->when(count($classList) > 0, function ($query) use ($classList) {
                return $query->whereIn('classrooms.classroom_id', $classList);
            })
            ->when($studentId, function ($query) use ($studentId) {
                return $query->where('discipline_award_records.student_id', $studentId);
            })
            // filter with semiester id
            ->when($currentSemester, function ($query) use ($currentSemester) {
                return $query->where('discipline_award_records.academic_semester', $currentSemester);
            })
            // Filter with Date
            ->when($startDate && $endDate, function ($query) use ($startDate, $endDate) {
                return $query->whereBetween('discipline_award_records.date', [$startDate, $endDate]);
            })
            ->where('discipline_award_records.academic_year_id', $academicYearId)
            ->where('discipline_award_records.branch_id', $branchId)
            ->groupBy(
                'discipline_award_records.discipline_award_record_id',
                'discipline_award_records.discipline_award_remaining_point_id',
                'discipline_award_records.academic_year_id',
                'discipline_award_records.branch_id',
                'discipline_award_records.student_id',
                'discipline_award_records.gift_title',
                'discipline_award_records.gift_point',
                'discipline_award_records.date',
                'discipline_award_records.note',
                'classrooms.classroom_name',
                'students_detail.first_name',
                'students_detail.middle_name',
                'students_detail.last_name'
            )
            ->orderBy('discipline_award_records.date', 'desc')
            ->get();
        // dd($award_record_lists);
        $dt = Datatables::of($award_record_lists)->addIndexColumn()
            ->addColumn('student_id', function ($row) {
                return $row->student_id;
            })
            ->addColumn('student_name', function ($row) {
                return $row->first_name . " " . $row->middle_name . " " . $row->last_name;
            })
            ->addColumn('classroom_name', function ($row) {
                return $row->classroom_name;
            })
            ->addColumn('gift_title', function ($row) {
                return $row->gift_title;
            })
            ->addColumn('gift_point', function ($row) {
                return $row->gift_point;
            })
            ->addColumn('date', function ($row) {
                return $row->date;
            })
            ->addColumn('note', function ($row) {
                return $row->note;
            })
            ->addColumn('discipline_award_record_id', function ($row) {
                return $row->discipline_award_record_id;
            })
            ->make(true);

        return $dt;
    }

    public function storeBpsItem($request){
        DisciplineItem::create([
            'item_title'        =>$request->item_title,
            'item_type'         =>$request->item_type,
            'item_point'        =>$request->item_point,
        ]);
    }
    public function archiveBpsItem($bpsItemId,$status){
        DisciplineItem::find($bpsItemId)->update(['item_status'=>$status]);
    }
    public function updateBpsItem($itemId,$itemTitle){
        DisciplineItem::find($itemId)->update(['item_title'=>$itemTitle]);
    }
    public function storeDetentionDuty($dutyUserId,$detentionTypeId){
        if($dutyUserId && $detentionTypeId){
            DetentionDutyList::create([
                'academic_year_id'      =>$this->ah->academicYear(),
                'branch_id'             =>$this->ah->currentBranch(),
                'duty_teacher_id'       =>$dutyUserId,
                'detention_type_id'     =>$detentionTypeId,
                'assigned_user_id'      =>Auth::user()->id,
            ]);
        }else{
    throw new \ErrorException('Please Fill All Field');
        }
    }
    public function updateDetentionDuty($request){
        if($request->all()){
            DetentionDutyList::find($request->dutyId)->update([
                'duty_teacher_id'=>$request->editDutyUserId,
                'detention_type_id'=>$request->editDetentionTypeId,
                'assigned_user_id'=>Auth::user()->id,
            ]);
        }else{
        throw new \ErrorException('Please Fill All Field');
            }
        }
    public function archiveDetentionDuty($dutyId){
        DetentionDutyList::find($dutyId)->update(['status'=>0]);
    }
}
