<?php
namespace   App\Library\Repository;
use App\Library\Helper\AcademicHelper;
use App\Models\StudentHealthInformation;
use App\Models\UserDetail;

class HealthReportRepository{
    public function getStudentHealthStatus(){
        $ah = new AcademicHelper();
        $list = $ah->branchStudents($ah->currentBranch());
        $ids = $list->pluck('id'); // This will get only the 'id' column

        // Get health info for students in this list
        $healthRecords = StudentHealthInformation::whereIn('student_id', $ids)->get()->keyBy('student_id');
        // Merge health data into each student (or null if not exists)
        $records = $list->map(function ($student) use ($healthRecords) {
            $student->health = $healthRecords->get($student->id); // null if not exists
            return $student;
        });
        return $records;
    }

    public function getStaffHealthStatus(){
        $ah = new AcademicHelper();
        $list = $ah->branchUsers($ah->currentBranch());
        $ids = $list->pluck('id');

        $records = UserDetail::whereIn('user_id', $ids)->get();

        return $records;
    }

}