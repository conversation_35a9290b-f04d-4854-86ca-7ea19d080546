<?php

namespace App\Library\Repository;

use App\Models\ElectiveGrade;
use App\Models\Timetable;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use App\Models\StudentClassAttendance;
use App\Models\StudentAttendance;
use App\Library\Helper\AcademicHelper;
use App\Library\Repository\MobileNotificationRepository;
use App\Models\AttendanceLog;
use App\Models\DisciplineRecord;
use App\Models\PublicHoliday;
use Carbon\Carbon;
use Carbon\CarbonPeriod;
use Illuminate\Support\Facades\Auth;

class TimetableRepository
{


    protected MobileNotificationRepository $mobileNotification;
    protected BpsRepository $bpsRepository;
    public function __construct(MobileNotificationRepository $mobileNotification, BpsRepository $bpsRepository)
    {
        $this->mobileNotification = $mobileNotification;
        $this->bpsRepository = $bpsRepository;
    }
    public function update($data)
    {
        $academicHelper = new AcademicHelper();
        $academicYearId = $academicHelper->academicYear();
        $branchId = $academicHelper->currentBranch();

        DB::beginTransaction();
        try {
            Timetable::where('academic_year_id', $academicYearId)
                ->where('branch_id', $branchId)
                ->where('user_id', $data['userId'])
                ->delete();

            $dataArr = explode("/*/", $data['data']);


            foreach ($dataArr as $key => $item) {
                if ($item != '') {
                    $itemArr = explode("_", $item);

                    $day = $itemArr[0];
                    $time = $itemArr[1];
                    $grade = $itemArr[2];
                    $subject = $itemArr[3];

                    $timetable                    = new Timetable();
                    $timetable->academic_year_id  = $academicYearId;
                    $timetable->branch_id         = $branchId;
                    $timetable->user_id           = $data['userId'];
                    $timetable->week_day          = $day;
                    $timetable->week_time         = $time;
                    $timetable->subject_id        = $subject;
                    $timetable->grade_id          = $grade;
                    $timetable->save();
                }
            }
            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    public function getTimetable($userId)
    {
        $rd = '';

        $academicHelper = new AcademicHelper();
        $academicYearId = $academicHelper->academicYear();
        $branchId = $academicHelper->currentBranch();

        $list = Timetable::where('academic_year_id', $academicYearId)
            ->where('branch_id', $branchId)
            ->where('user_id', $userId)
            ->get();

        foreach ($list as $key => $data) {
            $rd .= $data->week_day . "|" . $data->week_time . "|" . $data->grade_id . "/*/";
        }

        return $rd;
    }


    public function getTimetableByBranch($data)
    {
        $rd = '';

        $academicHelper = new AcademicHelper();
        $academicYearId = $academicHelper->academicYear();

        $currentWeek = $academicHelper->branchCurrentWeek($data['branchId']);

        $list = Timetable::selectRaw(
            '
                                        academic_timetable.timetable_id,
                                        academic_timetable.week_day,
                                        academic_timetable.week_time,
                                        academic_timetable.grade_id,
                                        academic_timetable.subject_id,
                                        count(students_attendance_class.class_attendance_id) as att'
        )
            ->leftJoin('students_attendance_class', function ($join) use ($academicYearId, $currentWeek) {
                $join->where('students_attendance_class.academic_year_id', $academicYearId);
                $join->on('students_attendance_class.week_day', 'academic_timetable.week_day');
                $join->on('students_attendance_class.week_time', 'academic_timetable.week_time');
                $join->on('students_attendance_class.grade_id', 'academic_timetable.grade_id');
                $join->on('students_attendance_class.subject_id', 'academic_timetable.subject_id');
                $join->where('students_attendance_class.week', $currentWeek);
            })
            ->where('academic_timetable.academic_year_id', $academicYearId)
            ->where('academic_timetable.branch_id', $data['branchId'])
            ->where('academic_timetable.user_id', $data['userId'])
            ->groupBy('academic_timetable.timetable_id')
            ->get();

        foreach ($list as $key => $d) {
            $isAttendance = $d->att > 0 ? '1' : '0';

            $gradeName = isset($d->elective_grade->grade_name) ? $d->elective_grade->grade_name : '[Deleted]';

            $rd .= $d->timetable_id . '/*/' .
                $d->week_day . '/*/' .
                $d->week_time . '/*/' .
                $gradeName . '/*/' .
                $d->grade_id . '/*/' .
                $d->subject->subject_name . '/*/' .
                $d->subject_id . '/*/' .
                $isAttendance . '*-*';
        }
        return $rd;
    }

    public function getDashboardTimetable()
    {
        $rd = '';

        $academicHelper = new AcademicHelper();
        $currentWeek = $academicHelper->currentWeek();
        $academicYearId = $academicHelper->academicYear();
        $branchId = $academicHelper->currentBranch();

        $userId = \Auth::user()->id;

        $currentWeek = $academicHelper->currentWeek();

        $list = Timetable::selectRaw(
            '
                                        academic_timetable.timetable_id,
                                        academic_timetable.week_day,
                                        academic_timetable.week_time,
                                        academic_timetable.grade_id,
                                        academic_timetable.subject_id,
                                        count(students_attendance_class.class_attendance_id) as att'
        )
            ->leftJoin('students_attendance_class', function ($join) use ($academicYearId, $currentWeek) {
                $join->where('students_attendance_class.academic_year_id', $academicYearId);
                $join->on('students_attendance_class.week_day', 'academic_timetable.week_day');
                $join->on('students_attendance_class.week_time', 'academic_timetable.week_time');
                $join->on('students_attendance_class.grade_id', 'academic_timetable.grade_id');
                $join->on('students_attendance_class.subject_id', 'academic_timetable.subject_id');
                $join->where('students_attendance_class.week', $currentWeek);
            })
            ->where('academic_timetable.academic_year_id', $academicYearId)
            ->where('academic_timetable.branch_id', $branchId)
            ->where('academic_timetable.user_id', $userId)
            ->groupBy('academic_timetable.timetable_id')
            ->get();
        // Getting Public Holidays make single Array
        $public_holidays = PublicHoliday::select('academic_year_id', 'start_date', 'end_date')->where('academic_year_id', $academicYearId)->where('branch_id', $branchId)->get();
        $holidayArray = [];
        foreach ($public_holidays as $publicHoliday) {
            $holidayStartDate = $publicHoliday->start_date;
            $holidayEndDate = $publicHoliday->end_date;
            $singleHolidayArray = CarbonPeriod::create($holidayStartDate, $holidayEndDate);
            foreach ($singleHolidayArray as $singleHoliday) {
                $holidayArray[] = $singleHoliday->format('Y-m-d'); // Convert Carbon date to string
            }
        }
        $holidayString = implode(',', $holidayArray);
        // dd($holidayArray);
        foreach ($list as $key => $d) {
            $isAttendance = $d->att > 0 ? '1' : '0';

            $gradeName = isset($d->elective_grade->grade_name) ? $d->elective_grade->grade_name : '[Deleted]';

            $rd .= $d->timetable_id . '/*/' .
                $d->week_day . '/*/' .
                $d->week_time . '/*/' .
                $gradeName . '/*/' .
                $d->grade_id . '/*/' .
                $d->subject->subject_name . '/*/' .
                $d->subject_id . '/*/' .
                $isAttendance . '*-*';
        }
        $rd .= '|**|' . $holidayString;
        return $rd;
    }

    public function storeClassAttendance($data)
    {
        $ah = new AcademicHelper();
        $timetable = Timetable::find($data['timetableId']);
        $attendanceInfo = explode("/", $data['attendance']);

        $academicYearId = $ah->academicYear();
        $currentWeek = $ah->currentWeek();
        $branchId = $ah->currentBranch();
        // $weekDayDate = $ah->weekDayDate($currentWeek, $timetable->week_day);
        foreach ($attendanceInfo as $key => $info) {
            if ($info != '') {
                $arr = explode("|", $info);

                StudentClassAttendance::updateOrCreate(
                    [
                        'academic_year_id'  => $academicYearId,
                        'week_day'          => $timetable->week_day,
                        'week_time'         => $timetable->week_time,
                        'subject_id'        => $timetable->subject_id,
                        'teacher_id'        => $timetable->user_id,
                        'grade_id'          => $timetable->grade_id,
                        'week'              => $currentWeek,
                        'student_id'        => $arr[0],
                        'date'              => $data['AttendanceDate']
                    ],
                    [
                        // 'date'              => date('Y-m-d'),
                        'attendance_status' => $arr[1],
                        'attendance_note'   => $arr[2]
                    ]
                );
                /*      //Sending notification for absent subjects
                if($arr[1] == "absent"){
                    // Check if student is already marked absent for the entire day
                    $currentDate = date('Y-m-d');
                    $academicYearId = $this->ah->branchAcademicYear($timetable->branch_id);

                    $dailyAttendance = \App\Models\StudentAttendance::where('student_id', $arr[0])
                        ->where('date', $currentDate)
                        ->where('academic_year_id', $academicYearId)
                        ->where('attendance_status', 'absent')
                        ->first();

                    if (!$dailyAttendance) {
                        // Only send notification if student is not already marked absent for the day
                        $userInfo = User::where('id',  $arr[0])
                            ->where('user_status', 1)
                            ->first();
                        $subjectInfo = ElectiveGrade::where('grade_id', $timetable->grade_id)
                            ->first();
                        $msj = $userInfo->name." was marked as absent in ".$subjectInfo->grade_name." class on ".date('Y-m-d');
                        $this->mobileNotification->sendSingle([
                            'title'     => 'Attendance',
                            'message'   => $msj,
                            'student'   => $arr[0],
                            'type'      => 'attendance'
                        ]);
                    } else {
                        \Log::info("Skipping class absence notification for student {$arr[0]} - already marked absent for the day (TimetableRepository)");
                    }
                }
                */
                if ($arr[1] == "late") {
                    $bpsRecord = [
                        "student" => [$arr[0]],
                        "case_type" => "1",
                        "dps_case" => "136", // late dps
                        "prs_case" => null,
                        "branch_id" => $branchId,
                        "date" => $data['AttendanceDate'],
                        "note" => null,
                    ];
                    $checkBps = DisciplineRecord::where('student_id', $arr[0])
                        ->where('user_id', Auth::user()->id)
                        ->where('item_id', '136')
                        ->where('date', $data['AttendanceDate'])
                        ->where('branch_id', $branchId)
                        ->first();
                    // dd($checkBps);
                    // if late between Classes , get a late bps record
                    if (!$checkBps) {
                        $this->bpsRepository->storeBps($bpsRecord);
                    }
                }
            }
        }
    }

    /*public function storeApiClassAttendance($data) {

        $timetable = Timetable::find($data['timetableId']);
        $academicYearId = $timetable->academic_year_id;
        $branchId = $timetable->branch_id;
        $today = date('Y-m-d');
        $attendanceInfo = explode("/", $data['attendance']);
        $weekInfo = AcademicWeek::where('start_date', '<=', $today)
            ->where('end_date', '>=', $today)
            ->where('branch_id', $branchId)
            ->first();
        $currentWeek = $weekInfo->week;
        // $weekDayDate = $ah->weekDayDate($currentWeek, $timetable->week_day);

        foreach ($attendanceInfo as $key => $info) {
            if($info != '') {
                $arr = explode("|", $info);

                StudentClassAttendance::updateOrCreate(
                    [
                        'academic_year_id'  => $academicYearId,
                        'week_day'          => $timetable->week_day,
                        'week_time'         => $timetable->week_time,
                        'subject_id'        => $timetable->subject_id,
                        'teacher_id'        => $timetable->user_id,
                        'grade_id'          => $timetable->grade_id,
                        'week'              => $currentWeek,
                        'student_id'        => $arr[0]
                    ],
                    [
                        'date'              => date('Y-m-d'),
                        'attendance_status' => $arr[1],
                        'attendance_note'   => $arr[2]
                    ]
                );
                /*      //Sending notification for absent subjects
                if($arr[1] == "absent"){
                    $userInfo = User::where('id',  $arr[0])
                        ->where('user_status', 1)
                        ->first();
                    $subjectInfo = ElectiveGrade::where('grade_id', $timetable->grade_id)
                        ->first();
                    $msj = $userInfo->name." was marked as absent in ".$subjectInfo->grade_name." class on ".date('Y-m-d');
                    $this->mobileNotification->sendSingle([
                        'title'     => 'Attendance',
                        'message'   => $msj,
                        'student'   => $arr[0],
                        'type'      => 'attendance'
                    ]);
                }



            }
        }
    }*/


    public function storeDailyAttendance($data)
    {
        $ah = new AcademicHelper();
        $attendanceInfo = explode("/", $data['attendance']);
        $branchId = $ah->currentBranch();
        $academicYearId = $ah->academicYear();
        $attendanceTime = Carbon::createFromFormat('H:i', '8:00');
        foreach ($attendanceInfo as $key => $info) {
            if ($info != '') {
                $arr = explode("|", $info);
                if (Carbon::hasFormat($arr[3], 'H:i')) {
                    $swipeTime = Carbon::createFromFormat('H:i', $arr[3]);
                    // $swipeTime = $attendanceTime->copy()->addMinutes($arr[3])->format('H:i:s');
                    if ($arr[1] == "absent") {
                        $swipeTime = null;
                    }
                    // return $swipeTime->format('H:i:s');
                    // StudentAttendance::updateOrCreate(
                    //     [
                    //         'academic_year_id'      => $academicYearId,
                    //         'branch_id'             => $branchId,
                    //         'student_id'            => $arr[0],
                    //         'date'                  => date('Y-m-d'),
                    //     ],
                    //     [
                    //         'attendance_status'     => $arr[1],
                    //         'notes'                 => $arr[2],
                    //         'swipe_time'            => $swipeTime,
                    //     ]
                    // );
                    StudentAttendance::create([
                        'academic_year_id'      => $academicYearId,
                        'branch_id'             => $branchId,
                        'student_id'            => $arr[0],
                        'date'                  => date('Y-m-d'),
                        'attendance_status'     => $arr[1],
                        'notes'                 => $arr[2],
                        'swipe_time'            => $swipeTime,
                    ]);
                    if ($arr[1] == "absent") {
                        $userInfo = User::where('id',  $arr[0])
                            ->where('user_status', 1)
                            ->first();
                        $msj = $userInfo->name . " was marked as absent on:" . date('Y-m-d');

                        // $this->mobileNotification->sendSingle([
                        //     'title'         => 'Attendance',
                        //     'message'       => $msj,
                        //     'student'       => $arr[0],
                        //     'type'          => 'attendance',
                        //     'user_type'     => 'student',
                        // ]);
                        $this->mobileNotification->sendRealTime([
                            'title'         => 'Attendance',
                            'message'       => $msj,
                            'student'       => $arr[0],
                            'type'          => 'attendance',
                            'user_type'     => 'student',
                            'priority'      => 'high',
                        ]);
                    }
                    if ($arr[1] == "late") {
                        $bpsRecord = [
                            "student" => [$arr[0]],
                            "case_type" => "1",
                            "dps_case" => "136", // late dps
                            "prs_case" => null,
                            "date" => date('Y-m-d'),
                            "note" => null,
                            "branch_id" => $branchId,
                        ];
                        $checkBps = DisciplineRecord::where('student_id', $arr[0])
                            ->where('user_id', Auth::user()->id)
                            ->where('item_id', '136')
                            ->where('date', date('Y-m-d'))
                            ->where('branch_id', $branchId)
                            ->first();
                        // if late in Entrance , get a late bps record
                        if (!$checkBps) {
                            $this->bpsRepository->storeBps($bpsRecord);
                        }
                    }
                }
            }
        }
    }
    public function storeStaffDailyAttendance($data)
    {
        $ah = new AcademicHelper();
        $attendanceInfo = explode("/", $data['attendance']);
        $academicYearId = $ah->academicYear();
        $attendanceTime = Carbon::createFromFormat('H:i', '8:00');
        foreach ($attendanceInfo as $key => $info) {
            if ($info != '') {
                $arr = explode("|", $info);
                if (Carbon::hasFormat($arr[2], 'H:i')) {
                    $swipeTime = Carbon::createFromFormat('H:i', $arr[2]);
                    if ($arr[1] == "absent") {
                        $swipeTime = null;
                    }
                    $dateTime = Carbon::parse(date('Y-m-d') . ' ' . $swipeTime->format('H:i:s'));
                    $checkActiveBranch = \DB::table('users_branches')->where('user_id', $arr[0])->where('is_active', 1)->first();
                    if ($checkActiveBranch) {
                        $branchId = $checkActiveBranch->branch_id;
                    } else {
                        $branchId = Auth::user()->branch_id;
                    }
                    AttendanceLog::create([
                        'academic_year_id'      => $academicYearId,
                        'device_id'             =>  0,
                        'branch_id'             => $branchId,
                        'user_id'               => $arr[0],
                        'date'                  => date('Y-m-d'),
                        'date_time'             => $dateTime,
                        'time'                  => $swipeTime,
                    ]);
                }
            }
        }
    }
}
