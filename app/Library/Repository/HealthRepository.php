<?php

namespace App\Library\Repository;

use App\Models\StudentMeasurements;
use App\Models\UserDetail;
use App\Models\StudentHealthInformation;
use App\Library\Helper\AcademicHelper;
use App\Models\HealthStudent;
use App\Models\HealthStaff;
use App\Models\HealthGuest;

class HealthRepository {

    public function saveStaffHealthInfo($data) {

        UserDetail::updateOrCreate(
            [
                'user_id' => $data['user_id'],
            ],
            [
                'health_allergies'                      => $data['allergies'],
                'health_chronic_diseases'               => $data['chronic_diseases'],
                'health_chronic_diseases_require_leave' => $data['chronic_diseases_requires_leave'],
                'health_regularly_used_medication'      => $data['regularly_used_medication'],
                'health_is_pregnant'                    => $data['pregnant'] ?? null,
                'health_height'                         => $data['height'],
                'health_weight'                         => $data['weight'],
                'health_blood_type'                     => $data['blood_type'],
                'health_other_health_issues'            => $data['other_health_issues'],
            ]
        );
    }

    public function saveStudentHealthInfo($data) {
        $medications = '';

        // Check if 'allowed_medication' exists and is an array
        if (isset($data['allowed_medication']) && is_array($data['allowed_medication'])) {
            foreach ($data['allowed_medication'] as $med) {
                if ($med != '') {
                    $medications .= $med . ',';
                }
            }

            // Trim trailing comma if medications were added
            $medications = rtrim($medications, ',');
        }


        $update = StudentHealthInformation::updateOrCreate(
            [
                'student_id' => $data['student_id'],
            ],
            [
                'medical_conditions' => $data['medical_condition'],
                'regularly_used_medication' => $data['regularly_used_medication'],
                'has_vision_problem' => $data['vision_problem'],
                'vision_check_date' => $data['vision_check_date'],
                'hearing_issue' => $data['hearing_issue'],
                'special_food_consideration' => $data['food_consideration'],
                'allergies' => $data['allergy'],
                'allergy_symptoms' => $data['allergy_symptoms'],
                'allergy_first_aid' => $data['allergy_first_aid'],
                'allowed_drugs' => $medications,
                'emergency_name_1' => $data['emergency_name_1'],
                'emergency_name_2' => $data['emergency_name_2'],
                'emergency_phone_1' => $data['emergency_phone_1'],
                'emergency_phone_2' => $data['emergency_phone_2']
            ]
        );

    }


    public function createStudentRecord($data) {
        $ah = new AcademicHelper();
        HealthStudent::create([
            'branch_id'                 => $ah->currentBranch(),
            'academic_year_id'          => $ah->academicYear(),
            'student_id'                => $data['student_id'],
            'date'                      => $data['date'],
            'time'                      => $data['time'],
            'discharge_time'            => $data['discharge_time'],
            'reason'                    => implode(',', $data['reason']),
            'action'                    => implode(',', $data['action']),
            'parent_contact_time'       => $data['parent_contact_time'],
            'temperature'               => $data['temperature'],
            'blood_pressure'            => $data['blood_pressure'],
            'saturation'                => $data['saturation'],
            'pulse'                     => $data['pulse'],
            'medication'                => $data['medication'],
            'comments'                  => $data['comments'],
            'creator_id'                => \Auth::user()->id,
            'status'                    => 1

        ]);
    }

    public function createStudentMeasureRecord($data) {
        $ah = new AcademicHelper();
        StudentMeasurements::create([
            'branch_id'                 => $ah->currentBranch(),
            'academic_year_id'          => $ah->academicYear(),
            'student_id'                => $data['student_id'],
            'date'                      => $data['date'],
            'height'                    => $data['height'],
            'weight'                    => $data['weight'],
        ]);

    }
    public function deleteStudentMeasureRecord($recordId) {
        // Check if the record exists
        $record = StudentMeasurements::find($recordId);

        if (!$record) {
            return response()->json(['message' => 'Record not found'], 404);
        }

        // Optionally check if the user is authorized to delete the record
        // $this->authorize('delete', $record);

        // Attempt to delete the record
        try {
            $record->delete();
            return response()->json(['message' => 'Record deleted successfully'], 200);
        } catch (\Exception $e) {
            return response()->json(['message' => 'Error deleting record', 'error' => $e->getMessage()], 500);
        }
    }


    public function createStaffRecord($data) {
        $ah = new AcademicHelper();
        HealthStaff::create([
            'branch_id'                 => $ah->currentBranch(),
            'academic_year_id'          => $ah->academicYear(),
            'user_id'                   => $data['user_id'],
            'date'                      => $data['date'],
            'time'                      => $data['time'],
            'discharge_time'            => $data['discharge_time'],
            'reason'                    => implode(',', $data['reason']),
            'action'                    => implode(',', $data['action']),
            'temperature'               => $data['temperature'],
            'blood_pressure'            => $data['blood_pressure'],
            'saturation'                => $data['saturation'],
            'pulse'                     => $data['pulse'],
            'medication'                => $data['medication'],
            'comments'                  => $data['comments'],
            'creator_id'                => \Auth::user()->id,
            'status'                    => 1
        ]);
    }

    public function deleteStudentRecord($recordId) {
        $record = HealthStudent::find($recordId);
        $record->status = 0;
        $record->deleter_id = \Auth::user()->id;
        $record->save();
    }

    public function deleteStaffRecord($recordId) {
        $record = HealthStaff::find($recordId);
        $record->status = 0;
        $record->deleter_id = \Auth::user()->id;
        $record->save();
    }

    public function deleteGuestRecord($recordId) {
        $record = HealthGuest::find($recordId);
        $record->status = 0;
        $record->deleter_id = \Auth::user()->id;
        $record->save();
    }

    public function createGuestRecord($data) {

        $ah = new AcademicHelper();
        HealthGuest::create([
            'branch_id'                 => $ah->currentBranch(),
            'academic_year_id'          => $ah->academicYear(),
            'full_name'                 => $data['name'],
            'date'                      => $data['date'],
            'time'                      => $data['time'],
            'discharge_time'            => $data['discharge_time'],
            'reason'                    => implode(',', $data['reason']),
            'action'                    => implode(',', $data['action']),
            'temperature'               => $data['temperature'],
            'blood_pressure'            => $data['blood_pressure'],
            'saturation'                => $data['saturation'],
            'pulse'                     => $data['pulse'],
            'medication'                => $data['medication'],
            'comments'                  => $data['comments'],
            'creator_id'                => \Auth::user()->id,
            'status'                    => 1
        ]);
    }
}