<?php

namespace App\Library\Repository;

use Illuminate\Support\Facades\DB;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use App\Library\Helper\AcademicHelper;
use App\Models\TeacherClassLesson;
use \Exception;

class TCLRepository {

    public function create($data) {
        $academicHelper = new AcademicHelper();
        $branchId = $academicHelper->currentBranch();
        $academicYearId = $academicHelper->academicYear();

        foreach($data['classrooms'] as $classroom) {
            foreach ($data['subjects'] as $subject) {
                $tcl = new TeacherClassLesson();
                $tcl->branch_id = $branchId;
                $tcl->academic_year_id = $academicYearId;
                $tcl->teacher_id = $data['teacher'];
                $tcl->class_id = $classroom;
                $tcl->subject_id = $subject;
                $tcl->weekly_count = $data['weekly_count'];
                $tcl->start_date = $data['start_date'];
                $tcl->end_date = $data['end_date'];
                $tcl->save();
            }
        }
    }

    public function delete($tclId) {
        TeacherClassLesson::where('teacher_class_lesson_id', $tclId)->delete();
    }
    public function update($request) {
        TeacherClassLesson::find($request->tclId)->update([
            'start_date'=>$request->startDate,
            'end_date'=>$request->endDate,
            'weekly_count'=>$request->weeklyCount,
        ]);
    }

}
