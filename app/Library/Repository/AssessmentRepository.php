<?php

namespace App\Library\Repository;

use App\Models\AcademicSemester;
use App\Models\AcademicTerm;
use App\Models\AcademicYear;
use App\Models\ElectiveGrade;
use App\Models\LearningArea;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Exception;
use App\Library\Helper\AcademicHelper;
use App\Models\LogAssessment;
use App\Models\SummativeAssessment;
use App\Models\SummativeAssessmentData;
use App\Models\FormativeAssessment;
use App\Models\FormativeAssessmentData;
use App\Models\Timetable;
use App\Models\ScoreTemplate;
use App\Models\ScoreTemplateGrade;
use App\Models\ScoreTemplateData;
use App\Models\ElectiveGradeStudent;
use App\Library\Repository\MobileNotificationRepository;
use Illuminate\Support\Facades\Log;
use App\Models\SettingsAssessment;
use App\Models\User;
use Error;
use Illuminate\Support\Facades\Auth;

class AssessmentRepository
{

    protected AcademicHelper $ah;
    protected MobileNotificationRepository $mobileNotification;

    public function __construct(AcademicHelper $ah, MobileNotificationRepository $mobileNotification)
    {
        $this->ah = $ah;
        $this->mobileNotification = $mobileNotification;
    }

    public function deleteSummative($id)
    {
        $ah = new AcademicHelper();
        $academicYearId = $ah->academicYear();
        $branchId = $ah->currentBranch();

        DB::beginTransaction();
        try {
            $assessment = SummativeAssessment::find($id);

            $gradeName = 'N/A';

            if (isset($assessment->grade->grade_name))
                $gradeName = $assessment->grade->grade_name;

            $logData = 'Assessmen:' . $assessment->assessment_name . ' Subject: ' . $assessment->subject->subject_name . ' Grade: ' . $gradeName;

            $assessment->data()->delete();
            $assessment->delete();

            //-- save log----
            $log = new LogAssessment();
            $log->academic_year_id = $academicYearId;
            $log->branch_id = $branchId;
            $log->type = 'Delete Summative Assessment';
            $log->data = $logData;
            $log->user_id = \Auth::user()->id;
            $log->save();

            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    public function deleteFormative($id)
    {
        $ah = new AcademicHelper();
        $academicYearId = $ah->academicYear();
        $branchId = $ah->currentBranch();

        DB::beginTransaction();
        try {
            $assessment = FormativeAssessment::find($id);
            $logData = 'Assessmen:' . $assessment->assessment_name . ' Subject: ' . $assessment->subject->subject_name . ' Grade: ' . $assessment->grade->grade_name;

            $assessment->data()->delete();
            $assessment->delete();

            //-- save log----
            $log = new LogAssessment();
            $log->academic_year_id = $academicYearId;
            $log->branch_id = $branchId;
            $log->type = 'Delete Formative Assessment';
            $log->data = $logData;
            $log->user_id = \Auth::user()->id;
            $log->save();

            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    public function updateSummative($data)
    {
        $ah = new AcademicHelper();
        $academicYearId = $ah->academicYear();
        $branchId = $ah->currentBranch();

        DB::beginTransaction();
        try {
            $assessment = SummativeAssessment::find($data['id']);
            $logData = 'Assessmen:' . $assessment->assessment_name . ' Subject: ' . $assessment->subject->subject_name . ' Grade: ' . $assessment->grade->grade_name;
            $maxScore = $assessment->max_score;
            $arr = explode('|*|', $data['data']);
            foreach ($assessment->data as $key => $student) {
                $isFound = 0;

                foreach ($arr as $key => $item) {
                    if ($item != '') {
                        $tempArr = explode('/*/', $item);
                        $studentId = $tempArr[0];
                        $dataId = $tempArr[1];
                        $score = isset($tempArr[2]) && $tempArr[2] !== '' ? (int)$tempArr[2] : null;

                        if ($dataId == $student->data_id) {
                            $isFound++;
                            $scorePercentage = round((100 / $maxScore) * $score, 0);
                            SummativeAssessmentData::where('data_id', $dataId)->update(['score' => $score, 'score_percentage' => $scorePercentage]);
                            continue;
                        }
                    }
                }

                if ($isFound == 0) {
                    SummativeAssessmentData::where('data_id', $student->data_id)->delete();
                }
            }

            //-- save log----
            LogAssessment::create([
                'academic_year_id' => $academicYearId,
                'branch_id' => $branchId,
                'type' => 'Update Summative Assessment',
                'data' => $logData,
                'user_id' => \Auth::user()->id
            ]);

            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    public function updateFormative($data)
    {
        $ah = new AcademicHelper();
        $academicYearId = $ah->academicYear();
        $branchId = $ah->currentBranch();

        DB::beginTransaction();
        try {
            $assessment = FormativeAssessment::find($data['id']);
            $logData = 'Assessmen:' . $assessment->assessment_name . ' Subject: ' . $assessment->subject->subject_name . ' Grade: ' . $assessment->grade->grade_name;

            $arr = explode('|*|', $data['data']);
            foreach ($assessment->data as $key => $student) {
                $isFound = 0;

                foreach ($arr as $key => $item) {
                    if ($item != '') {

                        $tempArr = explode('/*/', $item);
                        $studentId = $tempArr[0];
                        $dataId = $tempArr[1];

                        $t1 = $tempArr[2];
                        $t2 = $tempArr[3];
                        $t3 = $tempArr[4];
                        $t4 = $tempArr[5];

                        if ($dataId == $student->assessment_data_id) {
                            $isFound++;
                            FormativeAssessmentData::where('assessment_data_id', $dataId)->update([
                                't1' => $t1,
                                't2' => $t2,
                                't3' => $t3,
                                't4' => $t4
                            ]);
                            continue;
                        }
                    }
                }
                if ($isFound == 0) {
                    FormativeAssessmentData::where('assessment_data_id', $student->assessment_data_id)->delete();
                }
            }
            //-- save log----
            $log = new LogAssessment();
            $log->academic_year_id = $academicYearId;
            $log->branch_id = $branchId;
            $log->type = 'Update Formative Assessment';
            $log->data = $logData;
            $log->user_id = \Auth::user()->id;
            $log->save();

            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }



    //---- USER functions ------------

    public function saveSummativeData($data)
    {
        $assessment = SummativeAssessment::find($data['assessmentId']);
        $scoreArr = explode("/*/", $data['scoreData']);

        $templateData = SummativeAssessmentData::where('assessment_id', $data['assessmentId'])
            ->where('type_id', '>', 0)
            ->first();
        $maxScore = $assessment->max_score;
        foreach ($scoreArr as $key => $score) {
            if ($score != '') {
                $tempArr = explode('|', $score);
                // dd($tempArr);

                $dataId         = $tempArr[0];
                $currentScore   = $tempArr[1];
                $studentId      = $tempArr[2];
                $currentComment = $tempArr[3];

                if (!is_numeric($currentScore)) {
                    $currentScore = null;
                }
                // Remove Data if Null ,toadjust Report card Calculation
                if ($currentScore == null) {
                    SummativeAssessmentData::find($dataId)->delete();
                } else {
                    // if($tempArr[0] > 0) {
                    $scorePercentage = round((100 / $maxScore) * $currentScore, 0);
                    SummativeAssessmentData::updateOrCreate(
                        [
                            'data_id' => $dataId
                        ],
                        [
                            'assessment_id'     => $data['assessmentId'],
                            'student_id'        => $studentId,
                            'type_id'           => $templateData->type_id,
                            'type_title'        => $templateData->type_title,
                            'type_percentage'   => $templateData->type_percentage,
                            'score'             => $currentScore,
                            'score_percentage'  => $scorePercentage,
                            'comment'           => $currentComment
                        ]
                    );
                    // }
                    if ($currentScore != '') {
                        $studentInfo = User::find($studentId);
                        // $this->mobileNotification->sendSingle([
                        //     'title'         => 'New Score',
                        //     'message'       => $studentInfo->name . " has a new Assessment Score on the  Subject",
                        //     'student'       => $studentId,
                        //     'type'          => 'assessment',
                        //     'user_type'     => $studentInfo->user_type
                        // ]);
                        $this->mobileNotification->sendRealTime([
                            'title'         => 'New Score',
                            'message'       => $studentInfo->name . " has a new Assessment Score on the  Subject",
                            'student'       => $studentId,
                            'type'          => 'assessment',
                            'user_type'     => $studentInfo->user_type
                        ]);
                    }
                }
            }
        }
    }

    public function saveFormativeData($data)
    {
        $assessment = FormativeAssessment::find($data['assessmentId']);
        $scoreArr = explode("|*|", $data['scoreData']);
        $templateData = ScoreTemplateData::find($assessment->formative_type_id);

        $typeId = null;
        $percentage = null;

        if ($templateData) {
            $typeId = $templateData->data_id;
            $percentage = $templateData->percentage;
        }

        //-- Delete all data--
        $assessment->data()->delete();

        foreach ($scoreArr as $key => $score) {
            if ($score != '') {
                $arr = explode('/*/', $score);
                $newAssessment = FormativeAssessmentData::create(
                    [
                        'formative_assessment_id'   => $data['assessmentId'],
                        'student_id'                => $arr[0],
                        't1'                        => $arr[1],
                        't2'                        => $arr[2],
                        't3'                        => $arr[3],
                        't4'                        => $arr[4],
                        'type_id'                   => $typeId,
                        'type_percentage'           => $percentage,
                        'comment'                   => $arr[5]
                    ]
                );
                $studentInfo = User::find($arr[0]);
                // $this->mobileNotification->sendSingle([
                //     'title'         => 'New Score',
                //     'message'       => $studentInfo->name . " has a new Assessment Score on the  Subject",
                //     'student'       => $arr[0],
                //     'type'          => 'assessment',
                //     'user_type'     => $studentInfo->user_type
                // ]);
                $this->mobileNotification->sendRealTime([
                    'title'         => 'New Score',
                    'message'       => $studentInfo->name . " has a new Assessment Score on the  Subject",
                    'student'       => $arr[0],
                    'type'          => 'assessment',
                    'user_type'     => $studentInfo->user_type
                ]);
            }
        }
    }
    public function saveFormativeSingleData($data)
    {
        $assessment = FormativeAssessment::find($data['assessmentId']);
        $scoreArr = explode("|*|", $data['scoreData']);
        $templateData = ScoreTemplateData::find($assessment->formative_type_id);

        $typeId = null;
        $percentage = null;

        if ($templateData) {
            $typeId = $templateData->data_id;
            $percentage = $templateData->percentage;
        }

        //-- Delete all data--
        // $assessment->data()->delete();

        foreach ($scoreArr as $key => $score) {
            if ($score != '') {
                $arr = explode('/*/', $score);
                // dd( $arr);
                $newAssessment = FormativeAssessmentData::updateOrCreate(
                    [
                        'formative_assessment_id'   => $data['assessmentId'],
                        'student_id'                => $arr[0],
                    ],
                    [
                        't1'                        => $arr[1],
                        't2'                        => $arr[2],
                        't3'                        => $arr[3],
                        't4'                        => $arr[4],
                        'type_id'                   => $typeId,
                        'type_percentage'           => $percentage,
                        'comment'                   => $arr[5]
                    ]
                );
            }
        }
    }
    public function deleteSummativeAssessment($assessmentId)
    {
        $assessment = SummativeAssessment::find($assessmentId);
        $assessment->data->delete();
        $assessment->delete();
    }

    public function deleteFormativeAssessment($assessmentId)
    {
        $assessment = FormativeAssessment::find($assessmentId);
        $assessment->data->delete();
        $assessment->delete();
    }

    public function updateSummativeTitle($data)
    {

        $optData = ScoreTemplateData::find($data['opt']);

        $assessment = SummativeAssessment::find($data['assessmentId']);
        $assessment->assessment_name = $data['title'];
        $assessment->comment = $data['comment'];
        $assessment->date = $data['date'];
        if ($data['strand'] != null) {
            $assessment->strand = $data['strand'];
        }
        $assessment->skill = $data['skill'];

        if ($optData) {
            $assessment->template_id = $optData->summative_template_id;

            SummativeAssessmentData::where('assessment_id', $data['assessmentId'])->update([
                'type_percentage'   => $optData->percentage,
                'type_id'           => $optData->data_id,
                'type_title'        => $optData->title,
            ]);
        } else {
            $assessment->formative_type_id = null;
            $assessment->formative_type_percentage = null;

            FormativeAssessmentData::where('formative_assessment_id', $data['assessmentId'])->update([
                'type_percentage'   => null,
                'type_id'           => null,
                'type_title'        => null
            ]);
        }

        $assessment->save();
    }

    public function updateFormativeTitle($data)
    {
        DB::beginTransaction();
        try {
            $optData = ScoreTemplateData::find($data['opt']);

            $assessment = FormativeAssessment::find($data['assessmentId']);
            $assessment->assessment_name = $data['title'];
            $assessment->comment = $data['comment'];
            $assessment->date = $data['date'];
            $assessment->strand = $data['strand'];
            $assessment->skill = $data['skill'];

            if ($optData) {
                $assessment->formative_type_id = $optData->data_id;
                $assessment->formative_type_percentage = $optData->percentage;

                FormativeAssessmentData::where('formative_assessment_id', $data['assessmentId'])->update([
                    'type_percentage'   => $optData->percentage,
                    'type_id'           => $optData->data_id
                ]);
            } else {
                $assessment->formative_type_id = null;
                $assessment->formative_type_percentage = null;

                FormativeAssessmentData::where('formative_assessment_id', $data['assessmentId'])->update([
                    'type_percentage'   => null,
                    'type_id'           => null
                ]);
            }

            $assessment->save();


            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();
            throw new Exception($e);
        }
    }

    public function gradeOptions($data)
    {
        $rd = '<option value="0">None</option>';
        // $timetable = Timetable::find($timetableId);
        $templateInfo = ScoreTemplate::leftJoin(
            'academic_summative_score_template_grades',
            'academic_summative_score_template_grades.summative_template_id',
            'academic_summative_score_template.summative_template_id'
        )
            ->leftJoin(
                'academic_summative_score_template_data',
                'academic_summative_score_template_data.summative_template_id',
                'academic_summative_score_template.summative_template_id'
            )
            ->where('academic_summative_score_template.subject_id', $data['subjectId'])
            ->where('academic_summative_score_template_grades.grade_id', $data['gradeId'])
            ->get();

        foreach ($templateInfo as $key => $info) {
            $rd .= '<option value="' . $info->data_id . '" data-percentage="' . $info->percentage . '" data-is-final-exam="' . $info->is_final_exam_percentage . '">' . $info->title . ' (' . $info->percentage . '%)</option>';
        }
        return $rd;
    }

     public function gradeOptionsAndLearningAreas($data)
    {
        $rd = '<option value="0">None</option>';

        // $timetable = Timetable::find($timetableId);
        $templateInfo = ScoreTemplate::leftJoin(
            'academic_summative_score_template_grades',
            'academic_summative_score_template_grades.summative_template_id',
            'academic_summative_score_template.summative_template_id'
        )
            ->leftJoin(
                'academic_summative_score_template_data',
                'academic_summative_score_template_data.summative_template_id',
                'academic_summative_score_template.summative_template_id'
            )
            ->where('academic_summative_score_template.subject_id', $data['subjectId'])
            ->where('academic_summative_score_template_grades.grade_id', $data['gradeId'])
            ->get();

        foreach ($templateInfo as $key => $info) {
            $rd .= '<option value="' . $info->data_id . '" data-percentage="' . $info->percentage . '">' . $info->title . ' (' . $info->percentage . '%)</option>';
        }

        $learningAreas = DB::table('academic_learning_areas')
            ->whereJsonContains('grades', $data['gradeId']) // Checks if the JSON contains the gradeId
            ->get();
            
        $learningAreas = $learningAreas->map(function ($item) {
            return [
                'id' => $item->id,
                'name' => $item->name
            ];
        });
        return [
            'gradeOptions' => $rd,
            'learningAreas' => $learningAreas
        ];

    }


    public function createSummative($data)
    {
        $rd = '';
        $dataArray = [];
        if (isset($data['same_subject_grades'])) {
            $dataArray = $data['same_subject_grades'];
            array_push($dataArray, $data['na_grade_id']);
        } else {
            array_push($dataArray, $data['na_grade_id']);
            // dd($dataArray);
        }
        $currentSemester = AcademicSemester::where('is_default', 1)
            ->where('branch_id',  $this->ah->currentBranch())
            ->first();
        // Check Term End Assessment Exit for same grade with same semester 

        foreach ($dataArray as $gradeId) {
            $uid = uniqid();
            $template = ScoreTemplateGrade::leftJoin(
                'academic_summative_score_template',
                'academic_summative_score_template.summative_template_id',
                'academic_summative_score_template_grades.summative_template_id'
            )
                ->where('academic_summative_score_template.subject_id', $data['na_subject_id'])
                ->where('academic_summative_score_template_grades.grade_id', $gradeId)
                ->where('academic_summative_score_template.academic_year_id', $this->ah->academicYear())
                ->where('academic_summative_score_template.branch_id', $this->ah->currentBranch())
                ->first();
            if ($data['is_final_exam'] == 1) {
                $checkAssessment = SummativeAssessment::where('branch_id', $this->ah->currentBranch())
                    ->where('academic_year_id', $this->ah->academicYear())
                    ->where('template_id', $template->summative_template_id)
                    ->where('grade_id', $gradeId)
                    ->whereNot('strand', $data['strand'])
                    ->whereBetween('date', [$currentSemester->start_date, $currentSemester->end_date])
                    ->where('is_final_exam', 1)
                    ->get();
                    
                if ($checkAssessment->isNotEmpty()) {
                    throw new Error("Final & Midterm Strand is Already Created for this Term . Make Sure , You choose the right Strand");
                }
                $checkWrongInputStrand = SummativeAssessment::where('branch_id', $this->ah->currentBranch())
                    ->where('academic_year_id', $this->ah->academicYear())
                    ->where('template_id', $template->summative_template_id)
                    ->where('grade_id', $gradeId)
                    ->where('strand', $data['strand'])
                    ->whereBetween('date', [$currentSemester->start_date, $currentSemester->end_date])
                    ->where('is_final_exam',0)
                    ->get();
                //    dd($checkWrongInputStrand);
                if ($checkWrongInputStrand->isNotEmpty()) {
                    throw new Error("Final & Midterm Strand Cannot Create on This Strand . Make Sure , You choose the right Strand");
                }
            } else {
                $checkAssessment = SummativeAssessment::where('branch_id', $this->ah->currentBranch())
                    ->where('academic_year_id', $this->ah->academicYear())
                    ->where('template_id', $template->summative_template_id)
                    ->where('grade_id', $gradeId)
                    ->where('strand', $data['strand'])
                    ->whereBetween('date', [$currentSemester->start_date, $currentSemester->end_date])
                    ->where('is_final_exam', 1)
                    ->get();
                if ($checkAssessment->isNotEmpty()) {
                    throw new Error("Only Final and Midterm Impacts can be created in the selected strand.");
                }
                // dd($checkAssessment);
            }
        }

        // dd('ok');
        DB::beginTransaction();
        foreach ($dataArray as $gradeId) {
            try {

                $uid = uniqid();
                $template = ScoreTemplateGrade::leftJoin(
                    'academic_summative_score_template',
                    'academic_summative_score_template.summative_template_id',
                    'academic_summative_score_template_grades.summative_template_id'
                )
                    ->where('academic_summative_score_template.subject_id', $data['na_subject_id'])
                    ->where('academic_summative_score_template_grades.grade_id', $gradeId)
                    ->where('academic_summative_score_template.academic_year_id', $this->ah->academicYear())
                    ->where('academic_summative_score_template.branch_id', $this->ah->currentBranch())
                    ->first();
                $assessment = SummativeAssessment::create([
                    'branch_id'         => $this->ah->currentBranch(),
                    'academic_year_id'  => $this->ah->academicYear(),
                    'assessment_name'   => $data['title'],
                    'teacher_id'        => \Auth::user()->id,
                    'template_id'       => $template->summative_template_id,
                    'subject_id'        => $data['na_subject_id'],
                    'grade_id'          => $gradeId,
                    'max_score'         => $data['max-score'],
                    'percentage'        => $template->percentage,
                    'strand'            => $data['strand'],
                    'date'              => $data['date'],
                    'uid'               => $uid,
                    'comment'           => $data['comment'],
                    'is_final_exam'     => $data['is_final_exam'],
                ]);
                if ($assessment) {
                    $students = ElectiveGradeStudent::where('grade_id', $assessment->grade_id)->get();
                    $type = ScoreTemplateData::find($data['option']);

                    foreach ($students as $key => $student) {
                        $item = SummativeAssessmentData::create([
                            'assessment_id'     => $assessment->assessment_id,
                            'student_id'        => $student->student_id,
                            'type_id'           => $data['option'],
                            'type_title'        => $type->title,
                            'type_percentage'   => $type->percentage
                        ]);

                        //--
                        //-- send mobile notification to students
                        if ($item) {
                            // $this->mobileNotification->sendSingle([
                            //     'title'         => 'New Summative Assessment',
                            //     'message'       => $student->name . ' has new summative assessment on the subject. ',
                            //     'student'       => $student->student_id,
                            //     'type'          => 'assessment',
                            //     'user_type'     => 'student',
                            // ]);
                            $this->mobileNotification->sendRealTime([
                                'title'         => 'New Summative Assessment',
                                'message'       => $student->name . ' has new summative assessment on the subject. ',
                                'student'       => $student->student_id,
                                'type'          => 'assessment',
                                'user_type'     => 'student',
                            ]);
                        }
                    }

                    $rd = 'ok|summative|' . $assessment->assessment_id;
                }
                DB::commit();
            } catch (Exception $e) {
                $rd = 'Failed to create assessment.  ' . $e->getMessage();
                DB::rollBack();
            }
        }
        return $rd;
    }

    public function createFormative($data)
    {
        $rd = '';

        DB::beginTransaction();
        try {
            $uid = uniqid();
            // $timetable = Timetable::find($data['timetable_id']);

            // $template = ScoreTemplateGrade::
            //             leftJoin(
            //                         'academic_summative_score_template',
            //                         'academic_summative_score_template.summative_template_id',
            //                         'academic_summative_score_template_grades.summative_template_id'
            //                     )
            //           ->where('academic_summative_score_template.subject_id', $data['na_subject_id'])
            //           ->where('academic_summative_score_template_grades.grade_id', $data['na_grade_id'])
            //           ->where('academic_summative_score_template.academic_year_id', $this->ah->academicYear())
            //           ->where('academic_summative_score_template.branch_id', $this->ah->currentBranch())
            //           ->first();

            // dd($template);
            // dd($data['option']);

            $typeId = null;
            $percentage = null;

            $type = ScoreTemplateData::find($data['option']);

            if ($type) {
                // $typeId     = $type->summative_template_id;
                $typeId = $data['option'];
                $percentage = $type->percentage;
            }

            $assessment = FormativeAssessment::create([
                'branch_id'                 => $this->ah->currentBranch(),
                'academic_year_id'          => $this->ah->academicYear(),
                'assessment_name'           => $data['title'],
                'teacher_id'                => \Auth::user()->id,
                'formative_type_id'         => $typeId,
                'formative_type_percentage' => $percentage,
                'subject_id'                => $data['na_subject_id'],
                'grade_id'                  => $data['na_grade_id'],
                'skill'                     => $data['lifeSkill'],
                'strand'                    => $data['strand'],
                'date'                      => $data['date'],
                'uid'                       => $uid,
                'comment'                   => $data['comment'],
            ]);

            if ($assessment) {
                $students = ElectiveGradeStudent::where('grade_id', $assessment->grade_id)->get();
                // $type = ScoreTemplateData::find($data['option']);

                foreach ($students as $key => $student) {
                    $item = FormativeAssessmentData::create([
                        'formative_assessment_id'   => $assessment->formative_assessment_id,
                        'student_id'                => $student->student_id,
                        'type_id'                   => $typeId,
                        'type_percentage'           => $percentage,
                    ]);

                    //--
                    //-- send mobile notification to students
                    if ($item) {
                        // $this->mobileNotification->sendSingle([
                        //     'title'     => 'New Summative Assessment',
                        //     'message'   => $student->name . ' has new formative assessment on subject.',
                        //     'student'   => $student->student_id,
                        //     'type'          => 'assessment',
                        //     'user_type'     => 'student',
                        // ]);
                        $this->mobileNotification->sendRealTime([
                            'title'     => 'New Summative Assessment',
                            'message'   => $student->name . ' has new formative assessment on subject.',
                            'student'   => $student->student_id,
                            'type'          => 'assessment',
                            'user_type'     => 'student',
                        ]);
                    }
                }
                $rd = 'ok|formative|' . $assessment->formative_assessment_id;
            }
            DB::commit();
        } catch (Exception $e) {
            $rd = 'Failed to create assessment.  ' . $e->getMessage();
            DB::rollBack();
        }
        return $rd;
    }

    public function lifeSkills($data)
    {
        $ah = new AcademicHelper();
        $term = $ah->currentSemester();
        $rd = '<option value="0">None</option>';
        // $timetable = Timetable::find($timetableId);
        $lifeSkillInfo = SettingsAssessment::leftJoin('skills_strands', 'skills_strands.skill_strand_id', 'settings_assessment.skill_strand_id')
            ->where('grade_id', $data['gradeId'])
            ->where('term', $term)
            ->where('skills_strands.type', 'skill')
            ->get();

        foreach ($lifeSkillInfo as $key => $info) {
            $rd .= '<option value="' . $info->skill_strand_id . '">' . $info->value . '</option>';
        }
        return $rd;
    }

    public function strands($data)
    {
        $ah = new AcademicHelper();
        $term = $ah->currentSemester();
        $rd = '<option value="0">None</option>';
        // $timetable = Timetable::find($timetableId);
        $strandInfo = SettingsAssessment::leftJoin('skills_strands', 'skills_strands.skill_strand_id', 'settings_assessment.skill_strand_id')
            ->where('grade_id', $data['gradeId'])
            // ->where('term', $term)
            ->where('skills_strands.type', 'strand')
            ->get();

        foreach ($strandInfo as $key => $info) {
            $rd .= '<option value="' . $info->skill_strand_id . '"  data-is-final-exam="' . $info->is_final_mid_exam . '">' . $info->value . '</option>';
        }
        return $rd;
    }
    public function getSameSubjectGrades($data)
    {
        $ah = new AcademicHelper();

        $term = $ah->currentSemester();
        $rd = '';
        $subjects = Timetable::where('academic_year_id', $ah->academicYear())
            ->where('branch_id', $ah->currentBranch())
            ->where('user_id', Auth::user()->id)
            ->where('subject_id', $data['subjectId'])
            ->whereNot('grade_id', $data['gradeId'])
            ->groupBy('grade_id')
            ->get();
        // dd(Auth::user()->id);
        foreach ($subjects as $key => $info) {
            $rd .= '<option value="' . $info->elective_grade->grade_id . '">' . $info->elective_grade->grade_name . '</option>';
        }
        return $rd;
    }


    // functions for Kg Assessments
    public function createKgAssessment($data)
    {
      
        $result = null;
        $lockedTerms = $this->getLockedTerms();
        $isLocked = false;

        foreach ($lockedTerms as $key => $lt) {
            $startDate = new \Datetime($lt->start_date);
            $endDate = new \Datetime($lt->end_date);
            $aDate = new \Datetime($data['date']);

            if ($aDate >= $startDate && $aDate <= $endDate) {
                $isLocked = true;
                break;
            }
        }
       
        if ($isLocked) {
            throw new Exception('Selected date is locked for assessment creation!');
        }

        DB::beginTransaction();
        try {
            $learningArea = LearningArea::find($data['learning_area']);
            if (!$learningArea) {
                throw new Exception('Invalid learning area selected.');
            }
         
            $typeId = null;
            $percentage = null;

            $type = ScoreTemplateData::find($data['option']);

            if ($type) {
                $typeId = $data['option'];
                $percentage = $type->percentage;
            }

            $assessment = FormativeAssessment::create([
                'branch_id' => $this->ah->currentBranch(),
                'academic_year_id' => $this->ah->academicYear(),
                'assessment_name' => $learningArea->name,
                'teacher_id' => \Auth::user()->id,
                'formative_type_id' => $typeId,
                'formative_type_percentage' => $percentage,
                'subject_id' => $data['na_subject_id'],
                'grade_id' => $data['na_grade_id'],
                'date' => $data['date'],
                'uid' => $learningArea->uid
            ]);
           
            if ($assessment) {
                $students = ElectiveGradeStudent::where('grade_id', $assessment->grade_id)->get();
                
                $studentData = $students->map(function ($student) use ($assessment, $typeId, $percentage) {
                    return [
                        'formative_assessment_id' => $assessment->formative_assessment_id,
                        'student_id' => $student->student_id,
                        'type_id' => $typeId,
                        'type_percentage' => $percentage,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ];
                })->toArray();
            
                FormativeAssessmentData::insert($studentData);
                $result = $assessment->formative_assessment_id;
               
            }
            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();
            \Log::error('Assessment creation failed: ' . $e->getMessage());
            throw new Exception('Failed to create assessment. Please try again later.');
        }
        return $result;


    }

    public function updateKGFormativeTitle($data)
    {
        DB::beginTransaction();
        try {
            $optData = ScoreTemplateData::find($data['opt']);

            $learningArea = LearningArea::find($data['title']);

            $assessment = FormativeAssessment::find($data['assessmentId']);
            $assessment->assessment_name = $learningArea->name;
            $assessment->uid = $learningArea->uid;
            $assessment->date = $data['date'];

            if ($optData) {
                $assessment->formative_type_id = $optData->data_id;
                $assessment->formative_type_percentage = $optData->percentage;

                FormativeAssessmentData::where('formative_assessment_id', $data['assessmentId'])->update([
                    'type_percentage' => $optData->percentage,
                    'type_id' => $optData->data_id
                ]);
            } else {
                $assessment->formative_type_id = null;
                $assessment->formative_type_percentage = null;

                FormativeAssessmentData::where('formative_assessment_id', $data['assessmentId'])->update([
                    'type_percentage' => null,
                    'type_id' => null
                ]);
            }

            $assessment->save();
            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();
            throw new Exception($e);
        }

    }

    public function getCurrentSemester($termId)
    {
        if (!is_null($termId)) {
            if ($termId === 'all') {
                return AcademicYear::firstWhere('is_active', 1) ?? $this->ah->currentTerm();
            } else {
                return AcademicSemester::find($termId) ?? $this->ah->currentTerm();
            }
        }
        return $this->ah->currentTerm();
    }

    public function getAssessments($currentSemester, $gradeId)
    {
        return FormativeAssessment::where('academic_year_id', $this->ah->academicYear())
            ->whereBetween('date', [$currentSemester->start_date, $currentSemester->end_date])
            ->where('branch_id', $this->ah->currentBranch())
            ->where('grade_id', $gradeId)
            ->where('teacher_id', \Auth::id())
            ->orderBy('uid', 'desc')
            ->orderBy('date', 'desc')
            ->get();
    }

    public function getStudents($gradeId)
    {
        return ElectiveGradeStudent::leftJoin('student_information', 'academic_elective_grade_students.student_id', '=', 'student_information.id')
            ->where('grade_id', $gradeId)
            ->where('student_status', 1)
            ->orderBy('student_information.id')
            ->get();
    }

    public function getLockedTerms()
    {
        return AcademicTerm::where('academic_year_id', $this->ah->academicYear())
            ->where('branch_id', $this->ah->currentBranch())
            ->where('is_locked', 1)
            ->get();
    }

    public function getGradeInfo($gradeId)
    {
        return ElectiveGrade::find($gradeId);
    }

    public function saveKgFormative($data)
    {
        foreach ($data['dataToSend'] as $student) {
            $assessment = FormativeAssessmentData::find($student['assessment_id']);

            if ($assessment) {

                $updateData = [
                    't1'      => '',
                    't2'      => '',
                    't3'      => '',
                    't4'      => '',
                    'comment' => $student['comment']
                ];

                switch ($student['mark']) {
                    case 'Strong':
                        $updateData['t1'] = '✔';
                        break;
                    case 'Satisfactory':
                        $updateData['t2'] = '✔';
                        break;
                    case 'Needs Improvement':
                        $updateData['t3'] = '✔';
                        break;
                    case 'Not Taught':
                        $updateData['t4'] = '✔';
                        break;
                
                }

                $assessment->update($updateData);
              
            }
        }
    }

}
