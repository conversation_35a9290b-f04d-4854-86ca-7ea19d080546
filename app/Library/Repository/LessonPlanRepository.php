<?php

namespace App\Library\Repository;

use App\Models\LessonPlan;
use App\Models\AcademicWeek;
use App\Models\LessonPlanComment;
use Illuminate\Support\Facades\DB;
use App\Library\Helper\AcademicHelper;
use App\Models\LessonPlanCommentReply;
use Illuminate\Validation\ValidationException;
use Illuminate\Database\Eloquent\ModelNotFoundException;

use App\Library\Repository\EmailRepository;

class LessonPlanRepository {

    protected AcademicHelper $ah;
    protected EmailRepository $emailRepository;

    public function __construct(AcademicHelper $ah, EmailRepository $email) {
        $this->ah = $ah;
        $this->email = $email;
    }

    public function create($data) {
        $currentDate = new \DateTime();
        $dt_min = new \DateTime("Friday");  //Find last day of the week
        $result = $dt_min->format('Y-m-d H:i:s');
        $weekCurent = $currentDate->format("W");
        $weekDeadline = $dt_min->format("W");

        if ($weekDeadline > $weekCurent) {
            $date = strtotime(date("Y-m-d H:i:s", strtotime($result)) . " -6 day");
            $finalDeadline = \DateTime::createFromFormat('U', $date);
        } else {
            $finalDeadline = $dt_min;
        }

        $toBeAdded = $finalDeadline->format("Y-m-d H:i:s");
        $finalDeadline = date("Y-m-d H:i:s",strtotime('+18 hours', strtotime($toBeAdded)));

        $week = AcademicWeek::where('academic_year_id', $this->ah->academicYear())
                            ->where('branch_id', $this->ah->currentBranch())
                            ->where('week', $data['week'])
                            ->first();
        if(array_key_exists('opt_confident',$data)){
            $chkConfident = "1";
        }else{$chkConfident = "0";}
        if(array_key_exists('opt_reflective',$data)){
            $chkReflective = "1";
        }else{$chkReflective = "0";}
        if(array_key_exists('opt_innovative',$data)){
            $chkInnovative = "1";
        }else{$chkInnovative = "0";}
        if(array_key_exists('opt_engaged',$data)){
            $chkEngaged = "1";
        }else{$chkEngaged = "0";}
        if(array_key_exists('opt_responsible',$data)){
            $chkResponsible = "1";
        }else{$chkResponsible = "0";}

        foreach ($data['classroom'] as $key => $grade) {
            LessonPlan::create([
                'academic_year_id'          => $this->ah->academicYear(),
                'uid'                       => uniqid(),
                'branch_id'                 => $this->ah->currentBranch(),
                'user_id'                   => \Auth::user()->id,
                'lesson_plan_week'          => $data['week'],
                'lesson_plan_date'          => $week->range,
                'lesson_plan_date_detail'   => $data['date'],
                'subject_id'                => $data['subject'],
                'grade_id'                  => $grade,
                'lesson_plan_unit'          => $data['unit'],
                'day_period'                => $data['day_period'],
                'unit_topic_subtopic'       => $data['unit_topic_subtopic'],
                'learning_objectives'       => $data['learning_objectives'],
                'activities_differentiation'=> $data['activities_differentiation'],
                'independent_pair'          => $data['independent_pair'],
                'evidence_assessment'       => $data['evidence_assessment'],
                'materials'                 => $data['materials'],
                'lesson_plan_link'          => $data['link'],
                'opt_confident'             => $chkConfident,
                'opt_reflective'            => $chkReflective,
                'opt_innovative'            => $chkInnovative,
                'opt_engaged'               => $chkEngaged,
                'opt_responsible'           => $chkResponsible,
                'deadline'                  => $finalDeadline
            ]);
        }
    }

    public function update($data) {
        if(array_key_exists('opt_confident',$data)){
            $chkConfident = "1";
        }else{$chkConfident = "0";}
        if(array_key_exists('opt_reflective',$data)){
            $chkReflective = "1";
        }else{$chkReflective = "0";}
        if(array_key_exists('opt_innovative',$data)){
            $chkInnovative = "1";
        }else{$chkInnovative = "0";}
        if(array_key_exists('opt_engaged',$data)){
            $chkEngaged = "1";
        }else{$chkEngaged = "0";}
        if(array_key_exists('opt_responsible',$data)){
            $chkResponsible = "1";
        }else{$chkResponsible = "0";}
        $plan = LessonPlan::find($data['plan_id']);
        $plan->lesson_plan_unit             = $data['unit'];
        $plan->lesson_plan_date_detail      = $data['date'];
        $plan->day_period                   = $data['day_period'];
        $plan->unit_topic_subtopic          = $data['unit_topic_subtopic'];
        $plan->learning_objectives          = $data['learning_objectives'];
        $plan->activities_differentiation   = $data['activities_differentiation'];
        $plan->independent_pair             = $data['independent_pair'];
        $plan->evidence_assessment          = $data['evidence_assessment'];
        $plan->materials                    = $data['materials'];
        $plan->lesson_plan_link             = $data['link'];
        $plan->opt_confident                = $chkConfident;
        $plan->opt_reflective               = $chkReflective;
        $plan->opt_innovative               = $chkInnovative;
        $plan->opt_engaged                  = $chkEngaged;
        $plan->opt_responsible              = $chkResponsible;
        $plan->save();
    }

    public function delete($planId) {
        $plan = LessonPlan::where('user_id', \Auth::user()->id)
                            ->where('lesson_plan_id', $planId)
                            ->where('academic_year_id', $this->ah->academicYear())
                            ->where('branch_id', $this->ah->currentBranch())
                            ->first();

        $now = new \DateTime();
        $deadline = new \DateTime($plan->deadline);
        if ($now < $deadline) {
            $plan->delete();
        }
    }

    public function postComment($data) {
        $lessonPlan = LessonPlan::where('uid', $data['uid'])->first();
        LessonPlanComment::create([
            'lesson_plan_id'    => $lessonPlan->lesson_plan_id,
            'user_id'   => \Auth::user()->id,
            'uid'   => uniqid(),
            'comment'   => $data['comment'],
            // 'parent_comment_id' => 0
        ]);

        $messageContent = 'Dear '.$lessonPlan->user->name.'. You have new comment on lesson plan created on '.$lessonPlan->created_at.'!<br>'.
                          'Please log in to your SIS account to check and reply to the comment';

        $this->email->sendEmail([
            'receiver' => $lessonPlan->user->email,
            'subject' => 'New Lesson Plan Comment: '.$lessonPlan['lesson_plan_unit'],
            'content' => $messageContent,
            'view' => 'lesson_plan_reply'
        ]);
    }

    public function postCommentReply($data) {
        // dd($data);
        $lessonPlan = LessonPlan::where('uid', $data['uid'])->first();
        LessonPlanCommentReply::create([
            'comment_id'    => $data['commentId'],
            'user_id'   => \Auth::user()->id,
            'text'   => $data['comment'],
        ]);

        $messageContent = 'Dear '.$lessonPlan->user->name.'. You have new comment on lesson plan created on '.$lessonPlan->created_at.'!<br>'.
                          'Please log in to your SIS account to check and reply to the comment';

        $this->email->sendEmail([
            'receiver' => $lessonPlan->user->email,
            'subject' => 'New Lesson Plan Comment: '.$lessonPlan['lesson_plan_unit'],
            'content' => $messageContent,
            'view' => 'lesson_plan_reply'
        ]);
    }

    public function deleteComment($data) {
        $comment = LessonPlanComment::find($data['commentId']);

        if($comment->comment_id > 0) {
            $comment->delete();
        } else {
            $subComments = LessonPlanCommentReply::where('comment_id', $comment->comment_id)
                                            ->whereNot('user_id', \Auth::user()->id)
                                            ->get();
            if($subComments->count() > 0) {
                throw new \ErrorException('You can not delete comment that have other user replies!');
            }

            $subComments = LessonPlanCommentReply::where('comment_id', $comment->comment_id)
                            ->get();

            foreach ($subComments as $key => $sub) {
                $sub->delete();
            }
            $comment->delete();
        }
    }

    public function saveReflection($data) {
        $plan = LessonPlan::where('uid', $data['uid'])->first();
        $plan->reflection = $data['text'];
        $plan->save();
    }


}
