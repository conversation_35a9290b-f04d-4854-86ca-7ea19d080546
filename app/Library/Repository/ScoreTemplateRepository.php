<?php

namespace App\Library\Repository;

use Illuminate\Support\Facades\DB;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use App\Library\Helper\AcademicHelper;
use App\Models\ElectiveGrade;
use App\Models\ScoreTemplate;
use App\Models\ScoreTemplateData;
use App\Models\ScoreTemplateGrade;
use App\Models\SummativeAssessmentData;
use App\Models\FormativeAssessmentData;
use \Exception;

class ScoreTemplateRepository
{

    public function subjectGrades($subjectId)
    {
        $rd = '';

        $academicHelper = new AcademicHelper();
        $academicYearId = $academicHelper->academicYear();
        $branchId = $academicHelper->currentBranch();

        $grades = ElectiveGrade::where('academic_year_id', $academicYearId)
            ->where('branch_id', $branchId)
            ->where('subject_id', $subjectId)
            ->get();

        foreach ($grades as $key => $grade) {
            $rd .= '<option value="' . $grade->grade_id . '">' . $grade->grade_name . '</option>';
        }
        return $rd;
    }

    public function create($data)
    {
        $academicHelper = new AcademicHelper();
        $academicYearId = $academicHelper->academicYear();
        $branchId = $academicHelper->currentBranch();
        $scoreTempleateGrades = ScoreTemplateGrade::whereIn('grade_id', $data['grades'])->get()->count();
        if ($scoreTempleateGrades > 0) {
            return 'Summative Template for grade is Already Created';
        }
        DB::beginTransaction();
        try {
            $uid = uniqid();

            $template = new ScoreTemplate();
            $template->academic_year_id = $academicYearId;
            $template->branch_id = $branchId;
            $template->subject_id = $data['subject'];
            $template->title = $data['template_name'];
            $template->uid = $uid;
            $template->save();

            if ($template) {
                $newTemplate = ScoreTemplate::where('uid', $uid)->first();
                $dataArr = explode("|*|", $data['option_data']);

                foreach ($dataArr as $opt) {
                    if ($opt != '') {
                        $tempArr = explode("/*/", $opt);

                        $templateData = new ScoreTemplateData();
                        $templateData->summative_template_id = $newTemplate->summative_template_id;
                        $templateData->title = $tempArr[0];
                        $templateData->percentage = $tempArr[1];
                        $templateData->is_final_exam_percentage = $tempArr[2] ?? 0;
                        $templateData->save();
                    }
                }

                foreach ($data['grades'] as $grade) {
                    $templateGrade = new ScoreTemplateGrade();
                    $templateGrade->summative_template_id = $newTemplate->summative_template_id;
                    $templateGrade->grade_id = $grade;
                    $templateGrade->save();
                }
            }

            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    public function update($data)
    {
        $template = ScoreTemplate::find($data['templateId']);
        $template->title = $data['templateName'];
        $template->save();
    }

    public function deleteGrade($data)
    {
        $templateGrade = ScoreTemplateGrade::where('summative_template_id', $data['templateId'])
            ->where('template_grade_id', $data['gradeOptionId'])
            ->delete();
    }

    public function addGrade($data)
    {
        if (isset($data['grades'])) {
            foreach ($data['grades'] as $grade) {
                ScoreTemplateGrade::where('summative_template_id', $data['templateId'])
                    ->where('grade_id', $grade)
                    ->delete();

                $newGrade = new ScoreTemplateGrade();
                $newGrade->summative_template_id = $data['templateId'];
                $newGrade->grade_id              = $grade;
                $newGrade->save();
            }
        }
    }

    public function addOption($data)
    {
        $option = new ScoreTemplateData();
        $option->summative_template_id                  = $data['templateId'];
        $option->title                                  = $data['title'];
        $option->percentage                             = $data['percent'];
        $option->is_final_exam_percentage               = $data['isFinalExamPercentage'];
        $option->save();
    }

    public function deleteOption($data)
    {
        DB::beginTransaction();
        try {
            //-- Delete summative Assessment Data
            SummativeAssessmentData::where('type_id', $data['optionId'])->delete();

            //-- Delete formative Assessment Data
            FormativeAssessmentData::where('type_id', $data['optionId'])->delete();

            ScoreTemplateData::where('data_id', $data['optionId'])->delete();

            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    public function deleteTemplate($data)
    {
        $academicHelper = new AcademicHelper();
        $academicYearId = $academicHelper->academicYear();
        $branchId = $academicHelper->currentBranch();

        $check = ScoreTemplate::where('academic_year_id', $academicYearId)
            ->where('branch_id', $branchId)
            ->where('summative_template_id', $data['templateId'])
            ->first();

        if ($check) {
            DB::beginTransaction();
            try {
                //-- Delete Template Grades
                ScoreTemplateGrade::where('summative_template_id', $data['templateId'])->delete();

                $options = ScoreTemplateData::where('summative_template_id', $data['templateId'])->get();
                foreach ($options as $key => $opt) {
                    //-- Delete assigned summative & formative Assessment Data
                    SummativeAssessmentData::where('type_id', $opt->data_id)->delete();
                    FormativeAssessmentData::where('type_id', $opt->data_id)->delete();
                }
                ScoreTemplate::where('summative_template_id', $data['templateId'])->delete();
                DB::commit();
            } catch (Exception $e) {
                DB::rollBack();
                throw $e;
            }
        }
    }
    public function findTemplateGrade($data)
    {
        $rd = [];
        $scoreTempleateGrades = ScoreTemplateGrade::whereIn('grade_id', $data['gradeIds'])->get()->count();
        if ($scoreTempleateGrades > 0) {
            $rd = [
                'message' => "<p class='text-danger' >Summative Template for grades is Already Created</p>",
                'disable' => 'true',
            ];
        } else {
            $rd = [
                'message' => "<p ></p>",
                'disable' => 'false',
            ];
        }
        return $rd;
    }
    public function findFinalMidTermPercentage()
    {
        $academicHelper = new AcademicHelper();
        $branchId = $academicHelper->currentBranch();
        $data = DB::table('settings')
            ->where('setting_type', 'final_mid_term')
            ->where('setting_value_4', $branchId)
            ->first();
        // dd($data);

        return response()->json($data);
    }
}
