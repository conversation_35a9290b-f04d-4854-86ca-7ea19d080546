<?php

namespace App\Library\Repository;

use App\Models\AcademicSemester;
use App\Models\AcademicTerm;
use App\Library\Helper\AcademicHelper;
use App\Library\Helper\ReportCardHelper;
use Exception;
use App\Models\ReportCardData;

class KGReportCardRepository
{

    protected AcademicHelper $ah;
    protected ReportCardHelper $reportHelper;

    public function __construct(AcademicHelper $ah, ReportCardHelper $reportHelper)
    {
        $this->ah = $ah;
        $this->reportHelper = $reportHelper;
    }

    public function classroomStudents($classroomId)
    {
        $rd = '';
        $students = $this->ah->getClassroomStudents($classroomId);
        foreach ($students as $key => $student) {
            $sName = $student->student->name ?? '';
            $rd .= '<option selected value="' . $student->student_id . '">[' . $student->student_id . '] ' . $sName . '</option>';
        }
        return $rd;
    }

    public function reportCardPreCheck($data)
    {
      
        if (!is_numeric($data['term'])) {
            throw new Exception('Term is required and should be numeric');
        }

        $term = AcademicSemester::find($data['term']);
       
        if (!$term) {
            throw new Exception('Invalid term for the current branch or academic year');
        }

        $preCheck = ReportCardData::create([
            'academic_year_id' => $this->ah->academicYear(),
            'uid' => uniqid('', false),
            'report_type' => 'term',
            'classroom_id' => $data['classroom'],
            'students' => implode(',', $data['students']),
            'start_date' => $term->start_date,
            'end_date' => $term->end_date,
            'report_title' => $data['title'],
            'created_by' => \Auth::user()->id
        ]);
      
        return $preCheck ? $preCheck->uid : null; 
    }


}