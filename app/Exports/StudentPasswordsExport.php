<?php

// app/Exports/StudentPasswordsExport.php

namespace App\Exports;

use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class StudentPasswordsExport implements FromCollection, WithHeadings, WithStyles, WithColumnWidths
{
    protected $data;

    public function __construct(Collection $data)
    {
        $this->data = $data;
    }

    public function collection()
    {
        return $this->data;
    }

    public function headings(): array
    {
        return ['ID', 'Name','Classroom', 'Password'];
    }

    // 👉 Make header row bold and increase row height
    public function styles(Worksheet $sheet)
    {
        return [
            1 => [
                'font' => ['bold' => true],
                'alignment' => [
                    'horizontal' => 'left', // 👈 force left alignment for header
                    'vertical' => 'center',
                ],
            ],
            // Apply to all rows
            'A:B' => [
                'alignment' => [
                    'horizontal' => 'left', // 👈 force left alignment for data
                ],
            ],
        ];
    }

    // 👉 Set column widths
    public function columnWidths(): array
    {
        return [
            'A' => 30, // ID column
            'B' => 30, // Name column
            'C' => 30, // Classroom column
            'D' => 30, // Password column
        ];
    }
}
