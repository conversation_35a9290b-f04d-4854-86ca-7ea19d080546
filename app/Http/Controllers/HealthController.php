<?php

namespace App\Http\Controllers;

use App\Http\Requests\StaffHealthStoreRequest;
use App\Library\Helper\AcademicHelper;
use App\Library\Helper\ListHelper;
use App\Models\Dictionary;
use App\Models\HealthGuest;
use App\Models\HealthStaff;
use App\Models\HealthStudent;
use App\Models\StudentHealthInformation;
use App\Models\StudentInformation;
use App\Models\StudentMeasurements;
use App\Models\User;
use Illuminate\Http\Request;
use App\Http\Requests\StudentHealthStoreRequest;
use App\Http\Requests\StudentHealthRecordStoreRequest;
use App\Http\Requests\StaffHealthRecordStoreRequest;
use App\Http\Requests\GuestHealthRecordStoreRequest;
use App\Library\Repository\HealthRepository;

class HealthController extends Controller
{
    protected HealthRepository $health;
    protected AcademicHelper $academicHelper;
    protected ListHelper $listHelper;

    public function __construct(HealthRepository $health, AcademicHelper $academicHelper, ListHelper $listHelper) {
        $this->health = $health;
        $this->academicHelper = $academicHelper;
        $this->listHelper = $listHelper;
    }

    public function index(){

        $academicYear = $this->academicHelper->academicYear();
        $branch = $this->academicHelper->currentBranch();

        $studentRecords = HealthStudent::select('health_record_id', 'student_id', 'reason', 'action', 'date')
            ->with('student')
            ->where('academic_year_id', $academicYear)
            ->where('branch_id', $branch)
            ->orderByDesc('date')
            ->get();

        $staffRecords = HealthStaff::select('record_id', 'user_id', 'reason', 'action', 'date')
            ->with('user')
            ->where('academic_year_id', $academicYear)
            ->where('branch_id', $branch)
            ->orderByDesc('date')
            ->get();

        $guestRecords = HealthGuest::where('academic_year_id', $academicYear)
            ->where('branch_id', $branch)
            ->where('status', 1)
            ->orderByDesc('date')
            ->get();

        $students = $this->academicHelper->branchStudents($branch);

        $staff = User::select('id', 'name', 'data_profession', 'username')
            ->where('branch_id', $branch)
            ->where('user_type', 'staff')
            ->where('user_status', 1)
            ->get();

        return view('app.health', [
            'studentRecords' => $studentRecords,
            'staffRecords'   => $staffRecords,
            'guestRecords'   => $guestRecords,
            'students'       => $students,
            'staff'          => $staff,
            'injuries'       => $this->listHelper->healthInjuries(),
            'actions'        => $this->listHelper->healthActions()
        ]);
    }
    public function studentDetail(Request $request)
    {
        $studentId = $request->get('studentId');
        $injuries = $this->listHelper->healthInjuries();
        $actions = $this->listHelper->healthActions();

        $medications = $this->listHelper->healthMedications();

        $student = StudentInformation::find($studentId);
        $records = HealthStudent::where('academic_year_id', $this->academicHelper->academicYear())
            ->where('branch_id', $this->academicHelper->currentBranch())
            ->where('student_id', $studentId)
            ->orderBy('health_record_id', 'desc')
            ->where('status', 1)
            ->get();

        $measurements = StudentMeasurements::where('academic_year_id', $this->academicHelper->academicYear())
            ->where('branch_id', $this->academicHelper->currentBranch())
            ->where('student_id', $studentId)
            ->orderBy('date')
            ->get();

        $healthInfo = StudentHealthInformation::find($studentId);
        return view('partial_view.app.health.student_info',
            compact('student', 'records', 'actions',
                'medications', 'injuries', 'healthInfo',
                'measurements'));
    }
    public function staffDetail(Request $request)
    {
        $userId = $request->get('userId');
        $injuries = $this->listHelper->healthInjuries();
        $actions = $this->listHelper->healthActions();

        $medications = $this->listHelper->healthMedications();

        $user = User::find($userId);
        $records = HealthStaff::where('academic_year_id', $this->academicHelper->academicYear())
            ->where('branch_id', $this->academicHelper->currentBranch())
            ->where('user_id', $userId)
            ->orderBy('record_id', 'desc')
            ->where('status', 1)
            ->get();

        $bloodType = Dictionary::where('type', 'blood_type')->get();
        return view('partial_view.app.health.staff_info',compact('user', 'records', 'actions', 'medications', 'injuries', 'bloodType'));
    }


    public function saveStudentHealthInfo(StudentHealthStoreRequest $request) {
        $data = $request->validated();
        return $this->health->saveStudentHealthInfo($data);
    }
    public function saveStafftHealthInfo(StaffHealthStoreRequest $request) {
        $data = $request->validated();
        return $this->health->saveStaffHealthInfo($data);
    }

    public function createStudentRecord(StudentHealthRecordStoreRequest $request) {
        $data = $request->validated();
        return $this->health->createStudentRecord($data);
    }

    public function deleteStudentRecord() {
        return $this->health->deleteStudentRecord(request('recordId'));
    }
    public function createStudentMeasureRecord(Request $request) {

        $data = $request->validate([
            'student_id'=> 'required|numeric',
            'date'      => 'required|string',
            'height'    => 'required|numeric|min:1|max:3000',
            'weight'    => 'required|numeric|min:1|max:200',
        ]);

        return $this->health->createStudentMeasureRecord($data);
    }

    public function deleteStudentMeasureRecord($recordId){
        if (is_numeric($recordId)) {
            return  $this->health->deleteStudentMeasureRecord($recordId);
        }
        return response()->json(['message' => 'Invalid record ID'], 400);

    }

    public function createStaffRecord(StaffHealthRecordStoreRequest $request) {
        $data = $request->validated();
        return $this->health->createStaffRecord($data);
    }

    public function deleteStaffRecord() {
        return $this->health->deleteStaffRecord(request('recordId'));
    }

    public function createGuestRecord(GuestHealthRecordStoreRequest $request) {
        $data = $request->validated();
        return $this->health->createGuestRecord($data);
    }

    public function deleteGuestRecord() {
        return $this->health->deleteGuestRecord(request('recordId'));
    }

}
