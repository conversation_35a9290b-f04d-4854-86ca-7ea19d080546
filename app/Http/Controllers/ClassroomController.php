<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Library\Repository\ClassroomRepository;
use App\Http\Requests\ClassroomStoreRequest;

class ClassroomController extends Controller
{
    protected ClassroomRepository $classroomRepository;

    public function __construct(ClassroomRepository $classroomRepository)
    {
        $this->classroomRepository = $classroomRepository;
    }

    public function getStudents()
    {
        return $this->classroomRepository->getStudentsHtmlTable(request('classroomId'));
    }

    public function store(ClassroomStoreRequest $request)
    {
        $data = $request->validated();

        if ($data['classroom_id'] != '') {
            return $this->classroomRepository->update($data);
        } else {
            return $this->classroomRepository->create($data);
        }
    }

    public function delete()
    {
        return $this->classroomRepository->delete(request('classroomId'));
    }

    public function addStudent()
    {
        return $this->classroomRepository->addStudent([
            'classroomId' => request('classroomId'),
            'students'    => request('students')
        ]);
    }

    public function deleteStudent()
    {
        return $this->classroomRepository->deleteStudent([
            'classroomId' => request('classroomId'),
            'studentId'    => request('studentId')
        ]);
    }

    public function getYearClassrooms()
    {
        return $this->classroomRepository->yearClassrooms(request('yearId'));
    }
    public function resetPasswordClassroom(Request $request)
    {
        return $this->classroomRepository->resetPasswordClassroom($request->all());
    }
}
