<?php

namespace App\Http\Controllers;

use App\Library\Repository\HealthReportRepository;
use App\Models\Dictionary;
use Illuminate\Http\Request;

class HealthReportController extends Controller
{
    protected HealthReportRepository $health;

    public function __construct(HealthReportRepository $healthReportRepository){
        $this->health = $healthReportRepository;
    }
    public function generateList(Request $request){

        $reportType = $request->input('reportType');
        $medicineDictionary = Dictionary::where('type', 'health_drugs')->where('item_status', Dictionary::STATUS_ACTIVE)->get();
       
        if($reportType == 'healthStudentStatus'){
            $reportData = $this->health->getStudentHealthStatus();

             // Return a JSON response with the rendered HTML (partial view)
        return response()->json([
            'reportHtml' => view('partial_view.app.health.reports.student_info', compact('reportData', 'medicineDictionary'))->render()
        ]);

        }else if ($reportType == 'healthStaffStatus') {
            $reportData = $this->health->getStaffHealthStatus();
            
            // Return a JSON response with the rendered HTML (partial view)
            return response()->json([
                'reportHtml' => view('partial_view.app.health.reports.staff_info', compact('reportData'))->render()
            ]);
        }

       
    }


}
