<?php

namespace App\Http\Controllers;

use App\Models\StudentInformation;
use App\Models\User;
use App\Models\Classroom;
use App\Models\Activity;
use App\Models\ReportCardData;
use PDF;

class FileGenerateController extends Controller
{
    public function createStudentIdCard()
    {
        $data = [
            'studentId' => request('studentId'),
            'classroom' => request('classroom')
        ];
        $info = StudentInformation::find(request('studentId'));
        $pdf = PDF::loadView('partial_view.general.student_id_card', $data)->setPaper([0, 0, 600, 956], 'portrait')->setWarnings(false);
        return $pdf->download(request('classroom') . '-' . request('studentId') . '-' . $info->name . '.pdf');
    }

    public function createStaffIdCard()
    {
        $data = ['userId' => request('userId')];
        $info = User::find(request('userId'));
        $pdf = PDF::loadView('partial_view.general.staff_id_card', $data)->setPaper([0, 0, 600, 956], 'portrait')->setWarnings(false);;
        return $pdf->download(request('userId') . '-' . $info->name . '.pdf');
    }

    public function generateStudentList()
    {
        $data = ['classroomId' => request('classroomId')];
        $classroom = Classroom::find(request('classroomId'));
        $pdf = PDF::loadView('partial_view.generate_lists.classroom_student_list', $data);
        return $pdf->stream(request('userId') . '-' . $classroom->classroom_name . '.pdf');
    }

    public function generateAttendanceByDate()
    {
        $data = [
            'classroomId' => request('classroomId'),
            'date'        => request('date')
        ];
        $classroom = Classroom::find(request('classroomId'));
        $pdf = PDF::loadView('partial_view.generate_lists.student_attendance_by_date', $data);
        return $pdf->stream(request('userId') . '-' . $classroom->classroom_name . '.pdf');
    }

    public function printActivity()
    {
        $data = [
            'activityId' => request('activityId')
        ];
        $info = Activity::find(request('activityId'));

        if ($info) {
            $pdf = PDF::loadView('partial_view.generate_lists.activity_permission_paper', $data);
            return $pdf->stream($info->activity_uid . '.pdf');
        } else {
            return "Activity not found!";
        }
    }

    public function printLessonPlan()
    {
        $data = [
            'uid' => request('uid')
        ];
        // $plan = LessonPlan::where('uid', request('uid'))->first();
        $pdf = PDF::loadView('partial_view.generate_lists.lesson_plan', $data);
        return $pdf->stream('LessonPlan-' . request('uid') . '.pdf');
    }

    public function printTermPlan()
    {
        $data = [
            'planId' => request('planId')
        ];

        // $plan = LessonPlan::where('uid', request('uid'))->first();
        $pdf = PDF::loadView('partial_view.generate_lists.term_plan', $data);
        return $pdf->stream('TermPlan-' . request('planId') . '.pdf');
    }

    public function generateBPSList()
    {
        $pdf = PDF::loadView('partial_view.generate_lists.bps_records_list');
        return $pdf->stream('bps_list.pdf');
    }

    public function generateMajorList()
    {
        $pdf = PDF::loadView('partial_view.generate_lists.major_records_list');
        return $pdf->stream('major_records_list.pdf');
    }

    public function generateVisitorBadge()
    {
        $data = [
            'badgeId' => request('badgeId')
        ];
        $pdf = PDF::loadView('partial_view.generate_lists.visitor_badge', $data);
        return $pdf->stream('badge_' . request('badgeId') . '.pdf');
    }

    // public function generateReportCard()
    // {
    //     $data = ['uid' => request('uid')];
    //     $cardData = ReportCardData::where('uid', request('uid'))->first();
    //     // return view('partial_view.generate_lists.report_card.'.$cardData->report_type, $data);
    //     $students = explode(",", $cardData->students);
    //     $pdf = PDF::loadView('partial_view.generate_lists.report_card.' . $cardData->report_type, $data);
    //     $pdf->setPaper('a4', 'portrait');
    //     $pdf->render();
    //     return $pdf->stream('report_card_' . request('uid') . '.pdf');
    // }
    public function generateReportCard()
    {
        $data = ['uid' => request('uid'), 'student' => request('student')];
        $cardData = ReportCardData::where('uid', request('uid'))->first();
        $student =  request('student');
        // Generate PDF for the selected student
        $pdf = PDF::loadView('partial_view.generate_lists.report_card.' . $cardData->report_type, $data);
        $pdf->setPaper('a4', 'portrait');

        // Stream the PDF for this student
        return $pdf->stream("report_card_{$student}.pdf", ["Attachment" => false]);
    }



    public function generateStudentCompactContract()
    {
        $data = [
            'cID' => request('cID')
        ];
        $pdf = PDF::loadView('partial_view.generate_lists.student_contract.compact', $data);
        return $pdf->stream('contract.pdf');
    }

    public function generateStudentCompactDetailed()
    {
        $data = [
            'cID' => request('cID')
        ];
        $pdf = PDF::loadView('partial_view.generate_lists.student_contract.detailed', $data);
        return $pdf->stream('contract.pdf');
    }

    public function generateKGReportCard() {
        $cardData = ReportCardData::where('uid', request('uid'))->first();
 
        return \View::make('partial_view.generate_lists.kg_report.'.$cardData->report_type, ['data' => $cardData]);
    } 
}
