<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Library\Repository\AcademicSettingsRepository;

class AcademicSettingsController extends Controller
{
    protected AcademicSettingsRepository $academicSettingsRepository;

    public function __construct(AcademicSettingsRepository $academicSettingsRepository)
    {
        $this->academicSettingsRepository = $academicSettingsRepository;
    }

    public function addExcludedSubject()
    {
        $this->academicSettingsRepository->addExcludedSubject([
            'subjectId'     => request('subjectId'),
            'classrooms'   => request('classrooms')
        ]);
    }

    public function deleteExcludedSubject()
    {
        $this->academicSettingsRepository->deleteExcludedSubject([
            'subjectId'     => request('subjectId')
        ]);
    }

    public function saveBellTimes()
    {
        $this->academicSettingsRepository->saveBellTimes([
            'bellData' => request('bellData')
        ]);
    }

    public function saveLbcAtribute()
    {
        $this->academicSettingsRepository->saveLbcAtribute([
            'id'    => request('id'),
            'title' => request('title'),
            'info'  => request('info')
        ]);
    }

    public function saveLbcPoint()
    {
        $this->academicSettingsRepository->saveLbcPoint([
            'id'    => request('id'),
            'title' => request('title'),
            'score'  => request('score')
        ]);
    }

    public function saveLbcType()
    {
        $this->academicSettingsRepository->saveLbcType([
            'id'    => request('id'),
            'title' => request('title'),
        ]);
    }

    public function addStrandSkill()
    {
        $this->academicSettingsRepository->addStrandSkill([
            'type'     => request('type'),
            'value'   => request('title'),
        ]);
        return 'ok';
    }

    public function deleteStrandSkill()
    {
        $this->academicSettingsRepository->deleteStrandSkill([
            'id'     => request('id')
        ]);
    }

    public function deleteAssignedStrandSkill()
    {
        $this->academicSettingsRepository->deleteAssignedStrandSkill([
            'id'     => request('id')
        ]);
    }

    public function assignStrandSkill()
    {
        $this->academicSettingsRepository->assignStrandSkill([
            'term'          => request('term'),
            'grades'        => request('grades'),
            'strandSkills'  => request('strand_skill'),
        ]);
    }
    public function lockAllAssessments()
    {
        $this->academicSettingsRepository->lockAllAssessments();
    }
    public function lockAssessmentsByDate()
    {
        $this->academicSettingsRepository->lockAssessmentsByDate([
            'date'          => request('date'),
            'is_locked'          => request('is_locked'),
        ]);
    }
    public function addFinalMidExamPercentage()
    {
        $this->academicSettingsRepository->addFinalMidExamPercentage([
            'branch_id'             => request('branchId'),
            'value'                 => request('value'),
            'type'                  => request('type'),
        ]);
    }
}
