<?php

namespace App\Http\Controllers;

use App\Http\Requests\BPSStoreRequest;
use App\Library\Repository\BpsRepository;
use Illuminate\Http\Request;

class BPSController extends Controller
{
    protected BpsRepository $bpsRepository;

    public function __construct(BPSRepository $bpsRepository)
    {
        $this->bpsRepository = $bpsRepository;
    }


    public function storeBps(BPSStoreRequest $request)
    {
        $data = $request->validated();
        return $this->bpsRepository->storeBps($data);
    }

    public function deleteBps()
    {
        return $this->bpsRepository->deleteBps(request('bpsId'));
    }
    public function deleteDetentionRecord()
    {
        return $this->bpsRepository->deleteDetentionRecord(request('detentionId'));
    }
    public function detentionBps(Request $request)
    {
        return $this->bpsRepository->detentionBps($request);
    }
    public function changeDetentionRecordType(Request $request)
    {
        return $this->bpsRepository->changeDetentionRecordType($request);
    }
    public function serveBps(Request $request)
    {
        return $this->bpsRepository->serveBps($request);
    }

    public function branchRecords(Request $request)
    {
        return $this->bpsRepository->branchRecords($request);
    }
    public function branchRecordTotal(Request $request)
    {
        return $this->bpsRepository->branchRecordTotal($request);
    }

    public function getList(Request $request)
    {
        return $this->bpsRepository->getList($request);
    }
    public function addBpsDetail(Request $request)
    {
        return $this->bpsRepository->addBpsDetail($request);
    }
    public function detentionRecords(Request $request)
    {
        return $this->bpsRepository->detentionRecords($request);
    }
    public function getBehaviourAnalysis(Request $request)
    {
        return $this->bpsRepository->getBehaviourAnalysis($request);
    }
    public function getBehaviourAnalysisChart(Request $request)
    {
        return $this->bpsRepository->getBehaviourAnalysisChart($request);
    }
    public function getBehaviourAnalysisChartByClass(Request $request)
    {
        $data = $request->all();
        return $this->bpsRepository->getBehaviourAnalysisChartByClass($data);
    }
    public function getBranchClass()
    {
        return $this->bpsRepository->getBranchClass(request('branchId'), request('yearId'));
    }
    public function getClassStudent()
    {
        return $this->bpsRepository->getClassStudent(request('classIds'));
    }
    public function branchAwards(Request $request)
    {
        return $this->bpsRepository->branchAwards($request);
    }
    public function branchAwardRecordList(Request $request)
    {
        return $this->bpsRepository->branchAwardRecordList($request);
    }
    public function storeBpsAward(Request $request)
    {
        return $this->bpsRepository->storeBpsAward($request);
    }
    public function deleteBpsAward()
    {
        return $this->bpsRepository->deleteBpsAward(request('recordId'));
    }
    public function storeBpsItem(Request $request)
    {
        return $this->bpsRepository->storeBpsItem($request);
    }
    public function archiveBpsItem()
    {
        return $this->bpsRepository->archiveBpsItem(request('bpsItemId'),request('status'));
    }
    public function updateBpsItem()
    {
        return $this->bpsRepository->updateBpsItem(request('itemId'),request('itemTitle'));
    }
    public function storeDetentionDuty()
    {
        return $this->bpsRepository->storeDetentionDuty(request('dutyUserId'),request('detentionTypeId'));
    }
    public function updateDetentionDuty(Request $request)
    {
        return $this->bpsRepository->updateDetentionDuty($request);
    }
    public function archiveDetentionDuty()
    {
        return $this->bpsRepository->archiveDetentionDuty(request('dutyId'));
    }
}
