<?php

namespace App\Http\Controllers;

use App\Models\LearningArea;
use Illuminate\Http\Request;
use App\Http\Requests\AssessmentCreateRequest;
use App\Library\Repository\AssessmentRepository;
use Illuminate\Support\Facades\DB;

class AssessmentController extends Controller
{
    protected AssessmentRepository $assessmentRepository;

    public function __construct(AssessmentRepository $assessmentRepository)
    {
        $this->assessmentRepository = $assessmentRepository;
    }

    public function delete()
    {
        if (request('type') == 'summative') {
            $this->assessmentRepository->deleteSummative(request('id'));
        } else if (request('type') == 'formative') {
            $this->assessmentRepository->deleteFormative(request('id'));
        }
    }

    public function update()
    {
        if (request('type') == 'summative') {
            $this->assessmentRepository->updateSummative([
                'id' => request('id'),
                'data' => request('data')
            ]);
        } else if (request('type') == 'formative') {
            return $this->assessmentRepository->updateFormative([
                'id' => request('id'),
                'data' => request('data')
            ]);
        }
    }

    //-- Save assessment sent by user-------------------
    public function saveSummativeData()
    {
        return $this->assessmentRepository->saveSummativeData([
            'assessmentId' => request('assessmentId'),
            'scoreData' => request('scoreData')
        ]);
    }

    public function saveFormativeData()
    {
        return $this->assessmentRepository->saveFormativeData([
            'assessmentId' => request('assessmentId'),
            'scoreData' => request('scoreData')
        ]);
    }
    public function saveFormativeSingleData()
    {
        return $this->assessmentRepository->saveFormativeSingleData([
            'assessmentId' => request('assessmentId'),
            'scoreData' => request('scoreData')
        ]);
    }

    public function deleteSummativeAssessment()
    {
        return $this->assessmentRepository->deleteSummative(request('assessmentId'));
    }

    public function deleteFormativeAssessment()
    {
        return $this->assessmentRepository->deleteFormative(request('assessmentId'));
    }

    public function updateSummativeTitle()
    {
        return $this->assessmentRepository->updateSummativeTitle([
            'assessmentId' => request('assessmentId'),
            'title' => request('title'),
            'comment' => request('comment'),
            'strand' => request('strand'),
            'skill' => request('skill'),
            'date' => request('date'),
            'opt' => request('opt')
        ]);
    }

    public function updateFormativeTitle()
    {
        return $this->assessmentRepository->updateFormativeTitle([
            'assessmentId' => request('assessmentId'),
            'title' => request('title'),
            'comment' => request('comment'),
            'date' => request('date'),
            'strand' => request('strand'),
            'skill' => request('skill'),
            'opt' => request('opt')
        ]);
    }

    public function getGradeOptions()
    {
        return $this->assessmentRepository->gradeOptions([
            'gradeId' => request('gradeId'),
            'subjectId' => request('subjectId')
        ]);
    }

    public function getLifeSkills()
    {
        return $this->assessmentRepository->lifeSkills([
            'gradeId' => request('gradeId'),
        ]);
    }

    public function getStrands()
    {
        return $this->assessmentRepository->strands([
            'gradeId' => request('gradeId'),
        ]);
    }
    public function getSameSubjectGrades()
    {
        return $this->assessmentRepository->getSameSubjectGrades([
            'gradeId' => request('gradeId'),
            'subjectId' => request('subjectId'),
        ]);
    }

    public function create(AssessmentCreateRequest $request)
    {
        $data = $request->validated();
        $data['max-score'] = request()->input('max-score');
        $data['strand'] = request()->input('strand');
        if ($data['type'] == 'summative') {
            return $this->assessmentRepository->createSummative($data);
        } else if ($data['type'] == 'formative') {
            $data['lifeSkill'] = request()->input('lifeSkill');
            return $this->assessmentRepository->createFormative($data);
        }
    }


    // for KG

    public function updateKGFormativeTitle()
    {
        return $this->assessmentRepository->updateKGFormativeTitle([
            'assessmentId' => request('assessmentId'),
            'title' => request('title'),
            'date' => request('date'),
            'opt' => request('opt')
        ]);
    }

    public function getGradeOptionsAndLearningAreas()
    {
        return $this->assessmentRepository->gradeOptionsAndLearningAreas([
            'gradeId' => request('gradeId'),
            'subjectId' => request('subjectId')
        ]);
    }

    public function createKgAssessment(Request $request)
    {
        $data = $request->validate(
            [
                'type' => 'required|string',
                'option' => 'required|numeric',
                'learning_area' => 'required|numeric',
                'date' => 'required',
                'na_grade_id' => 'required|numeric',
                'na_subject_id' => 'required|numeric',
            ]
        );

        return $this->assessmentRepository->createKgAssessment($data);

    }

    public function updateAfaAla()
    {
        function stringSimilarity($str1, $str2)
        {
            // Calculate Levenshtein distance or use another similarity function
            return levenshtein(strtolower($str1), strtolower($str2));
        }

        // Start a database transaction
        DB::beginTransaction();

        try {
            // Step 1: Fetch all assessments
            $assessments = DB::table('academic_formative_assessments')
                ->select('assessment_name', 'grade_id', 'academic_year_id', 'branch_id')
                ->where('academic_year_id', 7)
                ->where('branch_id', 3)
                ->get();

            // Step 2: Group assessments with similar names
            $groupedAssessments = [];
            foreach ($assessments as $assessment) {
                $matched = null;

                foreach ($groupedAssessments as &$group) {
                    // Ensure $group is valid
                    if (is_array($group) && isset($group['name'])) {
                        if (stringSimilarity($group['name'], $assessment->assessment_name) <= 3) {
                            $group['grade_ids'][] = $assessment->grade_id;
                            $group['names'][] = $assessment->assessment_name;
                            $matched = true;
                            break; // Exit inner loop if matched
                        }
                    }
                }
                // If no match, create a new group
                if (!$matched) {
                    $groupedAssessments[] = [
                        'name' => $assessment->assessment_name,
                        'academic_year_id' => $assessment->academic_year_id,
                        'branch_id' => $assessment->branch_id,
                        'grade_ids' => [$assessment->grade_id],
                        'names' => [$assessment->assessment_name],
                    ];
                }
            }

            // Step 3: Insert or update ala records
            foreach ($groupedAssessments as $group) {
                $existingAla = DB::table('academic_learning_areas')
                    ->where('name', $group['name'])
                    ->where('academic_year_id', $group['academic_year_id'])
                    ->where('branch_id', $group['branch_id'])
                    ->first();

                if ($existingAla) {
                    // Update grades JSON field by merging
                    $existingGrades = json_decode($existingAla->grades, true);
                    $mergedGrades = array_unique(array_merge($existingGrades, $group['grade_ids']));
                    DB::table('academic_learning_areas')
                        ->where('id', $existingAla->id)
                        ->update(['grades' => json_encode($mergedGrades)]);
                } else {
                    // Insert new ala record
                    $newUid = uniqid();
                    DB::table('academic_learning_areas')->insert([
                        'academic_year_id' => $group['academic_year_id'],
                        'branch_id' => $group['branch_id'],
                        'grades' => json_encode($group['grade_ids']),
                        'name' => $group['name'],
                        'uid' => $newUid,
                        'status' => 1,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);
                }
            }

            // Step 4: Update afa.uid for all matched assessments
            foreach ($groupedAssessments as $group) {
                $alaUid = DB::table('academic_learning_areas')
                    ->where('name', $group['name'])
                    ->where('academic_year_id', $group['academic_year_id'])
                    ->where('branch_id', $group['branch_id'])
                    ->value('uid');

                if ($alaUid) {
                    DB::table('academic_formative_assessments')
                        ->whereIn('assessment_name', $group['names'])
                        ->update(['uid' => $alaUid]);
                }
            }

            // Commit the transaction
            DB::commit();

            echo "Transformation and grade merging complete.";
        } catch (\Exception $e) {
            // Rollback the transaction if an error occurs
            DB::rollBack();

            // Log the error or handle it as needed
            echo "Error occurred: " . $e->getMessage();
        }
    }

    public function updateGradesInAcademicLearningAreas()
    {
        // Step 1: Retrieve records in chunks to avoid memory overload
        LearningArea::chunk(100, function ($learningAreas) {
            foreach ($learningAreas as $area) {
                $grades = json_decode($area->grades, true);

                if (is_array($grades)) {
                    $stringGrades = array_map('strval', $grades);
                    $area->grades = json_encode($stringGrades);
                    $area->save();
                }
            }
        });

        return response()->json(['message' => 'Grades updated successfully']);
    }

    public function showKgFormative(Request $request)
    {
        $gradeId = $request->input('id');
        $termId = $request->input('termId');

        $currentSemester = $this->assessmentRepository->getCurrentSemester($termId);

        if (!$currentSemester) {
            return response()->json([
                'status' => 'error',
                'error_code' => 'TERM_NOT_FOUND',
                'message' => 'The current academic term is not set. Contact your administrator to check academic terms in the settings.'
            ], 400);
        }

        $assessments = $this->assessmentRepository->getAssessments($currentSemester, $gradeId);
        $students = $this->assessmentRepository->getStudents($gradeId);
        $lockedTerms = $this->assessmentRepository->getLockedTerms();
        $gradeInfo = $this->assessmentRepository->getGradeInfo($gradeId);

        return view('partial_view.app.academy.assessment.kg-assessment.assessment_formative', compact(
            'gradeId',
            'currentSemester',
            'lockedTerms',
            'assessments',
            'students',
            'gradeInfo'
        ));
    }

    public function saveKgFormative(Request $request)
    {
        $data = $request->validate(
            [
                'dataToSend' => 'required|array|min:1',
                'dataToSend.*.student_id' => 'required|numeric',
                'dataToSend.*.assessment_id' => 'required|numeric',
                'dataToSend.*.mark' => 'required|string',
                'dataToSend.*.comment' => 'nullable|string',
            ]
        );
        return $this->assessmentRepository->saveKgFormative($data);

    }
}
