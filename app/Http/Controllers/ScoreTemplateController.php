<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Library\Repository\ScoreTemplateRepository;
use App\Http\Requests\ScoreTemplateStoreRequest;

class ScoreTemplateController extends Controller
{
    protected ScoreTemplateRepository $scoreTemplateRepository;

    public function __construct(ScoreTemplateRepository $scoreTemplateRepository)
    {
        $this->scoreTemplateRepository = $scoreTemplateRepository;
    }

    public function getSubjectGrades()
    {
        return $this->scoreTemplateRepository->subjectGrades(request('subjectId'));
    }

    public function create(ScoreTemplateStoreRequest $request)
    {
        $data = $request->validated();
        $this->scoreTemplateRepository->create($data);
    }

    public function update()
    {
        $this->scoreTemplateRepository->update([
            'templateId'    => request('templateId'),
            'templateName'  => request('templateName')
        ]);
    }

    public function deleteGrade()
    {
        $this->scoreTemplateRepository->deleteGrade([
            'templateId'    => request('templateId'),
            'gradeOptionId'  => request('gradeOptionId')
        ]);
    }

    public function addGrade()
    {
        $this->scoreTemplateRepository->addGrade([
            'templateId'    => request('templateId'),
            'grades'  => request('grades')
        ]);
    }

    public function addOption()
    {
        $this->scoreTemplateRepository->addOption([
            'templateId'                => request('templateId'),
            'title'                     => request('title'),
            'percent'                   => request('percent'),
            'isFinalExamPercentage'     => request('isFinalExamPercentage'),
        ]);
    }

    public function deleteOption()
    {
        $this->scoreTemplateRepository->deleteOption([
            'templateId' => request('templateId'),
            'optionId'   => request('optionId')
        ]);
    }

    public function deleteTemplate()
    {
        $this->scoreTemplateRepository->deleteTemplate([
            'templateId' => request('templateId')
        ]);
    }
    public function findTemplateGrade()
    {
        return $this->scoreTemplateRepository->findTemplateGrade([
            'gradeIds' => request('gradeIds')
        ]);
    }
    public function findFinalMidTermPercentage()
    {
        return $this->scoreTemplateRepository->findFinalMidTermPercentage();
    }
}
