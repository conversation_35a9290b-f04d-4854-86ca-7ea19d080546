<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Library\Repository\StudentRepository;
use App\Http\Requests\StudentStoreRequest;

class StudentController extends Controller
{
    protected StudentRepository $studentRepository;

    public function __construct(StudentRepository $studentRepository)
    {
        $this->studentRepository = $studentRepository;
    }

    public function archivedStudents()
    {
        $rd = '';

        $students = $this->studentRepository->archivedStudents();
        foreach ($students as $key => $student) {
            $rd .= '<tr>';
            $rd .= '<td>' . $student->id . '</td>';
            $rd .= '<td>' . $student->branch->branch_name . '</td>';
            $rd .= '<td>' . $student->name . '</td>';
            $rd .= '<td><a href="#" data-id="' . $student->id . '" class="btnRestoreStudent">Restore</a></td>';
            $rd .= '</tr>';
        }
        return $rd;
    }

    public function restoreStudent()
    {
        return $this->studentRepository->restoreStudent(request('studentId'));
    }

    public function archiveStudent()
    {
        return $this->studentRepository->archiveStudent([
            'studentId' => request('studentId')
        ]);
    }

    public function getIdCardInfo()
    {
        return $this->studentRepository->getIdCardInfo(request('id'));
    }

    public function store(StudentStoreRequest $request)
    {
        $data = $request->validated();
        if ($data['student_id'] != '') {
            return $this->studentRepository->update($data);
        } else {
            return $this->studentRepository->create($data);
        }
    }

    public function changePassword()
    {
        return $this->studentRepository->changePassword([
            'studentId' => request('studentId'),
            'password'  => request('password')
        ]);
    }

    public function transferStudent()
    {
        return $this->studentRepository->transferStudent([
            'studentId' => request('studentId'),
            'branchId'  => request('branchId')
        ]);
    }

    public function updateSignature()
    {
        return $this->studentRepository->updateSignature([
            'studentId'     => request('studentId'),
            'signatureData' => request('signatureData')
        ]);
    }

    public function createContract()
    {
        return $this->studentRepository->createContract([
            'studentId' => request('studentId'),
            'regType'   => request('regType'),
            'regYear'   => request('regYear'),
        ]);
    }

    public function updateContract()
    {
        return $this->studentRepository->updateContract([
            'parentName'            => request('parentName'),
            'contractId'            => request('contractId'),
            'studentId'             => request('studentId'),
            'signatureDataParent'   => request('signatureDataParent'),
            'signatureDataOfficer'  => request('signatureDataOfficer'),
            'optRegForm'            => request('optRegForm'),
            'optHealthForm'         => request('optHealthForm'),
            'optStudentInformation' => request('optStudentInformation'),
            'optProgramme'          => request('optProgramme'),
            'optPaymentAgreement'   => request('optPaymentAgreement'),
            'optTuitionFee'         => request('optTuitionFee'),
            'optParentAgreement'    => request('optParentAgreement'),
            'optCOCStudent'         => request('optCOCStudent'),
            'optCOCParent'          => request('optCOCParent'),
            'optDevicePolicy'       => request('optDevicePolicy')
        ]);
    }

    public function changeNewPassword()
    {
        $result = $this->studentRepository->changeNewPassword([
            'oldPassword'   => request('oldPassword'),
            'password'      => request('password')
        ]);
        if ($result['success']) {
            return response()->json(['success' => 'Password updated successfully.']);
        } else {
            return response()->json(['error' => $result['message']], 400);
        }
    }
}
