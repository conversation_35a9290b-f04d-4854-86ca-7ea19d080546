<?php

namespace App\Http\Controllers;

use App\Library\Helper\AcademicHelper;
use App\Library\Repository\BpsRepository;
use App\Library\Repository\MobileNotificationRepository;
use App\Models\AttendanceGroupMember;
use App\Models\AttendanceLog;
use Carbon\Carbon;
use Illuminate\Http\Request;
use App\Models\StudentAttendance;
use App\Models\User;

class StudentAttendanceController extends Controller
{
    //
    protected AcademicHelper $ah;
    protected MobileNotificationRepository $mobileNotification;
    protected BpsRepository $bpsRepository;

    public function __construct(AcademicHelper $ah, MobileNotificationRepository $mobileNotification, BpsRepository $bpsRepository)
    {
        $this->ah = $ah;
        $this->mobileNotification = $mobileNotification;
        $this->bpsRepository = $bpsRepository;
    }

    public function store(Request $request)
    {
        $record_id = request('record_id');
        $swipe_date = request('swipe_date');
        $code = request('card_no');
        $branchId = request('branch_id');
        $acYear = $this->ah->branchAcademicYear($branchId);

        $rd = '';
        //$branchId   = request('branchId');
        $userInfo = null;

        // $time = Carbon::createFromFormat('Y-m-d H:i:s', $swipe_date);
        // if ($time->timezoneName != 'Asia/Yangon') {
        //     $swipe_date = $time->setTimezone('Asia/Yangon')->format('Y-m-d H:i:s');
        // }

        //     $currentDateTime = Carbon::now('Asia/Yangon');
        //     // Separate date and time
        //     $dateStr = $currentDateTime->format('Y-m-d');
        //     $timeStr = $currentDateTime->format('H:i:s');
        //     return response()->json([
        //         'date'=>$dateStr,
        //         'timeStr'=>$timeStr,
        // ]);

        $userInfo = User::where('rfid', $code)
            ->where('user_status', 1)
            ->first();

        if (!$userInfo) {
            return response()->json(['message' => 'User not found'], 404);
        } else {
            $swipeDate = explode(" ", $swipe_date);

            if ($userInfo->user_type == 'staff') {
                $attendanceGroup = AttendanceGroupMember::where('user_id', $userInfo->id)->orderBy('group_member_id', 'desc')->first();
                if (!$attendanceGroup) {
                    $rd = '0|User attendance group not found';
                } else {
                    AttendanceLog::create([
                        'academic_year_id'  => $acYear,
                        'device_id'         => 0,
                        'user_id'           => $userInfo->id,
                        'branch_id'         => $userInfo->branch_id,
                        'date_time'         => $swipe_date,
                        'date'              => $swipeDate[0],
                        'time'              => $swipeDate[1]
                    ]);
                }
                return response()->json(['message' => 'Staff card' . $swipeDate[0]], 200);
            } else if ($userInfo->user_type == 'student') {
                $checkAttendance = StudentAttendance::where('student_id', $userInfo->id)->whereDate('date', $swipeDate[0])->first();
                if ($checkAttendance) {
                    $first = Carbon::createFromFormat('Y-m-d H:i:m', $checkAttendance->date . " " . $checkAttendance->swipe_time);
                    $second = Carbon::createFromFormat('Y-m-d H:i:m', $swipe_date);
                    $difference = $first->diffInMinutes($second);
                    if ($difference > 10) {
                        // $this->mobileNotification->sendSingle([
                        //     'academic_year' => $acYear,
                        //     'title'             => 'Leaving the School',
                        //     'message'           => $userInfo->name . ' left the school at: ' . $swipeDate[1],
                        //     'student'           => $userInfo->id,
                        //     'type'              => 'attendance',
                        //     'user_type'         => $userInfo->user_type,
                        // ]);
                        $this->mobileNotification->sendRealTime([
                            'academic_year' => $acYear,
                            'title'             => 'Leaving the School',
                            'message'           => $userInfo->name . ' left the school at: ' . $swipeDate[1],
                            'student'           => $userInfo->id,
                            'type'              => 'attendance',
                            'user_type'         => $userInfo->user_type,
                        ]);
                    }
                } else {
                    StudentAttendance::create([
                        'branch_id' => $branchId,
                        'academic_year_id' => $acYear,
                        'student_id' => $userInfo->id,
                        'date' => $swipeDate[0],
                        'swipe_time' => $swipeDate[1],
                        'attendance_type' => 'reader',
                        'attendance_status' => 'present',
                        'code' => $record_id,
                    ]);
                    // $this->mobileNotification->sendSingle([
                    //     'academic_year'     => $acYear,
                    //     'title'             => 'School Entrance',
                    //     'message'           => $userInfo->name . ' arrived at school at: ' . $swipeDate[1],
                    //     'student'           => $userInfo->id,
                    //     'type'              => 'attendance',
                    //     'user_type'         => $userInfo->user_type,
                    // ]);
                    $this->mobileNotification->sendRealTime([
                        'academic_year'     => $acYear,
                        'title'             => 'School Entrance',
                        'message'           => $userInfo->name . ' arrived at school at: ' . $swipeDate[1],
                        'student'           => $userInfo->id,
                        'type'              => 'attendance',
                        'user_type'         => $userInfo->user_type,
                    ]);
                    // Sent DPS if late
                    // $to_time = strtotime($swipeDate[1]);
                    // $from_time = strtotime('08:00:00');
                    // $lateMinutes = round(($from_time - $to_time) / 60);
                    // if ($lateMinutes < -1) {
                    //     $bpsRecord = [
                    //         "student"=>[$userInfo->id],
                    //         "case_type"=>"1",
                    //         "dps_case"=>"136", // late dps
                    //         "prs_case"=>null,
                    //         "date"=>$swipeDate[0],
                    //         "note"=>null,
                    //         "branch_id" => $branchId,
                    //         "user_id" => $userInfo->id,
                    //     ];
                    //     // if late in Entrance , get a late bps record
                    //     $this->bpsRepository->storeBps($bpsRecord);
                    // }
                }
            }
            return response()->json(['message' => 'Record stored successfully'], 200);
            //$rd = '1|'.$userInfo->user_type.'|'.$userInfo->id.'|'.$userInfo->name.'|'.$userInfo->photo.'|'.$carbonDate->format('Y-m-d H:i:m');
        }
        return response()->json(['message' => $record_id], 404);
    }
}
