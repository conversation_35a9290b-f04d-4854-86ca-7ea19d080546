<?php

namespace App\Http\Controllers;

use App\Library\Helper\AcademicHelper;
use App\Library\Repository\LearningAreaRepository;
use App\Models\LearningArea;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Str;


class LearningAreaController extends Controller
{

    protected $academicHelper;
    protected $learningArea;

    public function __construct()
    {
        $this->academicHelper = new AcademicHelper();
        $this->learningArea = new LearningAreaRepository();
    }
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $learning_areas = LearningArea::where('academic_year_id', $this->academicHelper->academicYear())
            ->where('branch_id', $this->academicHelper->currentBranch())
            ->orderBy('created_at', 'desc')
            ->get();
        $gradesList = $this->academicHelper->branchElectiveGradesHTML();
        $yearList = $this->academicHelper->academicYear();

        // Return only the partial view
        return response()->view('partial_view.app.administration.academic_settings.learning_area.list', [
            'learning_areas' => $learning_areas,
            'gradesList' => $gradesList,
            'yearList' => $yearList
        ]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'learning_area' => 'required|string',
            'grade' => 'required|array|min:1',
        ]);

        $learningArea = $this->learningArea->store($validated);

        $learningArea->load('subject');

        return response()->json([
            'id' => $learningArea->id,
            'name' => $learningArea->name,
            'grade' => $learningArea->grade_name,
            'subject' => $learningArea->subject_name,
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\LearningArea  $LearningArea
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $learningArea = LearningArea::findOrFail($id);
        $gradesList = $this->academicHelper->branchElectiveGrades();

        return response()->view('partial_view.app.administration.academic_settings.learning_area.form_edit', [
            'learningArea' => $learningArea,
            'gradesList' => $gradesList,
        ]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\LearningArea  $LearningArea
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request)
    {
        $validated = $request->validate([
            'id' => 'required|numeric',
            'learning_area' => 'required|string',
            'grade' => 'required|array|min:1',
        ], [
            'learning_area.required' => 'The learning area name is required.',
            'learning_area.string' => 'The learning area must be a valid text.',
            'grade.required' => 'At least one grade must be selected.',
            'grade.array' => 'Invalid grade format.',

        ]);

        $result = $this->learningArea->update($validated);
        $result->load('subject');

        return response()->json([
            'id' => $result->id,
            'name' => $result->name,
            'subject' => $result->subject_name,
        ]);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\LearningArea  $LearningArea
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request)
    {
        $validated = $request->validate([
            'learning_area_id' => 'required|numeric',
        ]);
        $result = $this->learningArea->destroy($validated['learning_area_id']);
        if ($result) {
            return response()->json(['message' => 'Learning area deleted successfully.'], 200);
        } else {
            return response()->json(['message' => 'Failed to delete learning area.'], 500);
        }

    }

    public function getByYear(Request $request)
    {
        $yearId = $request->input('academic_year_id');
        $branchId = $this->academicHelper->currentBranch();

        // Only show learning areas from the selected year that aren't in the current year
        $learningAreas = LearningArea::where('academic_year_id', $yearId)
            ->where('branch_id', $branchId)
            ->get()
            ->map(function ($area) {
                return [
                    'uid' => $area->uid,
                    'name' => $area->name,
                    'status' => $area->status
                ];
            });

        return response()->json([
            'success' => true,
            'learning_areas' => $learningAreas
        ]);
    }

    public function importFromPreviousYear(Request $request)
    {
        try {
            $selectedAreas = $request->input('learning_areas', []);
            $currentYearId = $this->academicHelper->academicYear();
            $branchId = $this->academicHelper->currentBranch();
            $userId = auth()->id();
           
            $importedCount = 0;
            $duplicateCount = 0;

            \DB::beginTransaction();

            foreach ($selectedAreas as $area) {
                // Check if already exists in current year
                $exists = LearningArea::where('uid', $area['uid'])
                    ->where('academic_year_id', $currentYearId)
                    ->where('branch_id', $branchId)
                    ->exists();

                if (!$exists) {
                    // Generate a unique UID
                    $uid = Str::uuid()->toString();

                    LearningArea::create([
                        'academic_year_id' => $currentYearId,
                        'branch_id' => $branchId,
                        'name' => $area['name'],
                        'status' => 1,
                        'uid' => $uid,
                        'created_by' => $userId,
                        'updated_by' => $userId
                    ]);

                    $importedCount++;
                } else {
                    $duplicateCount++;
                }
            }

            \DB::commit();

            $message = "Successfully imported $importedCount learning area(s)";
            if ($duplicateCount > 0) {
                $message .= " ($duplicateCount duplicate(s) skipped)";
            }

            return response()->json([
                'success' => true,
                'message' => $message,
            ]);
        } catch (\Exception $e) {
            \DB::rollBack();

            return response()->json([
                'success' => false,
                'message' => "Error importing learning areas: " . $e->getMessage(),
            ], 500);
        }
    }
}
