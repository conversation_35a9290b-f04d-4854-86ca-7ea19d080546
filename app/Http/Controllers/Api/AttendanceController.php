<?php

namespace App\Http\Controllers\Api;

use Illuminate\Http\Request;
use App\Models\User;

use Exception;
use App\Models\AttendanceLog;
use App\Models\AttendanceGroupMember;
use App\Models\StudentAttendanceLog;
use App\Library\Helper\AcademicHelper;
use Carbon\Carbon;
use App\Library\Repository\MobileNotificationRepository;
use App\Models\StudentPickupCard;
use App\Models\StudentPickupCardLog;
use App\Models\StudentPickupRequest;
use Illuminate\Support\Facades\DB;
use App\Models\StudentAttendance;

class AttendanceController
{
    protected AcademicHelper $ah;
    protected MobileNotificationRepository $mobileNotification;

    public function __construct(AcademicHelper $ah, MobileNotificationRepository $mobileNotification)
    {
        $this->ah = $ah;
        $this->mobileNotification = $mobileNotification;
    }

    public function addAttendance()
    {
        $record_id = $_POST['record_id'];
        $swipe_date = $_POST['swipe_date'];
        $code = $_POST['card_no'];
        $branchId = $_POST['branch_id'];

        $rd = '';
        //$branchId   = request('branchId');
        $userInfo = null;

        $userInfo = User::where('rfid', $code)
            ->where('user_status', 1)
            ->first();

        if (!$userInfo) {
            return response()->json(['message' => 'User not found'], 404);
            //Don't add the swipe record to Attendance table
        } else {
            $swipeDate = explode(" ", $swipe_date);

            if ($userInfo->user_type == 'staff') {
                /*$attendanceGroup = AttendanceGroupMember::where('user_id', $userInfo->id)->orderBy('group_member_id', 'desc')->first();
                if(!$attendanceGroup) {
                    $rd = '0|User attendance group not found';
                } else {
                    AttendanceLog::create([
                        'academic_year_id'  => $this->ah->academicYear(),
                        'devic_id'          => 0,
                        'branch_id'         => $branchId,
                        'attendance_id'     => $attendanceGroup->group_member_id,
                        'date_time'         => $carbonDate->format('Y-m-d H:i:m'),
                        'date'              => $carbonDate->format('Y-m-d'),
                        'time'              => $carbonDate->format('H:i:m')
                    ]);
                }*/
                return response()->json(['message' => 'Staff card'], 404);
                //Don't add the swipe record to Attendance table
            } else if ($userInfo->user_type == 'student') {
                $checkAttendance = StudentAttendance::where('student_id', $userInfo->id)->whereDate('date', $swipeDate[0])->first();
                if ($checkAttendance) {
                    $first = Carbon::createFromFormat('Y-m-d H:i:m', $checkAttendance->date . " " . $checkAttendance->swipe_time);
                    $second = Carbon::createFromFormat('Y-m-d H:i:m', $swipe_date);
                    $difference = $first->diffInMinutes($second);
                    if ($difference > 10) {
                        // $this->mobileNotification->sendSingle([
                        //     'title'             => 'Leaving the School',
                        //     'message'           => $userInfo->name . ' left the school at: ' . $swipeDate[1],
                        //     'student'           => $userInfo->id,
                        //     'type'              => 'attendance',
                        //     'user_type'         => $userInfo->user_type,
                        // ]);
                        $this->mobileNotification->sendRealTime([
                            'title'             => 'Leaving the School',
                            'message'           => $userInfo->name . ' left the school at: ' . $swipeDate[1],
                            'student'           => $userInfo->id,
                            'type'              => 'attendance',
                            'user_type'         => $userInfo->user_type,
                        ]);
                    }
                } else {
                    StudentAttendance::create([
                        'branch_id'         => $branchId,
                        'academic_year_id'  => $this->ah->academicYear(),
                        'student_id'        => $userInfo->id,
                        'date'              => $swipeDate[0],
                        'swipe_time'        => $swipeDate[1],
                        'attendance_type'   => 'readerAPI',
                        'attendance_status' => 'present',
                        'code'              => $record_id,
                    ]);
                    //send notification to Mobile app
                    // $this->mobileNotification->sendSingle([
                    //     'title'             => 'School Entrance',
                    //     'message'           => $userInfo->name . ' entered the school at: ' . $swipeDate[1],
                    //     'student'           => $userInfo->id,
                    //     'type'              => 'attendance',
                    //     'user_type'         => $userInfo->user_type,
                    // ]);
                    $this->mobileNotification->sendRealTime([
                        'title'             => 'School Entrance',
                        'message'           => $userInfo->name . ' entered the school at: ' . $swipeDate[1],
                        'student'           => $userInfo->id,
                        'type'              => 'attendance',
                        'user_type'         => $userInfo->user_type,
                    ]);
                }
            }
            return response()->json(['message' => 'Record received successfully'], 200);
            //$rd = '1|'.$userInfo->user_type.'|'.$userInfo->id.'|'.$userInfo->name.'|'.$userInfo->photo.'|'.$carbonDate->format('Y-m-d H:i:m');
        }
        return response()->json(['message' => $record_id], 404);
    }

    public function searchPickup()
    {
        $rd = '';
        $code       = request('id');
        $branchId   = request('branchId');
        $userInfo = null;

        $userInfo = User::where('id', $code)
            ->orWhere('rfid', $code)
            ->where('user_status', 1)
            ->first();

        if (!$userInfo) {
            $rd = '0|User not found';
        } else {
            $carbonDate = Carbon::now();
            $carbonDate->timezone = 'Asia/Bangkok';

            if ($userInfo->user_type == 'staff') {
                $attendanceGroup = AttendanceGroupMember::where('user_id', $userInfo->id)->orderBy('group_member_id', 'desc')->first();
                if (!$attendanceGroup) {
                    $rd = '0|User attendance group not found';
                } else {
                    AttendanceLog::create([
                        'academic_year_id'  => $this->ah->academicYear(),
                        'device_id'          => 0,
                        'branch_id'         => $branchId,
                        'attendance_id'     => $attendanceGroup->group_member_id,
                        'date_time'         => $carbonDate->format('Y-m-d H:i:m'),
                        'date'              => $carbonDate->format('Y-m-d'),
                        'time'              => $carbonDate->format('H:i:m')
                    ]);

                    $rd = '1|staff|' . $userInfo->id . '|' . $userInfo->name . '|' . $userInfo->photo . '|' . $carbonDate->format('Y-m-d H:i:m');
                }
            } else if ($userInfo->user_type == 'student') {
                $cards = StudentPickupCard::where('branch_id', $branchId)
                    //   ->where('academic_year_id', $this->ah->academicYear())
                    ->where('student_id', $userInfo->id)
                    ->get();
                $cardData = "";
                foreach ($cards as $key => $card) {
                    $photoFileName = $card->photo;
                    $photoFileName = '/' . ltrim($photoFileName, '/');
                    $cardData .= $card->card_id . "/*/" . $card->name . "/*/" . $card->relation . "/*/" . $photoFileName . "/*/" . $userInfo->id . "/-/";
                }
                $rd = '1|student|' . $userInfo->id . '|' . $userInfo->name . '|' . $userInfo->photo . '|' . $carbonDate->format('Y-m-d H:i:m') . '|' . $cardData;
            }
        }
        return $rd;
    }

    public function registerPickup()
    {
        $carbonDate = Carbon::now();
        $carbonDate->timezone = 'Asia/Bangkok';
        $date = $carbonDate->format('Y-m-d');
        $time = $carbonDate->format('H:i:m');

        $card = StudentPickupCard::find(request('cardId'));

        $create = StudentPickupCardLog::create([
            'academic_year_id'  => $this->ah->academicYear(),
            'branch_id'         => $card->branch_id,
            'student_id'        => $card->student_id,
            'pickup_id'         => request('cardId'),
            'date'              => $date,
            'time'              => $time
        ]);

        if ($create) {
            StudentAttendanceLog::create([
                'branch_id'         => $card->branch_id,
                'academic_year_id'  => $this->ah->academicYear(),
                'student_id'        => $card->student_id,
                'year'              => $carbonDate->format('Y'),
                'month'             => $carbonDate->format('m'),
                'day'               => $carbonDate->format('d'),
                'date'              => $date,
                'time'              => $time,
                'source_type'       => 'rfid'
            ]);

            StudentPickupRequest::where('student_id', $card->student_id)
                ->whereDate('request_date', $carbonDate->format('Y-m-d'))
                ->update([
                    'request_status'          => 1,
                    'request_status_message'  => 'Left school with ' . $card->relation . ', ' . $card->name . ' at ' . $carbonDate->format('H:i:s')
                ]);

            $notificationBody = $card->student->name . " left school at " . $time . " on " . $date . " with " . $card->relation . " " . $card->name . ". If you think something is wrong, please contact the school immediately.";
            // $this->mobileNotification->sendSingle([
            //     'title'             => 'School Exit',
            //     'message'           => $notificationBody,
            //     'student'           => $card->student_id,
            //     'type'              => 'attendance',
            //     'user_type'         => $card->student->user_type,
            // ]);
            $this->mobileNotification->sendRealTime([
                'title'             => 'School Exit',
                'message'           => $notificationBody,
                'student'           => $card->student_id,
                'type'              => 'attendance',
                'user_type'         => $card->student->user_type,
            ]);
        }
    }
}
