<?php

namespace App\Http\Controllers;

use App\Http\Requests\KGReportCardRequest;
use App\Library\Repository\KGReportCardRepository;
use App\Library\Helper\ReportCardHelper;
use App\Http\Requests\SingleSubjectAverageRequest;

class KGReportCardController extends Controller
{
    protected ReportCardHelper $reportHelper;
    protected KGReportCardRepository $reportCard;

    public function __construct(KGReportCardRepository $reportCard, ReportCardHelper $reportHelper) {
        $this->reportCard = $reportCard;
        $this->reportHelper = $reportHelper;
    }

    public function singleSubjectAverage(SingleSubjectAverageRequest $request){
        $data = $request->validated();
        return $this->reportHelper->singleSubjectAverage($data);
    }
    
    public function classroomStudents() {
        return $this->reportCard->classroomStudents(request('classroomId'));
    }

    public function reportCardPreCheck(KGReportCardRequest $request) {
        $data = $request->validated();
        return $this->reportCard->reportCardPreCheck($data);
    }
}
