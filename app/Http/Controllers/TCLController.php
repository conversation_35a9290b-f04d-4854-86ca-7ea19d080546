<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Requests\TCLStoreRequest;
use App\Library\Repository\TCLRepository;

class TCLController extends Controller
{
    protected TCLRepository $tclRepository;

    public function __construct(TCLRepository $tclRepository) {
        $this->tclRepository = $tclRepository;
    }

    public function create(TCLStoreRequest $request) {
        $data = $request->validated();
        $this->tclRepository->create($data);
    }

    public function delete() {
        $this->tclRepository->delete(request('tclId'));
    }
    public function update(Request $request) {
         $this->tclRepository->update($request);
    }
}
