<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StudentHealthStoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules() {
        return [
            'student_id'                => 'required|numeric',
            'medical_condition'         => 'nullable|string',
            'regularly_used_medication' => 'nullable|string',
            'vision_problem'            => 'nullable|string',
            'vision_check_date'         => 'nullable|date',
            'hearing_issue'             => 'nullable|string',
            'food_consideration'        => 'nullable|string',
            'allergy'                   => 'nullable|string',
            'allergy_symptoms'          => 'nullable|string',
            'allergy_first_aid'         => 'nullable|string',
            'allowed_medication'        => 'nullable|array',
            'emergency_name_1'          => 'nullable|string',
            'emergency_name_2'          => 'nullable|string',
            'emergency_phone_1'         => 'nullable|string',
            'emergency_phone_2'         => 'nullable|string',
        ];
    }

    public function messages() {
        return [
            'student_id.required' => 'Student is required!'
        ];
    }
}
