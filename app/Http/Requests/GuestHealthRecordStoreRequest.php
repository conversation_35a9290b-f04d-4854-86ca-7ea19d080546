<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class GuestHealthRecordStoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules() {
        return [
            'name'                  => 'required|string',
            'date'                  => 'required|date',
            'time'                  => 'required|string',
            'discharge_time'        => 'required|string',
            'reason'                => 'required|array|min:1',
            'action'                => 'nullable|array',
            'temperature'           => 'nullable|string',
            'blood_pressure'        => 'nullable|string',
            'saturation'            => 'nullable|numeric',
            'pulse'                 => 'nullable|integer',
            'medication'            => 'nullable|string',
            'comments'              => 'nullable|string'
        ];
    }

    public function messages() {
        return [
            'name.required'   => 'Name is required',
            'date.required'         => 'Admission date is required',
            'time.required'         => 'Admission time is required',
            'reason.required'       => 'At least 1 reason is required'
        ];
    }
}
