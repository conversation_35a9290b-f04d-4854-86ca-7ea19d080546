<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class KGReportCardRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules() {
        return [
            'classroom'                 => 'required|numeric',
            'students'                  => 'required|array|min:1',
            'term'                      => 'required|numeric',
            'title'                     => 'required|string',
        ];
    }

    public function messages() {
        return [
            'term.required'          => 'Term required',
            'classroom.required'     => 'Classroom required',
            'students.required'      => 'Students required',
            'title.required'         => 'Title required',
            
        ];
    }
}
