<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class AssessmentCreateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'type'                          => 'required|string',
            'title'                         => 'required|string',
            'comment'                       => 'nullable|string',
            'is_final_exam'                 => 'nullable|string',
            'same_subject_grades'           => 'nullable|array',
            'date'                          => 'required|date',
            'option'                        => 'required|numeric',
            'na_grade_id'                   => 'required|numeric',
            'na_subject_id'                 => 'required|numeric'
        ];
    }

    public function messages()
    {
        return [
            'type.required'          => 'Assessment type is required',
            'title.required'         => 'Title is required',
            'date.required'          => 'Date is required',
            'option.required'        => 'Option is required',
            'na_grade_id.required'   => 'Invalid Grade ID',
            'na_subject_id.required' => 'Invalid Subject ID'
        ];
    }
}
