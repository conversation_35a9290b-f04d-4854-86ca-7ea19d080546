$.ajaxSetup({
    headers: { "X-CSRF-Token": $("meta[name=_token]").attr("content") },
});

$(document).ready(function () {
    $("#menuAcademy").addClass("here");
    $("#menuAcademy").addClass("show");
    $("#menuAcademyAssessment").addClass("active");
    $('#impactTr').hide();
    // makeDataTable("summativeTable");
    makeSummativeTable();
    makeDataTable("formativeTable");
    makeDataTable("aListTable");
    $(document).on("change", "#type", function () {
        if ($(this).val() == "summative") {
            $("#lifeSkillSelector").hide();
            // $("#impactTr").show();
            $("#finalExamTr").show();
            $("#maxScoreTr").show();
        } else {
            $("#lifeSkillSelector").show();
            // $("#impactTr").hide();
            $("#finalExamTr").hide();
            $("#maxScoreTr").hide();
        }
    });
    $(document).on('click','#missingStrandCollapse',function(){
        $("#missingStrandCollapseModal").toggle('hide');
    })
    $(document).on('click','#missingImpactCollapse',function(){
        $("#missingImpactCollapseModal").toggle('hide');
    })
$(document).on("change", "#isFinalExam", function () {
    const selectedValue = $(this).val(); // "0" or "1"
    // Destroy and re-initialize Select2 to reflect changes
    $("#assessmentOption").select2("destroy").select2();

    // Reset the selection (optional)
    $("#assessmentOption").val("").trigger("change");

    // Filter the assessment options based on selectedValue
    $("#assessmentOption option").each(function () {
        const isFinalExam = $(this).data("is-final-exam");

        // Always keep the empty/default option enabled
        if ($(this).val() === "") {
            $(this).prop("disabled", false);
            $(this).prop('selected', true)
            return;
        }

        // Enable only matching options
        if (String(isFinalExam) === selectedValue) {
            $(this).prop("disabled", false);
            $(this).prop('selected', true)
        } else {
            $(this).prop("disabled", true);
            $(this).prop('selected', false)
        }
    });


    if(selectedValue == 1){
        let matched = false;
        $("#strand option").each(function () {
            const val = $(this).val();
            const isFinal = $(this).attr('data-is-final-exam');
            console.log($(this).attr('data-is-final-exam'));
            
            if (isFinal==1 && !matched) {
                $(this).prop("disabled", false);
                $(this).prop('selected', true); // set selected
                matched = true;
            }
        });
        $('#strand').trigger('change');
        $('#StrandTr').hide();
        if(matched == false){
            Swal.fire('Your Assessment Doesn`t Have Final Exam , Please Choose No (or) Report the Issue! ')
        }
    }else if (selectedValue == 0) {
        // Destroy and re-initialize Select2 to reflect changes
        $("#strand").select2("destroy").select2();

        // Reset the selection (optional)
        $("#strand").val("").trigger("change");
        $('#StrandTr').show(); // Show strand select box
        // Remove options that are in FinalStrands
        $("#strand option").each(function () {
            const val = $(this).val();
            const isFinal = $(this).attr('data-is-final-exam');
            if (isFinal == 1) {
                $(this).prop("disabled", true);
            }
        });
    }
    // // Destroy and re-initialize Select2 to reflect changes
    // $("#assessmentOption").select2("destroy").select2();

    // // Reset the selection (optional)
    // $("#assessmentOption").val("").trigger("change");
});

});

var clickedSummativeAssessment = null;
var clickedFormativeAssessment = null;

function getData(gradeId) {
    $("#tab_summative").html(
        '<p class="text-center"><img src="/img/loader.gif" style="width: 30px;" /></p>'
    );
    $("#tab_formative").html(
        '<p class="text-center"><img src="/img/loader.gif" style="width: 30px;" /></p>'
    );
    $("#tab_summative").load(
        "/app/academy/assessment/show-summative-data-class" +
            "?gradeId=" +
            gradeId +
            "&searchSemester=" +
            null,
        function () {
            $("#semester").select2({
                placeholder: "Select Term",
                allowClear: true,
            });
    $("#missingStrandCollapseModal").hide();
    $("#missingImpactCollapseModal").hide();
            makeDataTable("missingStrandPercentageTable");
            makeSummativeTable();
        }
    );
    $("#tab_formative").load(
        "/app/academy/assessment/show-formative-data-class" +
            "?gradeId=" +
            gradeId +
            "&searchSemester=" +
            null,
        function () {
            $("#formativeSemester").select2({
                placeholder: "Select Term",
                allowClear: true,
            });
            makeDataTable("missingLifeSkillTable");
            makeDataTable("formativeTable");
        }
    );
    $("#welcomeBox").hide();
}
$(document).on("click", ".btnViewSummative", function () {
    clickedSummativeAssessment = $(this).attr("data-id");
    $("#summativeDataArea").load(
        "/app/academy/assessment/show-summative-data?id=" +
            clickedSummativeAssessment
    );
});

$(document).on("click", ".btnViewFormative", function () {
    clickedFormativeAssessment = $(this).attr("data-id");
    $("#formativeDataArea").load(
        "/app/academy/assessment/show-formative-data?id=" +
            clickedFormativeAssessment
    );
});

$(document).on("click", "#btnSubmitSummativeData", function () {
    var scoreData = "";
    showLoader();
    $(".scoreBox").each(function () {
        var comment = $(
            ".comment-box_" + $(this).attr("data-student-id")
        ).val();
        scoreData +=
            $(this).attr("data-id") +
            "|" +
            $(this).val() +
            "|" +
            $(this).attr("data-student-id") +
            "|" +
            comment +
            "/*/";
    });

    $.ajax({
        type: "POST",
        url: "/app/academy/assessment/save-summative-data",
        data: {
            scoreData: scoreData,
            assessmentId: clickedSummativeAssessment,
        },
        success: function (data) {
            hideLoader();
            hideModal("#modalSummativeData");
            Swal.fire({
                position: "top-end",
                icon: "success",
                title: "Your work has been saved",
                showConfirmButton: false,
                timer: 1500,
            });
        },
        error: function (xhr, status, error) {
            hideLoader();
            parseException(xhr);
        },
    });
});

$(document).on("click", "#btnSubmitFormativeData", function () {
    var scoreData = "";
    $(".scoreRowFormative").each(function () {
        var studentId = $(this).attr("data-student");
        var t1 = $("." + studentId + "_t1").val();
        var t2 = $("." + studentId + "_t2").val();
        var t3 = $("." + studentId + "_t3").val();
        var t4 = $("." + studentId + "_t4").val();
        var comment = $(".comment-box_" + studentId).val();
        scoreData +=
            studentId +
            "/*/" +
            t1 +
            "/*/" +
            t2 +
            "/*/" +
            t3 +
            "/*/" +
            t4 +
            "/*/" +
            comment +
            "|*|";
    });

    showLoader();
    $.ajax({
        type: "POST",
        url: "/app/academy/assessment/save-formative-data",
        data: {
            scoreData: scoreData,
            assessmentId: clickedFormativeAssessment,
        },
        success: function (data) {
            hideLoader();
            hideModal("#modalFormativeData");
            Swal.fire({
                position: "top-end",
                icon: "success",
                title: "Your work has been saved",
                showConfirmButton: false,
                timer: 1500,
            });
        },
        error: function (xhr, status, error) {
            hideLoader();
            parseException(xhr);
        },
    });
});

$(document).on("click", ".btnDeleteSummative", function () {
    Swal.fire({
        title: "Are you sure?",
        text: "You won't be able to revert this!",
        icon: "warning",
        showCancelButton: true,
        confirmButtonColor: "#3085d6",
        cancelButtonColor: "#d33",
        confirmButtonText: "Yes, delete it!",
    }).then((result) => {
        if (result.isConfirmed) {
            showLoader();
            $.ajax({
                type: "POST",
                url: "/app/academy/assessment/delete-summative",
                data: { assessmentId: $(this).attr("data-id") },
                success: function (data) {
                    $("#tab_summative").load(
                        "/app/academy/assessment/show-summative-assessments?gradeId=" +
                            $(this).attr("data-grade-id") +
                            "&searchSemester=" +
                            null,
                        function () {
                            hideLoader();
                        }
                    );
                },
                error: function (xhr, status, error) {
                    hideLoader();
                    parseException(xhr);
                },
            });
        }
    });
});

$(document).on("click", ".btnDeleteFormative", function () {
    Swal.fire({
        title: "Are you sure?",
        text: "You won't be able to revert this!",
        icon: "warning",
        showCancelButton: true,
        confirmButtonColor: "#3085d6",
        cancelButtonColor: "#d33",
        confirmButtonText: "Yes, delete it!",
    }).then((result) => {
        if (result.isConfirmed) {
            showLoader();
            $.ajax({
                type: "POST",
                url: "/app/academy/assessment/delete-formative",
                data: { assessmentId: $(this).attr("data-id") },
                success: function (data) {
                    $("#tab_formative").load(
                        "/app/academy/assessment/show-formative-assessments?gradeId=" +
                            $(this).attr("data-grade-id") +
                            "&searchSemester=" +
                            null,
                        function () {
                            hideLoader();
                        }
                    );
                },
                error: function (xhr, status, error) {
                    hideLoader();
                    parseException(xhr);
                },
            });
        }
    });
});

$(document).on("click", ".btnEditSummative", function () {
    $("#modalSummativeEdit").modal("show");
    $("#summativeEditArea").load(
        "/app/academy/assessment/summative/edit?id=" + $(this).attr("data-id"),
        function () {}
    );
});

$(document).on("click", ".btnEditFormative", function () {
    $("#modalFormativeEdit").modal("show");
    $("#formativeEditArea").load(
        "/app/academy/assessment/formative/edit?id=" + $(this).attr("data-id"),
        function () {}
    );
});
$(document).on("click", ".btnViewSummative", function () {
    $("#modalAssessmentDetail").modal("show");
    $("#assessmentDetailArea").load(
        "/app/academy/assessment/summative/detail?id=" +
            $(this).attr("data-id"),
        function () {}
    );
});
$(document).on("click", ".btnViewFormative", function () {
    $("#modalAssessmentDetail").modal("show");
    $("#assessmentDetailArea").load(
        "/app/academy/assessment/formative/detail?id=" +
            $(this).attr("data-id"),
        function () {}
    );
});

$(document).on("click", "#btnSaveSummativeName", function () {
    var gradeId = $.trim($("#editSummativeAssessmentGradeId").val());
    var title = $.trim($("#editSummativeAssessmentName").val());
    var comment = $.trim($("#editSummativeAssessmentComment").val());
    var date = $("#editSummativeAssessmentDate").val();
    var opt = $("#editSummativeAssessmentOption").val();
    var strand = $("#editSummativeAssessmentStrand").val();
    var skill = $("#editSummativeAssessmentSkill").val();
    var assessmentId = $("#editSummativeAssessmentId").val();
    if (title != "") {
        showLoader();
        $.ajax({
            type: "POST",
            url: "/app/academy/assessment/update-summative-title",
            data: {
                assessmentId: assessmentId,
                title: title,
                comment: comment,
                date: date,
                opt: opt,
                strand: strand,
                skill: skill,
            },
            success: function (data) {
                hideModal("#modalSummativeEdit");
                $("#tab_summative").load(
                    "/app/academy/assessment/show-summative-assessments?gradeId=" +
                        gradeId +
                        "&searchSemester=" +
                        null,
                    function () {
                        makeSummativeTable();
                        hideLoader();
                    }
                );
            },
            error: function (xhr, status, error) {
                hideLoader();
                parseException(xhr);
            },
        });
    }
});

$(document).on("click", "#btnSaveFormativeName", function () {
    var gradeId = $.trim($("#editFormativeAssessmentGradeId").val());
    var title = $.trim($("#editFormativeAssessmentName").val());
    var comment = $("#editFormativeAssessmentComment").val();
    var date = $("#editFormativeAssessmentDate").val();
    var opt = $("#editFormativeAssessmentOption").val();
    var assessmentId = $("#editFormativeAssessmentId").val();
    var strand = $("#editFormativeAssessmentStrand").val();
    var skill = $("#editFormativeAssessmentSkill").val();
    if (title != "") {
        showLoader();
        $.ajax({
            type: "POST",
            url: "/app/academy/assessment/update-formative-title",
            data: {
                assessmentId: assessmentId,
                title: title,
                comment: comment,
                date: date,
                opt: opt,
                strand: strand,
                skill: skill,
            },
            success: function (data) {
                hideModal("#modalFormativeEdit");
                $("#tab_formative").load(
                    "/app/academy/assessment/show-formative-assessments?gradeId=" +
                        gradeId +
                        "&searchSemester=" +
                        null,
                    function () {
                        makeDataTable('formativeTable')
                        hideLoader();
                    }
                );
            },
            error: function (xhr, status, error) {
                hideLoader();
                parseException(xhr);
            },
        });
    }
});

$(document).on("click", ".sessionRow", function () {
    $(".sessionRow").removeClass("activeRow");
    $(this).addClass("activeRow");
    $("#formNewAssessment2 select").select2();

    var gradeId = $(this).attr("data-grade");
    var subjectId = $(this).attr("data-subject");
    showLoader();

    $.ajax({
        type: "POST",
        url: "/app/academy/assessment/get-grade-options",
        data: { gradeId: gradeId, subjectId: subjectId },
        success: function (data) {
            $("#assessmentOption").html(data);
            $("#na_grade_id").val(gradeId);
            $("#na_subject_id").val(subjectId);
            // $('#assessment_timetable_id').val(timetableId);
                    const selectedValue = $('#isFinalExam').val(); // "0" or "1"
                // Filter the assessment options based on selectedValue
                $("#assessmentOption option").each(function () {
                    const isFinalExam = $(this).data("is-final-exam");
                    // Always keep the empty/default option enabled
                    if ($(this).val() === "") {
                        $(this).prop("disabled", false);
                        return;
                    }

                    // Enable only matching options
                    if (String(isFinalExam) === selectedValue) {
                        $(this).prop("disabled", false);
                    } else {
                        $(this).prop("disabled", true);
                    }
                });

                // Destroy and re-initialize Select2 to reflect changes
                $("#assessmentOption").select2("destroy").select2();

                // Reset the selection (optional)
                $("#assessmentOption").val("").trigger("change");
        },
        error: function (xhr, status, error) {
            hideLoader();
            parseException(xhr);
        },
    });

    $.ajax({
        type: "POST",
        url: "/app/academy/assessment/get-life-skills",
        data: { gradeId: gradeId, subjectId: subjectId },
        success: function (data) {
            $("#lifeSkill").html(data);
        },
        error: function (xhr, status, error) {
            hideLoader();
            parseException(xhr);
        },
    });

    $.ajax({
        type: "POST",
        url: "/app/academy/assessment/get-strands",
        data: { gradeId: gradeId, subjectId: subjectId },
        success: function (data) {
            hideLoader();
            $("#strand").html(data);
        },
        error: function (xhr, status, error) {
            hideLoader();
            parseException(xhr);
        },
    });
    $.ajax({
        type: "POST",
        url: "/app/academy/assessment/get-same-subject-grades",
        data: { gradeId: gradeId, subjectId: subjectId },
        success: function (data) {
            hideLoader();
            $("#same_subject_grades").html(data);
        },
        error: function (xhr, status, error) {
            hideLoader();
            parseException(xhr);
        },
    });

    $("#summativeOptTable").css("opacity", 1);
    $("#summativeOptTable").css("pointer-events", "auto");
});

// $(document).on("click", "#title", function () {
//     if ($("#type").val() == "summative") {
//         $("#lifeSkillSelector").hide();
//     } else {
//         $("#lifeSkillSelector").show();
//     }
// });

$(document).on("click", "#btnSubmitNewAssessment", function () {
    var validated = validateForm("#formNewAssessment2");
    var gradeId = $("#na_grade_id").val();
    var subjectId = $("#na_subject_id").val();
    showLoader();
    hideModal("#modalNewAssessment");
    // $("#modalNewAssessment").modal("hide");
    // alert(validated.get("title"));
    if (validated !== "fail") {
        $.ajax({
            type: "POST",
            url: "/app/academy/assessment/create",
            data: validated,
            contentType: false,
            processData: false,
            cache: false,
            enctype: "multipart/form-data",
            success: function (data) { 
                  hideLoader();
                // emergency reload
                Swal.fire({
                    position: "top-end",
                    icon: "success",
                    title: "Your work has been saved",
                    html:
                        "<p>" +
                        validated.get("title") +
                        " is added ,You can view at the start (or) end of the Strand </p>",
                    showConfirmButton: false,
                    timer: 3000,
                });
                getData(gradeId);
            },
            error: function (xhr, status, error) {
                parseException(xhr);
            },
            // complete: function () {
            //     // Ensure any modal backdrop is removed and body is scrollable again
            //     $(".modal-backdrop").remove();
            //     $("body").css("overflow", "auto");
            //     $("html, body").css("position", "");
            // },
        });
    }
});

$(document).on("click", ".keypad", function () {
    var symbol = $(this).attr("data-symbol");
    navigator.clipboard.writeText(symbol);
    Swal.fire({
        position: "bottom-end",
        icon: "success",
        title: symbol + " copied.",
        showConfirmButton: false,
        timer: 1000,
        target: "#modalFormativeData",
    });
});

$(document).on("click", ".inputClick", function (event) {
    var studentId = $(this).closest("tr").attr("data-student");
    var inputValue = $(this).val();

    $("." + studentId + "_t1").val("");
    $("." + studentId + "_t2").val("");
    $("." + studentId + "_t3").val("");
    $("." + studentId + "_t4").val("");
    $(event.target).val("+");
    // Check if the value is not empty or null
    if (inputValue.trim() !== "") {
        // The input has a value
        $(event.target).val("");
    }
});
$(document).on("click", ".subjectRow", function () {
    var gradeId = $(this).attr("data-id");
    getData(gradeId);
});
$(document).on("change", ".summativeScore", function () {
    var dataId = $(this).attr("data-id");
    var assessmentId = $(this).attr("assessment-id");
    var maxScore = $(this).attr("assessment-max_score");
    var studentId = $(this).attr("student-id");
    var score = parseInt($(this).val(), 10);
    var inputBox = $(this);
    var scoreData = "";
    var comment = null;
    if (score > maxScore) {
        Swal.fire({
            position: "middle",
            icon: "error",
            title:
                "The entered score cannot be more than the maximum score of " +
                maxScore,
            showConfirmButton: false,
            timer: 2000,
        });
        return false; // Prevent further action
    }
    // showLoader();
    scoreData += dataId + "|" + score + "|" + studentId + "|" + comment + "/*/";
    // alert(dataId + "-" + assessmentId);
    $.ajax({
        type: "POST",
        url: "/app/academy/assessment/save-summative-data",
        data: {
            scoreData: scoreData,
            assessmentId: assessmentId,
        },
        success: function (data) {
            hideLoader();
            inputBox.css("background-color", "rgb(152, 251, 152)");
            inputBox.css("color", "black");
        },
        error: function (xhr, status, error) {
            hideLoader();
            parseException(xhr);
        },
    });
});
// $(document).on("click", ".inputFormativeClick", function (event) {
//     var studentId = $(this).attr("student-id");
//     var assessmentDataId = $(this).attr("data-id");
//     var inputValue = $(this).val();

//     $("." + studentId + "_t1")
//         .filter("[data-id='" + assessmentDataId + "']")
//         .val("");
//     $("." + studentId + "_t2")
//         .filter("[data-id='" + assessmentDataId + "']")
//         .val("");
//     $("." + studentId + "_t3")
//         .filter("[data-id='" + assessmentDataId + "']")
//         .val("");
//     $("." + studentId + "_t4")
//         .filter("[data-id='" + assessmentDataId + "']")
//         .val("");
//     $(event.target).val("+");
//     // Check if the value is not empty or null
//     if (inputValue.trim() !== "") {
//         // The input has a value
//         $(event.target).val("");
//     }

//     var scoreData = "";
//     var comment = "";
//     // alert(assessmentDataId);
//     var t1 = $("." + studentId + "_t1")
//         .filter("[data-id='" + assessmentDataId + "']")
//         .val();
//     var t2 = $("." + studentId + "_t2")
//         .filter("[data-id='" + assessmentDataId + "']")
//         .val();
//     var t3 = $("." + studentId + "_t3")
//         .filter("[data-id='" + assessmentDataId + "']")
//         .val();
//     var t4 = $("." + studentId + "_t4")
//         .filter("[data-id='" + assessmentDataId + "']")
//         .val();

//     scoreData +=
//         studentId +
//         "/*/" +
//         t1 +
//         "/*/" +
//         t2 +
//         "/*/" +
//         t3 +
//         "/*/" +
//         t4 +
//         "/*/" +
//         comment +
//         "|*|";
//     showLoader();
//     $.ajax({
//         type: "POST",
//         url: "/app/academy/assessment/save-formative-single-data",
//         data: {
//             scoreData: scoreData,
//             assessmentId: assessmentDataId,
//         },
//         success: function (data) {
//             hideLoader();
//             hideModal("#modalFormativeData");
//             Swal.fire({
//                 position: "top-end",
//                 icon: "success",
//                 title: "Your work has been saved",
//                 showConfirmButton: false,
//                 timer: 1500,
//             });
//         },
//         error: function (xhr, status, error) {
//             hideLoader();
//             parseException(xhr);
//         },
//     });
// });
$(document).on("change", ".inputFormativeClick", function (event) {
    var studentId = $(this).attr("student-id");
    var assessmentDataId = $(this).attr("data-id");
    var inputValue = $(this).val();
    var selectBox = $(this);
    var scoreData = "";
    var comment = "";
    var t1 = inputValue == "ee" ? "+" : "";

    var t2 = inputValue == "me" ? "+" : "";
    var t3 = inputValue == "ae" ? "+" : "";
    var t4 = inputValue == "be" ? "+" : "";

    scoreData +=
        studentId +
        "/*/" +
        t1 +
        "/*/" +
        t2 +
        "/*/" +
        t3 +
        "/*/" +
        t4 +
        "/*/" +
        comment +
        "|*|";
    // alert(scoreData);
    // showLoader();
    $.ajax({
        type: "POST",
        url: "/app/academy/assessment/save-formative-single-data",
        data: {
            scoreData: scoreData,
            assessmentId: assessmentDataId,
        },
        success: function (data) {
            // hideLoader();
            // hideModal("#modalFormativeData");
            // Swal.fire({
            //     position: "top-end",
            //     icon: "success",
            //     title: "Your work has been saved",
            //     showConfirmButton: false,
            //     timer: 1500,
            // });
            selectBox.css("background-color", "rgb(152, 251, 152)");
            selectBox.css("color", "black");
        },
        error: function (xhr, status, error) {
            // hideLoader();
            parseException(xhr);
        },
    });
});
$(document).on("click", "#summativeSemesterSearch", function (event) {
    var semester = $("#semester").val();
    var gradeId = $("#gradeId").val();
    showLoader();
    // alert(semester);
    $("#tab_summative").load(
        "/app/academy/assessment/show-summative-data-class" +
            "?gradeId=" +
            gradeId +
            "&searchSemester=" +
            semester,
        function () {
            // makeDataTable("summativeTable");
            makeSummativeTable();

            hideLoader();
        }
    );
    $("#welcomeBox").hide();
});
$(document).on("click", "#formativeSemesterSearch", function (event) {
    var semester = $("#formativeSemester").val();
    var gradeId = $("#gradeId").val();
    showLoader();
    // alert(semester);
    $("#tab_formative").load(
        "/app/academy/assessment/show-formative-data-class" +
            "?gradeId=" +
            gradeId +
            "&searchSemester=" +
            semester,
        function () {
            makeDataTable("formativeTable");
            hideLoader();
        }
    );
    $("#welcomeBox").hide();
});
$(document).on("keydown", ".table-summative-input", function (e) {
    if (e.key === "Enter") {
        e.preventDefault(); // Prevent the default action for Enter

        var currentTd = $(this).closest("td"); // Get the current <td>
        var currentTr = $(this).closest("tr"); // Get the current <tr>
        var nextInput;

        // Try to move to the input in the next row, same column
        var nextTr = currentTr.next("tr");
        if (nextTr.length > 0) {
            // Move to the same column in the next row
            nextInput = nextTr.find("td").eq(currentTd.index()).find("input");
        }

        // If the input in the next row and same column exists, focus on it
        if (nextInput && nextInput.length > 0) {
            nextInput.focus();
        }
    }
});
// Make Sumamtive Table 
function makeSummativeTable(){
    $("#summativeTable").DataTable({
        dom:
            "<'row'<'col-sm-6 d-flex align-items-center justify-conten-start'l>" +
            "<'col-sm-6 d-flex align-items-center justify-content-end'fB>>" +
            "<'table-responsive'tr><'row'" +
            "<'col-sm-12 col-md-5 d-flex align-items-center justify-content-center justify-content-md-start'i>" +
            "<'col-sm-12 col-md-7 d-flex align-items-center justify-content-center justify-content-md-end'p>>",
        lengthMenu: [
            [25, 50, -1],
            [25, 50, "All"],
        ],
        buttons: [
            {
                extend: "excel",
                text: "Export to Excel",
                text: '<i class="fas fa-file-excel"></i>',
                className: "btn-sm btn-primary",
                exportOptions: {
                    // Customize what data to export
                    format: {
                        body: function (data, row, column, node) {
                            // Check if the element contains an input field
                            if ($(node).find("input").length) {
                                // Return the input field's value
                                return $(node).find("input").val();
                            }
                            // Return original data otherwise
                            return data;
                        },
                    },
                },
            },
        ],
    });
}

$(document).on("click", "#btnNewAssessment", function (event) {
$("#modalNewAssessment").modal("show");
});
