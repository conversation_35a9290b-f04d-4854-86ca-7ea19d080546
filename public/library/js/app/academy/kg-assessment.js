$(document).ready(function () {

    // Constants
    const ENDPOINTS = {
        FORMATIVE_DATA: '/app/academy/assessment/kg-assessment/show-formative-data',
        FORMATIVE_DATA_VIEW: '/app/academy/assessment/kg-assessment/view-formative-data',
        SEARCH_TABLE: '/app/academy/assessment/kg-assessment/show-search-table',
        DELETE_FORMATIVE: '/app/academy/assessment/kg-delete-formative',
        FORMATIVE_EDIT: '/app/academy/assessment/kg-assessment/formative/edit',
        UPDATE_FORMATIVE: '/app/academy/assessment/kg-update-formative-title',
        GRADE_OPTIONS: '/app/academy/assessment/kg-get-grade-options',
        CREATE_ASSESSMENT: '/app/academy/assessment/kg-create',
        SAVE_FORMATIVE: '/app/academy/assessment/kg-formative-save'
    };

    // State management
    const state = {
        clickedTermId: null,
        clickedGradeId: null,
        clickedFormativeAssessment: null
    };

    const modalUtils = {
        cleanup: function (modalId) {
            const $modal = $(modalId);
            const $body = $('body');

            // Force cleanup everything
            $modal.hide();
            $('.modal-backdrop').remove();
            $body
                .removeClass('modal-open')
                .css('padding-right', '')
                .css('overflow', '')
                .css('height', '');
            // Reset modal state
            $modal
                .removeClass('show')
                .removeAttr('aria-modal')
                .attr('aria-hidden', 'true')
                .css('display', 'none');
        },

        cleanupAndReload: function (modalId, reloadCallback) {
            this.cleanup(modalId);

            // Small delay before reload to ensure modal cleanup is complete
            setTimeout(() => {
                if (typeof reloadCallback === 'function') {
                    reloadCallback();
                }
            }, 100);
        }
    };


    // Initial setup
    $.ajaxSetup({
        headers: { 'X-CSRF-Token': $('meta[name=_token]').attr('content') }
    });

    initializeMenu();
    initializeTables();

    // Event handlers
    const eventHandlers = {
        viewKGFormative: function (e) {
            state.clickedFormativeAssessment = $(this).data('id');
            $('#formativeViewDetailsDataArea').load(`${ENDPOINTS.FORMATIVE_DATA_VIEW}?id=${state.clickedFormativeAssessment}`);
        },
        editKGFormative: function (e) {
            state.clickedFormativeAssessment = $(this).data('id');
            $('#formativeDetailsDataArea').load(`${ENDPOINTS.FORMATIVE_DATA}?id=${state.clickedFormativeAssessment}`);
        },

        searchAssessment: function (e) {
            const params = {
                type: $('#assessmentType').val(),
                subject: $('#subjectList').val()
            };

            showLoader();
            $.get(ENDPOINTS.SEARCH_TABLE, params)
                .done(response => {
                    $('#assessmentListArea').html(response);
                    makeDataTable('assessmentKgTable');
                })
                .fail(() => {
                    $('#assessmentListArea').html('<p class="text-danger text-center">Failed to load data.</p>');
                })
                .always(hideLoader);
        },

        deleteKgFormative: function (e) {
            const assessmentId = $(this).data('id');

            Swal.fire({
                title: 'Are you sure?',
                text: 'This action cannot be undone.',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Delete',
                cancelButtonText: 'Cancel',
            }).then(result => {
                if (result.isConfirmed) {
                    showLoader();
                    $.post(ENDPOINTS.DELETE_FORMATIVE, { assessmentId })
                        .done(reloadSelectedGrade)
                        .fail(xhr => parseException(xhr))
                        .always(hideLoader);
                }
            });
        },

        editFormative: function (e) {
            const id = $(this).data('id');
            $('#modalFormativeEdit').modal('show');
            $('#formativeEditArea').load(`${ENDPOINTS.FORMATIVE_EDIT}?id=${id}`);
        },

        saveKgFormativeName: function (e) {
            const data = {
                assessmentId: $('#editFormativeAssessmentId').val(),
                title: $.trim($('#editFormativeAssessmentName').val()),
                date: $('#editFormativeAssessmentDate').val(),
                opt: $('#editFormativeAssessmentOption').val()
            };

            if (!data.title) return;

            showLoader();
            $.post(ENDPOINTS.UPDATE_FORMATIVE, data)
                .done(() => {
                    modalUtils.cleanupAndReload('#modalFormativeEdit', reloadSelectedGrade);
                })
                .fail(xhr => parseException(xhr))
                .always(hideLoader);
        },

        handleSessionKgRow: function (e) {
            $('.sessionKgRow').removeClass('activeRow');
            $(this).addClass('activeRow');

            const data = {
                gradeId: $(this).data('grade'),
                subjectId: $(this).data('subject')
            };
            $('#na_grade_id').val(data['gradeId']);
            $('#na_subject_id').val(data['subjectId']);

            showLoader();
            $.post(ENDPOINTS.GRADE_OPTIONS, data)
                .done(handleGradeOptionsResponse)
                .fail(xhr => parseException(xhr))
                .always(hideLoader);

            $('#summativeOptTable')
                .css('opacity', 1)
                .css('pointer-events', 'auto');
        },

        submitNewKgAssessment: function (e) { 
            const formData = validateForm('#formNewAssessment');
            if (formData === 'fail') return;

            showLoader();
            $.ajax({
                type: 'POST',
                url: ENDPOINTS.CREATE_ASSESSMENT,
                data: formData,
                contentType: false,
                processData: false,
                cache: false,
                enctype: "multipart/form-data"
            })
                .done(handleNewAssessmentResponse)
                .fail(xhr => parseException(xhr))
                .always(hideLoader);
        },

        handleSubjectKGRow: function (e) {
            state.clickedGradeId = $(this).data('id');
            state.clickedTermId = $('#semester').val();

            $('.subjectKGRow').removeClass('active');
            $(this).addClass('active');

            $('#menu_tab_formative').tab('show');
            $('#searchByTerm').show();

            reloadSelectedGrade();
        },

        submitKgFormativeData: function (e) {
            const dataToSend = collectFormativeData();
            if (!dataToSend.length) {
                console.log('No data to save');
                return;
            }

            $.post(ENDPOINTS.SAVE_FORMATIVE, { dataToSend })
                .done(() => {
                    modalUtils.cleanupAndReload('#modalFormativeData', () => {
                        Swal.fire({
                            position: 'top-end',
                            icon: 'success',
                            title: 'Work Saved',
                            showConfirmButton: false,
                            timer: 1500
                        });
                        reloadSelectedGrade();
                    });
                })
                .fail(() => console.error('Error updating assessment data'))
                .always(hideLoader);
        }
    };

    // Helper function for modal cleanup and reloading
    function hideModal(modalId) {
        modalUtils.cleanup(modalId);
    }

    // Helper functions
    function initializeMenu() {
        $('#menuAcademy').addClass('here show');
        $('#menuAcademyKgAssessment').addClass('active');
    }

    function initializeTables() {
        makeDataTable('formativeTable');
        makeDataTable('aListTable');
    }
    function handleGradeOptionsResponse(data) {
        $('#assessmentOption')
          .html(data.gradeOptions)
          .select2(); // Initialize Select2 first
      
        updateLearningAreas(data.learningAreas);
      
       
      }

    function updateLearningAreas(areas) {
        const select = $('#learningAreasSelect');
        select.empty().append('<option value="0">Select Learning Area</option>');
    
        if (areas.length) {
            areas.forEach(area => {
                select.append(`<option value="${area.id}">${area.name}</option>`);
            });
        } else {
            select.append('<option value="0">No Learning Areas Available</option>');
        }
        
         // Apply the white-space style *after* Select2 is initialized
         $('#learningAreasSelect').on('select2:open', function() {
            $('.select2-selection__rendered').css({ // Use the class you found!
              'white-space': 'pre-wrap',
              'word-wrap': 'break-word'
            });
          });
    
    } 

    function handleNewAssessmentResponse(data) {
        hideModal('#modalNewAssessment');
        $('#formNewAssessment')[0].reset();
        document.getElementById('menu_tab_formative').click();

        $('#modalFormativeData').modal('show');
        $('#formativeDetailsDataArea').load(`${ENDPOINTS.FORMATIVE_DATA}?id=${data}`);
    }

    function collectFormativeData() {
        const dataToSend = [];
        $('.scoreRowFormative').each(function () {
            const $row = $(this);
            const studentId = $row.data('student');
            const $markSelect = $row.find('.mark-select');
            const mark = $markSelect.val();
            const comment = $row.find('.comment-input').val();

            if (mark || comment) {
                dataToSend.push({
                    student_id: studentId,
                    assessment_id: $markSelect.data('assessment-id'),
                    mark,
                    comment
                });
            }
        });
        return dataToSend;
    }

    function reloadSelectedGrade() {
    if (!state.clickedGradeId) {
        Swal.fire({
            title: "Oops...",
            text: "Grade is not selected!",
            icon: "warning"
        });
        return;
    }

    showLoader();

    $.ajax({
        url: `/app/academy/assessment/kg-assessment/assessment_formative`,
        data: {
            id: state.clickedGradeId,
            termId: state.clickedTermId
        },
        method: 'GET',
        success: function (response) {
            $('#tab_kg_formative').html(response);
            makeDataTable('formativeTable');
        },
        error: function (xhr) {
            hideLoader();
            let message = "Something went wrong!";
            if (xhr.responseJSON && xhr.responseJSON.error_code === 'TERM_NOT_FOUND') {
                message = xhr.responseJSON.message;
            }
            Swal.fire({
                title: "Error",
                text: message,
                icon: "error"
            });
        }
    });
    hideLoader();
}


    // Event bindings
    $(document)
        .on('click', '.btnViewKGFormative', eventHandlers.viewKGFormative)
        .on('click', '.btnEditKGFormative', eventHandlers.editKGFormative)
        .on('click', '#searchAssessment', eventHandlers.searchAssessment)
        .on('click', '.btnDeleteKgFormative', eventHandlers.deleteKgFormative)
        .on('click', '.btnEditFormative', eventHandlers.editFormative)
        .on('click', '#btnSaveKgFormativeName', eventHandlers.saveKgFormativeName)
        .on('click', '.sessionKgRow', eventHandlers.handleSessionKgRow)
        .on('click', '#btnSubmitNewKgAssessment', eventHandlers.submitNewKgAssessment)
        .on('click', '.subjectKGRow', eventHandlers.handleSubjectKGRow)
        .on('click', '#btnSubmitKgFormativeData', eventHandlers.submitKgFormativeData)
        .on('click', '#menu_tab_archive', () => $('#searchByTerm').hide())
        .on('click', '#menu_tab_formative', () => $('#searchByTerm').show());
});