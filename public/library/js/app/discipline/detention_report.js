$.ajaxSetup({
    headers: { "X-CSRF-Token": $("meta[name=_token]").attr("content") },
});

$(document).ready(function () {
    $("#menuDiscipline").addClass("here");
    $("#menuDiscipline").addClass("show");
    $("#menuDisciplineDetentionReport").addClass("active");
    makeDateRangeBySelector("#dateRange");
    makeDateRangeBySelector("#dateRangeTotalReport");
    loadDetentionRecords();
    $(document).on("change", "#dateType", function () {
        var type = $("#dateType").val();
        if (
            type == "over-all" ||
            type == "week" ||
            type == "month" ||
            type == "term"
        ) {
            $("#divDateRange").hide("fast");
        } else if (type == "custom") {
            $("#divDateRange").show("fast");
        }
    });
});
$(document).on("change", ".detentionDropdown", function () {
    var recordId = $(this).closest("tr").attr("data-id");
    var detentionValue = $(this).closest("tr").find(".detentionDropdown").val();
    $.ajax({
        type: "POST",
        url: "/app/discipline/detention-report/detention-record",
        data: {
            detention_record_id: recordId,
            detention_type: detentionValue,
        },
        success: function (msg) {
            // location.reload();
        },
        error: function (xhr, status, error) {
            hideLoader();
            parseException(xhr);
        },
    });
});
$(document).on("click", ".btnDeleteRecord", function () {
    var recordId = $(this).closest("tr").attr("data-id");
    if (confirm("Are you sure ?")) {
        showLoader();
        $.ajax({
            type: "POST",
            url: "/app/discipline/detention-report/delete-record",
            data: { detentionId: recordId },
            success: function (msg) {
                location.reload();
            },
            error: function (xhr, status, error) {
                hideLoader();
                parseException(xhr);
            },
        });
    }
});
function loadDetentionRecords(data) {
    var $isVicePrincipal = $("#kt_app_main").data("role");
    var $isVicePrincipal = $("#kt_app_main").attr("data-role");
    if ($.fn.DataTable.isDataTable("#tableDetentionRecordList")) {
        $("#tableDetentionRecordList").DataTable().destroy();
    }
    $("#tableDetentionRecordList").DataTable({
        language: {
            lengthMenu: "Show _MENU_",
        },
        buttons: [
            {
                extend: "excelHtml5",
                text: '<i class="fas fa-file-excel"></i>',
                className: "btn-sm btn-primary",
                exportOptions: {
                    format: {
                        body: function (inner, rowidx, colidx, node) {
                            var originalValue = node.textContent.trim();
                            // make detention Dropdown to get selected value
                            if (colidx === 7) {
                                var iconHTML =
                                    '<i class="fa-solid fa-check ms-2 text-success h5"></i>';
                                if (node.innerHTML.includes(iconHTML)) {
                                    return "served"; // Return the desired text when the icon is present
                                }
                            } else if (colidx == 5) {
                                if ($(node).children("select").length > 0) {
                                    var selectNode = node.firstElementChild;
                                    var txt =
                                        selectNode.options[
                                            selectNode.selectedIndex
                                        ].value;
                                    return txt;
                                } else {
                                    return inner;
                                }
                            } else {
                                return originalValue;
                            }
                        },
                    },
                },
            },
            {
                extend: "pdfHtml5",
                text: '<i class="fas fa-file-pdf"></i>',
                className: "btn-sm btn-primary",
                exportOptions: {
                    format: {
                        body: function (inner, rowidx, colidx, node) {
                            var originalValue = node.textContent.trim();
                            // make detention Dropdown to get selected value
                            if (colidx === 7) {
                                var iconHTML =
                                    '<i class="fa-solid fa-check ms-2 text-success h5"></i>';
                                if (node.innerHTML.includes(iconHTML)) {
                                    return "served"; // Return the desired text when the icon is present
                                }
                            } else if (colidx == 5) {
                                if ($(node).children("select").length > 0) {
                                    var selectNode = node.firstElementChild;
                                    var txt =
                                        selectNode.options[
                                            selectNode.selectedIndex
                                        ].value;
                                    return txt;
                                } else {
                                    return inner;
                                }
                            } else {
                                return originalValue;
                            }
                        },
                    },
                },
            },
        ],
        processing: true,
        serverSide: true,
        ajax: {
            url: "/app/discipline/detention-report/get-detention-records",
            type: "POST", // Use POST method if required
            data: data,
        },
        order: [],
        deferRender: true,
        pageLength: 15,
        lengthMenu: [15, 25, 50, 75, 100, 500],
        columns: [
            { data: "student_id", name: "student_id" },
            { data: "student_name", name: "student_name" },
            { data: "classroom_name", name: "classroom_name" },
            { data: "latest_point", name: "latest_point" },
            { data: "date", name: "date" },
            { data: "served_detention_type", name: "served_detention_type" },
            { data: "system_note", name: "system_note" },
            { data: "served", name: "is_served" },
            { data: "name", name: "name" },
            {
                data: "detention_record_id",
                name: "Act",
                render: function (row, data, dataIndex) {
                    if ($isVicePrincipal == "true") {
                        return '<a href="#" class="me-2" id="btnServeRecord" ><i class="fas fa-check"></i></a><a href="#"  class="btnDeleteRecord" title="Delete record"><i class="fas fa-trash"></i></a>';
                    }
                    return "";
                },
            },
        ],
        createdRow: function (row, data, dataIndex) {
            $(row).addClass("recordRow");
            $(row).attr("data-id", data.detention_record_id);
            $("td", row).eq(12).addClass("text-end").css("width", "80px");
        },
        dom:
            "<'row'<'col-sm-4 d-flex align-items-center justify-conten-start'l>" +
            "<'col-sm-8 d-flex align-items-center justify-content-end'fB>>" +
            "<'table-responsive'tr><'row'" +
            "<'col-sm-12 col-md-5 d-flex align-items-center justify-content-center justify-content-md-start'i>" +
            "<'col-sm-12 col-md-7 d-flex align-items-center justify-content-center justify-content-md-end'p>>",
    });
}
$(document).on("click", "#btnSearchBps", function () {
    var classList = $("#classList").val();
    var student = $("#student").val();
    var dateType = $("#dateType").val();
    var dateRange = $("#dateRange").val();
    var detentionType = $("#detentionTypeSearch").val();
    if (dateType == "custom") {
        if (dateRange.includes("to")) {
            var dateArray = dateRange.split(" to ");
            var startDate = dateArray[0];
            var endDate = dateArray[1];
        } else {
            var startDate = dateRange;
            var endDate = dateRange;
        }
    }
    data = {
        classList: classList,
        student: student,
        dateType: dateType,
        detentionType: detentionType,
        startDate: startDate,
        endDate: endDate,
    };
    loadDetentionRecords(data);
});
// $(document).on("click", "#btnSuspesionRecord", function () {
//     var recordId = $(this).closest("tr").attr("data-id");
//     $("#modalBPS").modal("show");
//     $("#bpsArea").load("/app/discipline/detention-report/add-bps/" + recordId);
// });
$(document).on("click", "#btnSaveDetention", function () {
    var validated = validateForm("#formBPS");
    if (validated !== "fail") {
        showLoader();
        $.ajax({
            type: "POST",
            url: "/app/discipline/detention-report/detention-record",
            data: validated,
            contentType: false,
            processData: false,
            cache: false,
            enctype: "multipart/form-data",
            success: function (msg) {
                location.reload();
            },
            error: function (xhr, status, error) {
                hideLoader();
                parseException(xhr);
            },
        });
    }
});
$(document).on("click", "#btnServeRecord", function () {
    $.ajax({
        url: "/app/discipline/detention-report/serve-record",
        type: "post",
        data: { recordId: $(this).closest("tr").attr("data-id") },
        success: function (data) {
            Swal.fire({
                position: "top-end",
                icon: "success",
                title: "Your work has been saved",
                showConfirmButton: false,
                timer: 1500,
            });
            var classList = $("#classList").val();
            var student = $("#student").val();
            var dateType = $("#dateType").val();
            var dateRange = $("#dateRange").val();
            var detentionType = $("#detentionTypeSearch").val();
            if (dateType == "custom") {
                if (dateRange.includes("to")) {
                    var dateArray = dateRange.split(" to ");
                    var startDate = dateArray[0];
                    var endDate = dateArray[1];
                } else {
                    var startDate = dateRange;
                    var endDate = dateRange;
                }
            }
            data = {
                classList: classList,
                student: student,
                dateType: dateType,
                detentionType: detentionType,
                startDate: startDate,
                endDate: endDate,
            };
            loadDetentionRecords(data);
        },
        error: function (xhr, textStatus, error) {
            hideLoader();
            parseException(xhr);
        },
    });
});
