$(document).ready(function(){
    flatpickr("input[type=time]", {
        enableTime: true,
        noCalendar: true,
        dateFormat: "H:i",
        minuteIncrement: 1,
        time_24hr: true,
        allowInput: true // Allow manual input from the keyboard
    });

    $('#menuHealth').addClass('active');
    makeDataTable('tableStudents');
    makeDataTable('tableStaff');
    makeDataTable('tableGuest');
    makeDataTable('tableInfoStudents');
    makeDataTable('tableInfoStaff');
    makeDataTable('tableInfoGuest');


    function getStudentData(studentId) {
        $('#studentDetailArea').html('<p class="text-center"><img src="/img/loader.gif" style="width: 30px;" /></p>');
        $('#studentDetailArea').load('/app/health/student/show-detail?studentId=' + studentId, function(){
            makeDataTable('tableStudentRecords');
            makeDate();
            $('#formStudentHealthInfo select').select2();
            $('#btnAddRecord').show('fast');
            $('#btnSubmitHealthInfo').hide('fast');

            $("input[type=time]").flatpickr({
                enableTime: true,
                noCalendar: true,
                dateFormat: "H:i",
                minuteIncrement: 1,
                time_24hr: true,
                allowInput: true // Allow manual input from the keyboard
            });


            $('#formNewStudentRecord select').select2();
        });
    }

    function getStaffData(userId) {
        $('#staffDetailArea').html('<p class="text-center"><img src="/img/loader.gif" style="width: 30px;" /></p>');
        $('#staffDetailArea').load('/app/health/staff/show-detail?userId=' + userId, function(){
            makeDataTable('tableStaffRecords');
            makeDate();
            $('#btnAddStaffRecord').show('fast');

            $("input[type=time]").flatpickr({
                enableTime: true,
                noCalendar: true,
                dateFormat: "H:i",
                minuteIncrement: 1,
                time_24hr: true,
                allowInput: true // Allow manual input from the keyboard
            });

            $('#formNewStaffRecord select').select2();
        });
    }

    var clickedStaffId = null;

    $(document).on('click', '.staffInfoRow', function(){
        $('.staffInfoRow').removeClass('activeRow');
        $(this).addClass('activeRow');
        clickedStaffId = $(this).attr('data-id');
        getStaffData(clickedStaffId)
    });

    var clickedStudentId = null;
    $(document).on('click', '.studentInfoRow', function(){
        $('.studentInfoRow').removeClass('activeRow');
        $(this).addClass('activeRow');
        clickedStudentId = $(this).attr('data-id');
        getStudentData(clickedStudentId)
    });

    $(document).on('click', '#menuTabRecord', function(){
        $('#btnSubmitHealthInfo').hide('fast');
        $('#btnAddRecord').show('fast');
    });

    $(document).on('click', '#menuTabInformation', function(){
        $('#btnSubmitHealthInfo').show('fast');
        $('#btnAddRecord').hide('fast');
    });

    $(document).on('click', '#menuTabStaffInformation', function(){
        $('#btnSubmitStaffHealthInfo').show('fast');
        $('#btnAddRecord').hide('fast');
    });

    $(document).on('click', '#menuTabStaffRecord', function(){
        $('#btnSubmitStaffHealthInfo').hide('fast');
        $('#btnAddRecord').hide('fast');
    });

    $(document).on('click', '#menuTabMeasurements', function(){
        $('#btnSubmitHealthInfo').hide('fast');
        $('#btnSubmitStudentMeasurementRecord').show('fast');
    });

    $(document).on('click', '#btnSubmitStaffHealthInfo', function(){
        var validated = validateForm('#formStaffHealthInfo');
        if(validated !== 'fail') {
            showLoader();
            $.ajax({
                type: 'POST',
                url: '/app/health/staff/save-info',
                data: validated,
                contentType: false,
                processData: false,
                cache: false,
                enctype: "multipart/form-data",
                success: function (msg) {
                    hideLoader();
                    Swal.fire({
                        position: 'top-end',
                        icon: 'success',
                        title: Lang.get('js.work_saved'),
                        showConfirmButton: false,
                        timer: 1500
                    });
                },
                error: function (xhr, status, error) {
                    hideLoader();
                    parseException(xhr);
                }
            });
        }
    });

    $(document).on('click', '#btnSubmitHealthInfo', function(){
        var validated = validateForm('#formStudentHealthInfo');
        console.log(validated)
        if(validated !== 'fail') {
            showLoader();
            $.ajax({
                type: 'POST',
                url: '/app/health/student/save-info',
                data: validated,
                contentType: false,
                processData: false,
                cache: false,
                enctype: "multipart/form-data",
                success: function (msg) {
                    hideLoader();
                    Swal.fire({
                        position: 'top-end',
                        icon: 'success',
                        title: Lang.get('js.work_saved'),
                        showConfirmButton: false,
                        timer: 1500
                    });
                },
                error: function (xhr, status, error) {
                    hideLoader();
                    parseException(xhr);
                }
            });
        }
    });

    $(document).on('click', '#btnSubmitStudentRecord', function(){
        var validated = validateForm('#formNewStudentRecord');
        if(validated !== 'fail') {
            showLoader();
            $.ajax({
                type: 'POST',
                url: '/app/health/student/create-record',
                data: validated,
                contentType: false,
                processData: false,
                cache: false,
                enctype: "multipart/form-data",
                success: function (msg) {
                    hideLoader();
                    getStudentData(clickedStudentId);
                },
                error: function (xhr, status, error) {
                    hideLoader();
                    parseException(xhr);
                }
            });
        }
    });

    $(document).on('click', '#btnSubmitStudentMeasurementRecord', function(){
        var validated = validateForm('#formStudentHealthMeasurements');
        if(validated !== 'fail') {
            showLoader();
            $.ajax({
                type: 'POST',
                url: '/app/health/student/create-measure-record',
                data: validated,
                contentType: false,
                processData: false,
                cache: false,
                enctype: "multipart/form-data",
                success: function (msg) {
                    hideLoader();
                    getStudentData(clickedStudentId);
                },
                error: function (xhr, status, error) {
                    hideLoader();
                    parseException(xhr);
                }
            });
        }
    });

    $(document).on('click', '#btnDeleteMeasurement', function() {
        var recordId = $(this).data('id'); // Get the record ID

        if (confirm('Are you sure you want to delete this measurement?')) {
            $.ajax({
                type: 'POST',
                url: '/app/health/student/delete-measure-record/' + recordId,
                success: function(response) {
                    alert(response.message); // Display success message
                    // Remove the row from the table
                    $(this).closest('tr').remove();
                }.bind(this),
                error: function(xhr, status, error) {
                    alert('Error deleting record: ' + xhr.responseJSON.message); // Handle error
                }
            });
        }
    });

    $(document).on('click', '.btnDeleteStudentRecord', function(){
        Swal.fire({
            title: Lang.get('js.are_you_sure_to_delete'),
            text: Lang.get('js.can_not_revert'),
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: Lang.get('js.yes_delete'),
            cancelButtonText: Lang.get('js.cancel'),
        }).then((result) => {
            if (result.isConfirmed) {
                showLoader();
                $.ajax({
                    type: 'POST',
                    url: '/app/health/student/delete-record',
                    data: {'recordId': $(this).attr('data-id')},
                    success: function (msg) {
                        hideLoader();
                        getStudentData(clickedStudentId);
                    },
                    error: function (xhr, status, error) {
                        hideLoader();
                        parseException(xhr);
                    }
                });
            }
        })
    });

    $(document).on('click', '.btnDeleteGuestRecord', function(){
        Swal.fire({
            title: Lang.get('js.are_you_sure_to_delete'),
            text: Lang.get('js.can_not_revert'),
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: Lang.get('js.yes_delete'),
            cancelButtonText: Lang.get('js.cancel'),
        }).then((result) => {
            if (result.isConfirmed) {
                showLoader();
                $.ajax({
                    type: 'POST',
                    url: '/app/health/guest/delete-record',
                    data: {'recordId': $(this).attr('data-id')},
                    success: function (msg) {
                        location.reload();
                    },
                    error: function (xhr, status, error) {
                        hideLoader();
                        parseException(xhr);
                    }
                });
            }
        })
    });

    $(document).on('click', '#btnSubmitStaffRecord', function(){
        var validated = validateForm('#formNewStaffRecord');
        if(validated !== 'fail') {
            showLoader();
            $.ajax({
                type: 'POST',
                url: '/app/health/staff/create-record',
                data: validated,
                contentType: false,
                processData: false,
                cache: false,
                enctype: "multipart/form-data",
                success: function (msg) {
                    hideLoader();
                    getStaffData(clickedStaffId);
                },
                error: function (xhr, status, error) {
                    hideLoader();
                    parseException(xhr);
                }
            });
        }
    });

    $(document).on('click', '#btnSubmitGuestRecord', function(){
        var validated = validateForm('#formNewGuestRecord');
        if(validated !== 'fail') {
            showLoader();
            $.ajax({
                type: 'POST',
                url: '/app/health/guest/create-record',
                data: validated,
                contentType: false,
                processData: false,
                cache: false,
                enctype: "multipart/form-data",
                success: function (msg) {
                    location.reload();
                },
                error: function (xhr, status, error) {
                    hideLoader();
                    parseException(xhr);
                }
            });
        }
    });

    $(document).on('click', '.btnDeleteStaffRecord', function(){
        Swal.fire({
            title: Lang.get('js.are_you_sure_to_delete'),
            text: Lang.get('js.can_not_revert'),
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: Lang.get('js.yes_delete'),
            cancelButtonText: Lang.get('js.cancel'),
        }).then((result) => {
            if (result.isConfirmed) {
                showLoader();
                $.ajax({
                    type: 'POST',
                    url: '/app/health/staff/delete-record',
                    data: {'recordId': $(this).attr('data-id')},
                    success: function (msg) {
                        hideLoader();
                        getStaffData(clickedStaffId);
                    },
                    error: function (xhr, status, error) {
                        hideLoader();
                        parseException(xhr);
                    }
                });
            }
        })
    });

// Reports
    $('#reportType').change(function() {
        var reportType = $(this).val();

        $.ajax({
            type: 'GET',
            url: '/app/reports/health/list-info',
            data: { reportType: reportType },
            success: function(response){
                // Check if the DataTable is already initialized
                if ($.fn.DataTable.isDataTable('#tableReport')) {
                    // Destroy the existing instance
                    $('#tableReport').DataTable().destroy();
                }

                // Clear the existing table contents (optional, based on your requirement)
                $('#tableReport tbody').empty();

                // Update the table with the returned partial view
                $('#tableReport').html(response.reportHtml);
                makeDataTable('tableReport');
            },
            error: function(error) {
                console.log('Error fetching report details:', error);
            }


        });

    });

});