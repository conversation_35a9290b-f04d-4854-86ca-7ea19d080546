$(document).ready(function () {

    $.ajaxSetup({
        headers: { "X-CSRF-Token": $("meta[name=_token]").attr("content") },
    });
    $("#menuAdministration").addClass("here");
    $("#menuAdministration").addClass("show");
    $("#menuAdministrationAcademicSettings").addClass("active");

    //makeDataTable('activityTypeTable');
    makeDataTable("strandSkillsTable");
    makeDataTable("assignedStrandSkillTable");
    makeDataTable("excludedSubjectTable");
    makeDataTable("AcademicYearTable");
    //makeDataTable('attributeTable');
    //makeDataTable('pointsTable');
});

$(document).on("click", "#tabAS", function () {
    $("#btnAddSubject").hide();
    $("#btnSaveBellTimes").hide();
    $("#btnAssignSS").hide();
    $("#btnAcademicSemester").show();
    $('#btnAddLearningArea').hide();
    $('#btnAddPreviousLearningArea').hide();
});

$(document).on("click", "#tabSS", function () {
    $("#btnAddSubject").hide();
    $("#btnSaveBellTimes").hide();
    $("#btnAcademicSemester").hide();
    $("#btnAssignSS").show();
    $('#btnAddLearningArea').hide();
    $('#btnAddPreviousLearningArea').hide();
});

$(document).on("click", "#tabExcludedSubjects", function () {
    $("#btnAddSubject").show();
    $("#btnAssignSS").hide();
    $("#btnAcademicSemester").hide();
    $("#btnSaveBellTimes").hide();
    $('#btnAddLearningArea').hide();
    $('#btnAddPreviousLearningArea').hide();
});

$(document).on("click", "#tabAssessments", function () {
    $("#btnAddSubject").hide();
    $("#btnAssignSS").hide();
    $("#btnAcademicSemester").hide();
    $("#btnSaveBellTimes").hide();
    $('#btnAddLearningArea').hide();
    $('#btnAddPreviousLearningArea').hide();
});

$(document).on('click', '#tabLearningArea', function () {
        $("#btnAddSubject").hide();
        $("#btnAssignSS").hide();
        $("#btnAcademicSemester").hide();
        $("#btnSaveBellTimes").hide();
        $('#btnAddLearningArea').show();
        $('#btnAddPreviousLearningArea').show();

        showLoader();
        loadLearningAreas();
    });

/*
$(document).on('click', '#tabBellTimes', function(){
    $('#btnAddSubject').hide();
    $('#btnAssignSS').hide();
    $('#btnSaveBellTimes').show();
});

$(document).on('click', '#tabLbcSettings', function(){
    $('#btnAddSubject').hide();
    $('#btnAssignSS').hide();
    $('#btnSaveBellTimes').hide();
});
*/
$(document).on("click", "#btnAssignSS", function (e) {
    e.preventDefault(); // Prevent default anchor behavior
    var modal = new bootstrap.Modal(document.getElementById("modalAssignSS"));
    modal.show();
});
$(document).on("click", "#btnAddSubject", function (e) {
    e.preventDefault(); // Prevent default anchor behavior
    var modal = new bootstrap.Modal(document.getElementById("modalAddSubject"));
    modal.show();
    // console.log("hi");
});
$(document).on("click", "#btnSubmitExcludedSubject", function () {
    showLoader();
    $("#excludedClassroomList").trigger("change");
    // var classrooms = $('#excludedClassroomList').serialize();

    $.ajax({
        url: "/app/administration/academic-settings/add-excluded-subject",
        type: "post",
        data: {
            subjectId: $("#excludedSubjectList").val(),
            classrooms: $("#excludedClassroomList").val(),
        },
        success: function (data) {
            hideLoader();
            hideModal("#modalAddSubject");
            $("#tabExcluded").load(
                "/app/administration/academic-settings/show-excluded-subject",
                function () {
                    makeDataTable("excludedSubjectTable");
                }
            );
            var modal = new bootstrap.Modal(
                document.getElementById("modalAddSubject")
            );
            // Clear and reset select2 dropdowns
            $("#excludedSubjectList").val(null).trigger("change");
            $("#excludedClassroomList").val([]).trigger("change");
        },
        error: function (xhr, textStatus, error) {
            hideLoader();
            parseException(xhr);
        },
    });
});

$(document).on("click", ".btnDeleteSubject", function () {
    Swal.fire({
        title: "Are you sure?",
        text: "You won't be able to revert this!",
        icon: "warning",
        showCancelButton: true,
        confirmButtonColor: "#3085d6",
        cancelButtonColor: "#d33",
        confirmButtonText: "Yes, delete it!",
    }).then((result) => {
        if (result.isConfirmed) {
            showLoader();
            $.ajax({
                url: "/app/administration/academic-settings/delete-excluded-subject",
                type: "post",
                data: { subjectId: $(this).attr("data-id") },
                success: function (data) {
                    hideLoader();
                    $("#tabExcluded").load(
                        "/app/administration/academic-settings/show-excluded-subject",
                        function () {
                            makeDataTable("excludedSubjectTable");
                        }
                    );
                },
                error: function (xhr, textStatus, error) {
                    hideLoader();
                    parseException(xhr);
                },
            });
        }
    });
});
/*

$(document).on('click', '#btnSaveBellTimes', function(){
    showLoader();

    var bellData = '';

    for (let day = 1; day <= 6; day++) {
        for (let session = 1; session <= 9; session++) {
            bellData += day + '/!*!/' + session + '/!*!/' + $('#' + day + '_' + session + '_start').val() + '/!*!/' + $('#' + day + '_' + session + '_end').val() + '|*|';
        }
    }

    $.ajax({
        url: '/app/administration/academic-settings/save-bell-times',
        type: 'post',
        data: { 'bellData': bellData },
        success: function(data){
            hideLoader();
            Swal.fire({
                position: 'top-end',
                icon: 'success',
                title: 'Your work has been saved',
                showConfirmButton: false,
                timer: 1500
            });
        },
        error: function(xhr, textStatus, error){
          hideLoader();
          parseException(xhr);
        }
    });
});

$(document).on('click', '.btnEditAttribute', function(){
    $('#modalEditAttribute').modal('show');
    $('#learnerAttributeTitle').val($(this).attr('data-title'));
    $('#learnerAttributeInfo').val($(this).attr('data-info'));
    $('#btnSaveAtribute').attr('data-id', $(this).attr('data-id'));
});

$(document).on('click', '#btnSaveAtribute', function(){
    if($.trim($('#learnerAttributeTitle').val()) != '' && $.trim($('#learnerAttributeInfo').val()) != '') {
        showLoader();
        $.ajax({
            url: '/app/administration/academic-settings/lbc-save-attribute',
            type: 'post',
            data: {
                'id'    : $(this).attr('data-id'),
                'title' : $('#learnerAttributeTitle').val(),
                'info'  : $('#learnerAttributeInfo').val()
            },
            success: function(data){
                location.reload();
            },
            error: function(xhr, textStatus, error){
              hideLoader();
              parseException(xhr);
            }
        });
    } else {
        Swal.fire('Title & info is required!');
    }
});

$(document).on('click', '.btnEditPoint', function(){
    $('#modalEditPoint').modal('show');
    $('#pointTitle').val($(this).attr('data-title'));
    $('#pointScore').val($(this).attr('data-score'));
    $('#btnSavePoint').attr('data-id', $(this).attr('data-id'));
});

$(document).on('click', '#btnSavePoint', function(){
    if($.trim($('#pointTitle').val()) != '' && $.trim($('#pointScore').val()) != '') {
        showLoader();
        $.ajax({
            url: '/app/administration/academic-settings/lbc-save-point',
            type: 'post',
            data: {
                'id'    : $(this).attr('data-id'),
                'title' : $('#pointTitle').val(),
                'score'  : $('#pointScore').val()
            },
            success: function(data){
                location.reload();
            },
            error: function(xhr, textStatus, error){
              hideLoader();
              parseException(xhr);
            }
        });
    } else {
        Swal.fire('Title & info is required!');
    }
});

$(document).on('click', '.btnEditType', function(){
    $('#modalEditType').modal('show');
    $('#typeTitle').val($(this).attr('data-title'));
    $('#btnSaveType').attr('data-id', $(this).attr('data-id'));
});

$(document).on('click', '#btnSaveType', function(){
    if($.trim($('#typeTitle').val()) != '') {
        showLoader();
        $.ajax({
            url: '/app/administration/academic-settings/lbc-save-type',
            type: 'post',
            data: {
                'id'    : $(this).attr('data-id'),
                'title' : $('#typeTitle').val(),
            },
            success: function(data){
                location.reload();
            },
            error: function(xhr, textStatus, error){
              hideLoader();
              parseException(xhr);
            }
        });
    } else {
        Swal.fire('Title & info is required!');
    }
});
*/

$(document).on("click", "#btnSubmitSS", function () {
    showLoader();
    $.ajax({
        url: "/app/administration/academic-settings/add-strand-skill",
        type: "post",
        data: {
            type: $("#ssType").val(),
            title: $("#ssTitle").val(),
        },
        success: function (data) {
            hideLoader();
            hideModal("#modalAddSS");
            window.location.reload();
        },
        error: function (xhr, textStatus, error) {
            hideLoader();
            parseException(xhr);
        },
    });
});

$(document).on("click", ".btnDeleteSS", function () {
    Swal.fire({
        title: "Are you sure?",
        text: "You won't be able to revert this!",
        icon: "warning",
        showCancelButton: true,
        confirmButtonColor: "#3085d6",
        cancelButtonColor: "#d33",
        confirmButtonText: "Yes, delete it!",
    }).then((result) => {
        if (result.isConfirmed) {
            showLoader();
            $.ajax({
                url: "/app/administration/academic-settings/delete-strand-skill",
                type: "post",
                data: { id: $(this).attr("data-id") },
                success: function (data) {
                    hideLoader();
                    window.location.reload();
                },
                error: function (xhr, textStatus, error) {
                    hideLoader();
                    parseException(xhr);
                },
            });
        }
    });
});

$(document).on("click", "#btnModalAssignSS", function () {
    showLoader();
    $.ajax({
        url: "/app/administration/academic-settings/assign-ss",
        type: "post",
        data: {
            term: $("#termSS").val(),
            grades: $("#modalGradeList").val(),
            strand_skill: $("#modalSSlist").val(),
        },
        success: function (data) {
            hideLoader();
            hideModal("#modalAssignSS");
            // window.location.reload();
            $("#tabStrandSkill").html(
                '<p style="text-align: center;"><img src="/img/loader.gif" style="width: 30px;" /></p>'
            );
            $("#tabStrandSkill").load(
                window.location.href + " #tabStrandSkill > *",
                function () {
                    makeDataTable("strandSkillsTable");
                    makeDataTable("assignedStrandSkillTable");
                }
            );
            var modal = new bootstrap.Modal(
                document.getElementById("modalAssignSS")
            );
            // Clear and reset select2 dropdowns
            $("#modalGradeList").val([]).trigger("change");
            $("#modalSSlist").val([]).trigger("change");
        },
        error: function (xhr, textStatus, error) {
            hideLoader();
            parseException(xhr);
        },
    });
});

$(document).on("click", ".btnDeleteAssignedSS", function () {
    Swal.fire({
        title: "Are you sure?",
        text: "You won't be able to revert this!",
        icon: "warning",
        showCancelButton: true,
        confirmButtonColor: "#3085d6",
        cancelButtonColor: "#d33",
        confirmButtonText: "Yes, delete it!",
    }).then((result) => {
        if (result.isConfirmed) {
            showLoader();
            $.ajax({
                url: "/app/administration/academic-settings/delete-assigned-strand-skill",
                type: "post",
                data: { id: $(this).attr("data-id") },
                success: function (data) {
                    hideLoader();
                    // window.location.reload();
                    $("#tabStrandSkill").html(
                        '<p style="text-align: center;"><img src="/img/loader.gif" style="width: 30px;" /></p>'
                    );
                    $("#tabStrandSkill").load(
                        window.location.href + " #tabStrandSkill > *",
                        function () {
                            makeDataTable("strandSkillsTable");
                            makeDataTable("assignedStrandSkillTable");
                        }
                    );
                    var modal = new bootstrap.Modal(
                        document.getElementById("modalAssignSS")
                    );
                },
                error: function (xhr, textStatus, error) {
                    hideLoader();
                    parseException(xhr);
                },
            });
        }
    });
});
$(document).on("click", "#btnLockAllAssessments", function () {
    Swal.fire({
        title: "Are you sure?",
        text: "You are now locking/unlocking all the Assessments in this Year!",
        icon: "info",
        showCancelButton: true,
        confirmButtonColor: "#3085d6",
        cancelButtonColor: "#d33",
        confirmButtonText: "Yes, Do it!",
    }).then((result) => {
        if (result.isConfirmed) {
            showLoader();
            $.ajax({
                url: "/app/administration/academic-settings/lock-all-assessments",
                type: "post",
                success: function (data) {
                    hideLoader();
                    Swal.fire({
                        position: "top-end",
                        icon: "success",
                        title: "Your work has been saved",
                        showConfirmButton: false,
                        timer: 1500,
                    });
                    window.location.reload();
                },
                error: function (xhr, textStatus, error) {
                    hideLoader();
                    parseException(xhr);
                },
            });
            hideLoader();
            // window.location.reload();
        }
    });
});
$(document).on("click", "#btnLockAssessmentByDate", function () {
    var date = $("#inputDate").val();
    showLoader();
    $.ajax({
        url: "/app/administration/academic-settings/lock-assessments-by-date",
        type: "post",
        data: { date: date, is_locked: 1 },
        success: function (data) {
            hideLoader();
            Swal.fire({
                position: "top-end",
                icon: "success",
                title: "Your work has been saved",
                showConfirmButton: false,
                timer: 1500,
            });
            window.location.reload();
        },
        error: function (xhr, textStatus, error) {
            hideLoader();
            parseException(xhr);
        },
    });
    hideLoader();
});
$(document).on("click", "#btnUnLockAssessmentByDate", function () {
    var date = $("#inputUnLockDate").val();
    showLoader();
    $.ajax({
        url: "/app/administration/academic-settings/lock-assessments-by-date",
        type: "post",
        data: { date: date, is_locked: 0 },
        success: function (data) {
            hideLoader();
            Swal.fire({
                position: "top-end",
                icon: "success",
                title: "Your work has been saved",
                showConfirmButton: false,
                timer: 1500,
            });
            window.location.reload();
        },
        error: function (xhr, textStatus, error) {
            hideLoader();
            parseException(xhr);
        },
    });
    hideLoader();
});
$(document).on("click", ".academicYearRow", function () {
    $(".academicYearRow").removeClass("activeRow");
    $(this).addClass("activeRow");
    $("#academicSemesterArea").html(
        '<p style="text-align: center;"><img src="/img/loader.gif" style="width: 30px;" /></p>'
    );
    $("#academicSemesterArea").load(
        "/app/administration/academic-settings/get-academic-semester?academicYearId=" +
        $(this).attr("data-year_id") +
        "&branchId=" +
        $(this).attr("data-branch_id"),
        function () {
            makeDataTable("AcademicSemesterTable");
        }
    );
});
$(document).on("click", "#btnCreateAcademicSemester", function () {
    var academicYearId = $("#academicYearList").val();
    var academicSemester = $("#termAcademicYearSemester").val();
    var startDate = $("#aSStartDate").val();
    var endDate = $("#aSEndDate").val();
    var status = $("#aSStatus").val();
    if (
        academicYearId !== "" &&
        academicSemester !== "" &&
        startDate !== "" &&
        endDate !== ""
    ) {
        showLoader();
        $.ajax({
            url: "/app/administration/academic-settings/add-academic-semester",
            type: "post",
            data: {
                academicYearId: academicYearId,
                academicSemester: academicSemester,
                startDate: startDate,
                endDate: endDate,
                status: status,
            },
            success: function (data) {
                hideLoader();
                window.location.reload();
            },
            error: function (xhr, textStatus, error) {
                hideLoader();
                parseException(xhr);
            },
        });
    } else {
        Swal.fire("All Fields are required!");
    }
});

$(document).on("click", ".btnEditSemester", function () {
    $("#modalAcademicSemesterEdit").modal("show");
    $("#academicSemesterEditArea").html(
        '<p style="text-align: center;"><img src="/img/loader.gif" style="width: 30px;" /></p>'
    );
    $("#academicSemesterEditArea").load(
        "/app/administration/academic-settings/academic-semester-edit?semesterId=" +
        $(this).attr("data-id")
    );
});

$(document).on("click", "#btnEditAcademicSemester", function () {
    var academicYearId = $("#academicYearEdit").val();
    var academic_semester_id = $("#semesterEdit").val();
    var startDate = $("#aSStartDateEdit").val();
    var endDate = $("#aSEndDateEdit").val();
    var status = $("#aSStatusEdit").val();
    if (startDate !== "" && endDate !== "") {
        showLoader();
        $.ajax({
            url: "/app/administration/academic-settings/add-academic-semester",
            type: "post",
            data: {
                academicYearId: academicYearId,
                academic_semester_id: academic_semester_id,
                startDate: startDate,
                endDate: endDate,
                status: status,
            },
            success: function (data) {
                hideLoader();
                window.location.reload();
            },
            error: function (xhr, textStatus, error) {
                hideLoader();
                parseException(xhr);
            },
        });
    } else {
        Swal.fire("All Fields are required!");
    }
});

$(document).on('click', '#btnSelectAllGrade', function () {
    var $select = $('#modalGradeList');
    var $button = $(this);

    if ($button.text() === 'Select All') {
        // Select all non-empty options 
        var allValues = $select.find('option[value!=" "]').map(function () {
            return $(this).val();
        }).get();

        $select.val(allValues).trigger('change');
        $button.text('Unselect All').removeClass('btn-danger').addClass('btn-warning');
    } else {
        // Unselect all options
        $select.val([]).trigger('change');
        $button.text('Select All').removeClass('btn-warning').addClass('btn-danger');
    }
});

// Learning Areas
$(document).on('click', '#btnAddLearningArea', function () {
    $('#modalAddLearningArea').modal('show');
    $('#modalLearningArea').load('/app/administration/academic-settings/learning-area/show-create-form', function () {
        $('select[name="grade[]"]').select2({
            width: '100%',
            dropdownParent: $('#modalAddLearningArea'),
            multiple: true
        });
    });
});

$(document).on('click', '#btnAddPreviousLearningArea', function () {
    $('#importPreviousYearModal').modal('show');

    $('#importPreviousYearModalBody').load('/app/administration/academic-settings/learning-area/import-previous-year', function () {
        initializeImportEvents();
    });

});

$(document).on('click', '#btnSaveLearningArea', function () {
    var validated = validateForm('#formAddLearningArea');
    if (validated !== 'fail') {
        showLoader();
        $.ajax({
            type: 'POST',
            url: '/app/administration/academic-settings/learning-area/store',
            data: validated,
            contentType: false,
            processData: false,
            cache: false,
            enctype: "multipart/form-data",
            success: function (response) {
                console.log(response);
                $('#modalAddLearningArea').modal('hide');
                hideLoader();

                // Join grade names into a string for display with badges
                let gradeBadges = response.grade.map(grade => {
                    return `<span class="badge badge-primary">${grade}</span>`;
                }).join(' ');

                const newRow = `<tr>
                    <td>${response.id}</td>
                    <td>${response.name}</td>
                    <td>${gradeBadges}</td>
                    <td>${response.subject}</td>
                    <td>
                        <a class="btnEditLA" href="#" data-id="{{$response->id}}"><i class="fas fa-edit"></i></a>&nbsp;
                        <a class="btnDeleteLA" href="#" data-id="{{$response->id}}"><i class="fas fa-trash"></i></a>
                    </td>
                </tr>`;
                $('#learningAreaTable tbody').append(newRow);
            },
            error: function (xhr, status, error) {
                hideLoader();
                parseException(xhr);
            }
        });
    }
});

$(document).on('click', '.btnEditLA', function () {
    $('#modalEditLearningArea').modal('show');
    $('#updateLearningArea').load('/app/administration/academic-settings/learning-area/show-edit-form/' + $(this).attr('data-id'), function () {
        $('select[name="grade[]"]').select2({
            width: '100%',
            dropdownParent: $('#modalEditLearningArea'),
            multiple: true
        });
    });
});

$(document).on('click', '.btnDeleteLA', function () {
    if (confirm('Are you sure to delete?')) {
        showLoader();

        $.ajax({
            url: '/app/administration/academic-settings/learning-area/delete',
            type: 'POST',
            data: {
                'learning_area_id': $(this).attr('data-id'),
            },
            success: function (response) {
                hideLoader();
                loadLearningAreas();
            },
            error: function (xhr, textStatus, error) {
                hideLoader();
                parseException(xhr);
            }
        });
    }
});

$(document).on('click', '#btnUpdateLearningArea', function () {
    var formId = '#formEditLearningArea'; // Form ID for editing
    var validated = validateForm(formId);
    if (validated !== 'fail') {
        showLoader();
        $.ajax({
            type: 'POST',
            url: '/app/administration/academic-settings/learning-area/update', // Endpoint for saving
            data: validated,
            contentType: false,
            processData: false,
            success: function (response) {
                hideLoader();
                $('#modalEditLearningArea').modal('hide');
                loadLearningAreas();

            },
            error: function (xhr) {
                hideLoader();
                parseException(xhr);
            },
        });
    }
});


function initializeImportEvents() {
    // Handle year selection change
    $('#previous_academic_year').change(function () {
        const yearId = $(this).val();

        if (yearId) {
            // Show loading indicator
            $('#previous_learning_areas_tbody').html('<tr><td colspan="3" class="text-center">Loading...</td></tr>');
            $('#previous_learning_areas_container').show();

            // Fetch learning areas for selected year
            $.ajax({
                url: "/app/administration/academic-settings/learning-area/get-by-year",
                type: "GET",
                data: { academic_year_id: yearId },
                success: function (response) {
                    if (response.learning_areas.length > 0) {
                        let rows = '';

                        $.each(response.learning_areas, function (index, area) {
                            const statusText = area.status == 1 ? 'Active' : 'Inactive';
                            const statusBadge = area.status == 1 ?
                                '<span class="badge badge-success">Active</span>' :
                                '<span class="badge badge-danger">Inactive</span>';

                            rows += `
                                        <tr>
                                            <td>
                                                <input type="checkbox" class="learning_area_checkbox" 
                                                       data-uid="${area.uid}" 
                                                       data-name="${area.name}">
                                            </td>
                                            <td>${area.name}</td>
                                            <td>${statusBadge}</td>
                                        </tr>
                                    `;
                        });

                        $('#previous_learning_areas_tbody').html(rows);
                        $('#import_selected_btn').prop('disabled', true);
                    } else {
                        $('#previous_learning_areas_tbody').html('<tr><td colspan="3" class="text-center">No learning areas found for this academic year</td></tr>');
                        $('#import_selected_btn').prop('disabled', true);
                    }
                },
                error: function () {
                    $('#previous_learning_areas_tbody').html('<tr><td colspan="3" class="text-center text-danger">Error loading learning areas</td></tr>');
                    $('#import_selected_btn').prop('disabled', true);
                }
            });
        } else {
            $('#previous_learning_areas_container').hide();
            $('#import_selected_btn').prop('disabled', true);
        }
    });

    // Handle select all checkbox
    $(document).on('change', '#select_all_areas', function () {
        $('.learning_area_checkbox').prop('checked', $(this).prop('checked'));
        updateImportButtonState();
    });

    // Handle individual checkboxes
    $(document).on('change', '.learning_area_checkbox', function () {
        updateImportButtonState();
    });

    // Import selected learning areas
    $(document).on('click', '#import_selected_btn', function () {
        const selectedAreas = [];

        $('.learning_area_checkbox:checked').each(function () {
            selectedAreas.push({
                uid: $(this).data('uid'),
                name: $(this).data('name'),
                grades: $(this).data('grades')
            });
        });

        if (selectedAreas.length > 0) {
            $.ajax({
                url: "/app/administration/academic-settings/learning-area/import",
                type: "POST",
                data: {
                    _token: $('meta[name="csrf-token"]').attr('content'),
                    learning_areas: selectedAreas
                },
                success: function (response) {
                    if (response.success) {
                        $('#importPreviousYearModal').modal('hide');
                        toastr.success(response.message);
                        // Reload the page
                        showLoader();
                        loadLearningAreas();

                    } else {
                        toastr.error(response.message);
                    }
                },
                error: function (xhr) {
                    const errorMsg = xhr.responseJSON ? xhr.responseJSON.message : 'An error occurred while importing learning areas';
                    toastr.error(errorMsg);
                }
            });
        }
    });
}

function updateImportButtonState() {
    const anyChecked = $('.learning_area_checkbox:checked').length > 0;
    $('#import_selected_btn').prop('disabled', !anyChecked);
}

function loadLearningAreas() {

    $.ajax({
        url: '/learning-areas',
        method: 'GET',
        success: function (response) {
            $('#learning-area-content').html(response);
            makeDataTable('learningAreaTable');
            hideLoader();
        },
        error: function () {
            alert('Failed to load Learning Areas.');
        }
    });


}

$(document).on('change','#midInput', function(){
    var midTermPercentage = $(this).val();
    var midTermBranchId = $(this).attr('data-branch-id');
    if(midTermPercentage !='' && midTermPercentage !=0 && midTermPercentage <= 100){
        // alert(midTermBranchId);
                    $.ajax({
                url: "/app/administration/academic-settings/add-final-mid-exam-percentage",
                type: "post",
                data:{
                    branchId:midTermBranchId,
                    value:midTermPercentage,
                    type:'mid',
                },
                success: function (data) {
                    hideLoader();
                    Swal.fire({
                        position: "top-end",
                        icon: "success",
                        title: "Your work has been saved",
                        showConfirmButton: false,
                        timer: 1500,
                    });
                    // window.location.reload();
                },
                error: function (xhr, textStatus, error) {
                    hideLoader();
                    parseException(xhr);
                },
            });
    }else if (midTermPercentage ==0){
        Swal.fire("Mid Term % Cannot Be 0%");
    }else if (midTermPercentage > 100){
        Swal.fire("Mid Term % Cannot Exceed 100%");
    }
    else {
        Swal.fire("Mid Term % Cannot Be Empty");
    }
});
$(document).on('change','#finalInput', function(){
    var finalTermPercentage = $(this).val();
    var finalTermBranchId = $(this).attr('data-branch-id');
    if(finalTermPercentage !='' && finalTermPercentage !=0 && finalTermPercentage <= 100){
        // alert(midTermBranchId);
                    $.ajax({
                url: "/app/administration/academic-settings/add-final-mid-exam-percentage",
                type: "post",
                data:{
                    branchId:finalTermBranchId,
                    value:finalTermPercentage,
                    type:'final',
                },
                success: function (data) {
                    hideLoader();
                    Swal.fire({
                        position: "top-end",
                        icon: "success",
                        title: "Your work has been saved",
                        showConfirmButton: false,
                        timer: 1500,
                    });
                    // window.location.reload();
                },
                error: function (xhr, textStatus, error) {
                    hideLoader();
                    parseException(xhr);
                },
            });
    }else if (finalTermPercentage ==0){
        Swal.fire("Final Term % Cannot Be 0%");
    }else if (finalTermPercentage > 100){
        Swal.fire("Final Term % Cannot Exceed 100%");
    }
    else {
        Swal.fire("Final Term % Cannot Be Empty");
    }
})
