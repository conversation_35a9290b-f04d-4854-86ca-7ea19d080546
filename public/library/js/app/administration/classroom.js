$.ajaxSetup({
  headers: { "X-CSRF-Token": $("meta[name=_token]").attr("content") },
});

$(document).ready(function () {
  $("#menuAdministration").addClass("here");
  $("#menuAdministration").addClass("show");
  $("#menuAdministrationClasses").addClass("active");
  makeDataTable("classroomTable");
  $(document).on("change", "#caseType", function () {
    var caseType = $("#caseType").val();

    if (caseType == 1) {
      $("#dps_area").show();
      $("#prs_area").hide();
      $("#prs_area select").val("").trigger("change.select2");
    } else if (caseType == 0) {
      $("#prs_area").show();
      $("#dps_area").hide();
      $("#dps_area select").val("").trigger("change.select2");
    }
  });
  $(document).on("change", "#classroom_import_year", function () {
    var yearId = $("#classroom_import_year").val();
    if (yearId > 0) {
      showLoader();
      $.ajax({
        type: "POST",
        url: "/app/administration/classes/get-year-classrooms",
        data: { yearId: yearId },
        success: function (data) {
          hideLoader();
          $("#classroom_import_classroom").html(data);
        },
        error: function (xhr, status, error) {
          hideLoader();
          parseException(xhr);
        },
      });
    } else {
      $("#classroom_import_classroom").html("");
    }
  });
});

var clickedClassroomId = null;
var checkedIds = [];

$(document).on("click", ".classRow", function () {
  $(".classRow").removeClass("activeRow");
  $(this).addClass("activeRow");
  clickedClassroomId = $(this).attr("data-id");
  getClassDetails(clickedClassroomId);
});

function getClassDetails(classroomId) {
  $("#classroomDetailArea").load(
    "/app/administration/classes/get-students?classroomId=" + classroomId,
    function (responseText, textStatus, req) {
      if (textStatus == "error") {
        showError("Something went wrong!");
      }
      makeDataTable("classroomDetailTable");
      $("#addClassroomStudent").show("fast");
      hideLoader();
    }
  );
}

$(document).on("click", "#btnAddClassroom", function () {
  $("#classroomResponseArea").load(
    "/app/administration/classes/show-create-form"
  );
});

$(document).on("click", ".btnEditClass", function () {
  $("#classroomResponseArea").load(
    "/app/administration/classes/show-edit-form?classroomId=" +
      $(this).attr("data-id")
  );
  $("#modalAddClassroom").modal("show");
});

$(document).on("click", "#btnSaveClassroom", function () {
  var validated = validateForm("#formClassroom");
  if (validated !== "fail") {
    showLoader();
    $.ajax({
      type: "POST",
      url: "/app/administration/classes/store",
      data: validated,
      contentType: false,
      processData: false,
      cache: false,
      enctype: "multipart/form-data",
      success: function (msg) {
        location.reload();
      },
      error: function (xhr, status, error) {
        hideLoader();
        parseException(xhr);
      },
    });
  }
});

$(document).on("click", ".btnDeleteClass", function () {
  Swal.fire({
    title: "Are you sure?",
    text: "You won't be able to revert this!",
    icon: "warning",
    showCancelButton: true,
    confirmButtonColor: "#3085d6",
    cancelButtonColor: "#d33",
    confirmButtonText: "Yes, delete it!",
  }).then((result) => {
    if (result.isConfirmed) {
      showLoader();
      $.ajax({
        type: "POST",
        url: "/app/administration/classes/delete",
        data: { classroomId: $(this).attr("data-id") },
        success: function (msg) {
          location.reload();
        },
        error: function (xhr, status, error) {
          hideLoader();
          parseException(xhr);
        },
      });
    }
  });
});

$(document).on("click", "#btnSaveStudents", function () {
  var students = $("#studentList").val();
  showLoader();

  $.ajax({
    type: "POST",
    url: "/app/administration/classes/add-student",
    data: { classroomId: clickedClassroomId, students: students },
    success: function (msg) {
      $("#modalAddStudents").modal("hide");
      $(".modal-backdrop").remove();
      getClassDetails(clickedClassroomId);
    },
    error: function (xhr, status, error) {
      hideLoader();
      parseException(xhr);
    },
  });
});

$(document).on("click", ".btnDeleteStudent", function () {
  if (confirm("Are you sure ?")) {
    showLoader();
    $.ajax({
      type: "POST",
      url: "/app/administration/classes/delete-student",
      data: {
        classroomId: clickedClassroomId,
        studentId: $(this).attr("data-id"),
      },
      success: function (msg) {
        $("#modalAddStudents").modal("hide");
        $(".modal-backdrop").remove();
        getClassDetails(clickedClassroomId);
      },
      error: function (xhr, status, error) {
        hideLoader();
        parseException(xhr);
      },
    });
  }
});

$(document).on("change", "#classroom_import", function () {
  var importOpt = $("#classroom_import").val();

  if (importOpt == 1) {
    $(".divImportOptions").css("opacity", "1");
    $(".divImportOptions").css("pointer-events", "auto");
  } else {
    $(".divImportOptions").css("opacity", "0.5");
    $(".divImportOptions").css("pointer-events", "none");
  }
});

// $(document).on("click", "#btnSaveDailyAttendance", function () {
//     var attendanceData = "";

//     $(".attendanceRow").each(function () {
//         var studentId = $(this).attr("data-id");
//         $(".attendanceBtn_" + studentId).each(function () {
//             if ($(this).hasClass("btn-primary")) {
//                 var type = $(this).attr("data-type");
//                 var attendanceNote = $.trim(
//                     $("#attendanceNote_" + studentId).val()
//                 );
//                 var lateMinus = $("#lateMinus_" + studentId).val();
//                 attendanceData +=
//                     studentId +
//                     "|" +
//                     type +
//                     "|" +
//                     attendanceNote +
//                     "|" +
//                     lateMinus +
//                     "/";
//             }
//         });
//     });
//     console.log(attendanceData);

//     showLoader();

//     $.ajax({
//         url: "/app/home/<USER>/save-daily-attendance",
//         type: "post",
//         data: { attendance: attendanceData },
//         success: function (data) {
//             hideLoader();
//             Swal.fire({
//                 position: "top-end",
//                 icon: "success",
//                 title: "Attendance has been saved",
//                 showConfirmButton: false,
//                 timer: 1500,
//             });
//         },
//         error: function (xhr, textStatus, error) {
//             Swal.fire("Error occured!", xhr.responseText);
//             $("#load_screen").hide();
//         },
//     });
// });

$(document).on("click", ".attendanceBtn", function () {
  var studentId = $(this).attr("data-student-id");
  var type = $(this).attr("data-type");
  $(".attendanceBtn_" + studentId).removeClass("btn-primary");
  $(".attendanceBtn_" + studentId).removeClass("btn-secondary");
  $(".attendanceBtn_" + studentId).addClass("btn-secondary");
  $(this).removeClass("btn-secondary");
  $(this).addClass("btn-primary");
  if (type == "present" || type == "absent") {
    $(this)
      .closest("td") // Find the closest <td> ancestor
      .find(".lateMinus") // Find all elements with the class .lateMinus within that <td>
      // .closest("div") // Optionally, find the closest ancestor <div> if needed
      // .removeClass("bg-primary");
      .val("");
    $(this).closest("td").find(".dropdown-content").hide();
  }
});

$(document).on("click", ".btnViewStudentDetail", function () {
  $("#modalStudentInfo").modal("show");
  $("#responseArea").load(
    "/app/administration/classes/view-student-form?studentId=" +
      $(this).attr("data-id"),
    function () {
      $("select").select2();
    }
  );
});
$(document).on("click", ".lateBtn", function () {
  var type = $(this).attr("data-type");
  var id = $(this).closest("div").attr("id");
  $(this).closest("td").find(".dropdown-content").toggle();
});
// $(document).on("click", ".lateMinus", function () {
//     // Remove bg-primary class from all .lateMinus elements within the same parent container
//     $(this)
//         .closest(".dropdown-content")
//         .find(".lateMinus")
//         .closest("div")
//         .removeClass("bg-primary");

//     // Add bg-primary class to the clicked element
//     $(this).closest("div").addClass("bg-primary");
// });
$(document).on("change", "#selectAllRow", function () {
  $(".rowCheckbox").prop("checked", $(this).prop("checked"));

  if ($(this).prop("checked")) {
    $(".rowCheckbox:checked").each(function () {
      var id = $(this).closest("tr").data("id");
      checkedIds.push(id);
    });
  } else {
    checkedIds = [];
  }
});
$(document).on("change", ".rowCheckbox", function () {
  var clickedCheckboxId = $(this).attr("id");
  // $(".rowCheckbox:checked").each(function () {
  //     var id = $(this).closest("tr").data("id");
  //     checkedIds.push(id);
  // });

  if ($(this).prop("checked")) {
    var id = $("#" + clickedCheckboxId)
      .closest("tr")
      .data("id");
    checkedIds.push(id);
  } else {
    // Checkbox is unchecked, remove its ID from the array
    var index = checkedIds.indexOf(
      $("#" + clickedCheckboxId)
        .closest("tr")
        .data("id")
    );
    if (index !== -1) {
      checkedIds.splice(index, 1);
    }
  }
});
$(document).on("click", "#btnAddBPS", function () {
  $("#modalBPS").modal("show");
  $("#bpsArea").load(
    "/app/academy/homeroom/add-bps",
    { checkedIds: checkedIds },
    function () {
      $("#formBPS select").select2();
      makeDate();
    }
  );
});
$(document).on("click", "#addBpsforSingleStudent", function () {
  $("#modalBPS").modal("show");
  var checkedIds = [];
  var id = $(this).closest("tr").data("id");
  checkedIds.push(id);
  $("#bpsArea").load(
    "/app/academy/homeroom/add-bps",
    { checkedIds: checkedIds },
    function () {
      $("#formBPS select").select2();
      makeDate();
    }
  );
});
$(document).on("click", "#btnSaveBPS", function () {
  var validated = validateForm("#formBPS");
  if (validated !== "fail") {
    showLoader();
    $.ajax({
      type: "POST",
      url: "/app/discipline/store-bps",
      data: validated,
      contentType: false,
      processData: false,
      cache: false,
      enctype: "multipart/form-data",
      success: function (msg) {
        location.reload();
      },
      error: function (xhr, status, error) {
        hideLoader();
        parseException(xhr);
      },
    });
  }
});
$(document).on("click", "#btnResetPasswordClass", function () {
  var classroomId = $("#btnResetPasswordClass").attr("data-class-id");
  Swal.fire({
    title: "Are you sure?",
    text: "You won't be able to revert this!",
    icon: "warning",
    showCancelButton: true,
    confirmButtonColor: "#3085d6",
    cancelButtonColor: "#d33",
    confirmButtonText: "Yes, Reset!",
  }).then((result) => {
    if (result.isConfirmed) {
      window.location.href =
        "/app/administration/classes/reset-password-classroom?classroomId=" +
        classroomId;
      setTimeout(() => {
        Swal.fire({
          position: "top-end",
          icon: "info",
          title:
            "Student passwords will be changed and exported. If successful, the downloaded file will appear in the top-right corner of your browser.",
          showConfirmButton: false,
          timer: 3000,
        });
      }, 1000);
    }
  });
});
