$.ajaxSetup({
    headers: { 'X-CSRF-Token' : $('meta[name=_token]').attr('content') }
});

$(document).ready(function(){
    $('#menuAdministration').addClass('here');
    $('#menuAdministration').addClass('show');
    $('#menuAdministrationTeacherClassLesson').addClass('active');

    makeDataTable('tclTable');
});

$(document).on('click', '#btnSubmitTcl', function(){
    var validated = validateForm('#formNewTcl');
    if(validated !== 'fail') {
        showLoader();
        $.ajax({
          type: 'POST',
          url: '/app/administration/teacher-class-lesson/create',
          data: validated,
          contentType: false,
          processData: false,
          cache: false,
          enctype: "multipart/form-data",
          success: function (msg) {
            location.reload();
          },
          error: function (xhr, status, error) {
            hideLoader();
            parseException(xhr);
          }
        });
    }
});

$(document).on('click', '.btnDeleteTCL', function(){
    if(confirm('Are you sure ?')) {
        showLoader();
        $.ajax({
            type: 'POST',
            url: '/app/administration/teacher-class-lesson/delete',
            data: { 'tclId' : $(this).attr('data-id') },
            success: function (msg) {
              location.reload();
            },
            error: function (xhr, status, error) {
              hideLoader();
              parseException(xhr);
            }
          });
    }
});
$(document).on("click", ".btnEditTCL", function (e) {
  $("#modalEditTCL").modal("show");
  $("#updateTCLArea").html(
    '<p style="text-align: center;"><img src="/img/loader.gif" style="width: 30px;" /></p>'
);
  $('#updateTCLArea').load('/app/administration/teacher-class-lesson/show-edit-tcl?tclId='
    + $(this).attr('data-id'),function(){
      makeDate();
    }
  );
});
$(document).on('click','#btnUpdateTCL',function(){
  var startDate = $('#startDate').val();
  var endDate = $('#endDate').val();
  var weeklyCount = $('#weeklyCount').val();
  var tclId = $('#tclId').val();
  if(startDate != '' && endDate != ''){
        showLoader();
    $.ajax({
        url: "/app/administration/teacher-class-lesson/update",
        type: "post",
        data: {
            startDate: startDate,
            endDate: endDate,
            weeklyCount: weeklyCount,
            tclId: tclId,
        },
        success: function (data) {
            window.location.reload();
        },
        error: function (xhr, textStatus, error) {
            hideLoader();
            parseException(xhr);
        },
    });
  }else{
    Swal.fire("Please Fill All Field!");
  }
})