$(document).ready(function () {
    $("#menuAdministration").addClass("here");
    $("#menuAdministration").addClass("show");
    $("#menuAdministrationAssessment").addClass("active");
        $("#missingStrandCollapseModal").hide();
    $("#missingImpactCollapseModal").hide();
        $(document).on('click','#missingStrandCollapse',function(){
        $("#missingStrandCollapseModal").toggle('hide');
    })
    $(document).on('click','#missingImpactCollapse',function(){
        $("#missingImpactCollapseModal").toggle('hide');
    })
    $("#summativeTable").DataTable({
        dom:
            "<'row'<'col-sm-6 d-flex align-items-center justify-conten-start'l>" +
            "<'col-sm-6 d-flex align-items-center justify-content-end'fB>>" +
            "<'table-responsive'tr><'row'" +
            "<'col-sm-12 col-md-5 d-flex align-items-center justify-content-center justify-content-md-start'i>" +
            "<'col-sm-12 col-md-7 d-flex align-items-center justify-content-center justify-content-md-end'p>>",
        lengthMenu: [
            [25, 50, -1],
            [25, 50, "All"],
        ],
        buttons: [
            {
                extend: "excel",
                text: "Export to Excel",
                text: '<i class="fas fa-file-excel"></i>',
                className: "btn-sm btn-primary",
                exportOptions: {
                    format: {
                        body: function (data, row, column, node) {
                            if ($(node).find("input").length) {
                                return $(node).find("input").val();
                            }
                            return data;
                        },
                    },
                },
            },
        ],
    });
    makeDataTable("formativeTable");
});
$(document).on("click", "#summativeSemesterSearch", function (event) {
    var semester = $("#semester").val();
    var gradeId = $("#gradeId").val();
    showLoader();
    window.location.href =
        "/app/administration/assessment-administration/assessment_grade_detail?grade_id=" +
        gradeId +
        "&searchSemester=" +
        semester +
        "&#tab_summative";
});
$(document).on("click", "#formativeSemesterSearch", function (event) {
    var semester = $("#formativeSemester").val();
    var gradeId = $("#gradeId").val();
    showLoader();
    window.location.href =
        "/app/administration/assessment-administration/assessment_grade_detail?grade_id=" +
        gradeId +
        "&searchSemester=" +
        semester +
        "&#tab_formative";
});
