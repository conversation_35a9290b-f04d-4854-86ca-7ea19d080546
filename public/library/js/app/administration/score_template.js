$.ajaxSetup({
    headers: { "X-CSRF-Token": $("meta[name=_token]").attr("content") },
});

$(document).ready(function () {
    $("#menuAdministration").addClass("here");
    $("#menuAdministration").addClass("show");
    $("#menuAdministrationSummativeScoreTemplates").addClass("active");
    makeDataTable("templateListTable");

    $(document).on("change", "#subjectList", function () {
        var subjectId = $("#subjectList").val();
        if (subjectId > 0) {
            showLoader();
            $.ajax({
                url: "/app/administration/summative-score-template/get-subject-grade",
                type: "post",
                data: { subjectId: subjectId },
                success: function (data) {
                    hideLoader();
                    $("#gradeList").html(data);
                },
                error: function (xhr, textStatus, error) {
                    hideLoader();
                    parseException(xhr);
                },
            });
        }
    });
    $(document).on("change", "#gradeList", function () {
        var grade = $("#gradeList").val();
        if (grade.length > 0) {
            showLoader();
            $.ajax({
                url: "/app/administration/summative-score-template/find-template-grade",
                type: "post",
                data: { gradeIds: grade },
                success: function (data) {
                    hideLoader();
                    $("#alert-banner").html(data.message);
                    if (data.disable == "true") {
                        $("#btnSubmitTemplate").prop("disabled", true);
                    } else {
                        $("#btnSubmitTemplate").prop("disabled", false);
                    }
                },
                error: function (xhr, textStatus, error) {
                    hideLoader();
                    parseException(xhr);
                },
            });
        } else {
            $("#alert-banner").html("");
            $("#btnSubmitTemplate").prop("disabled", false);
        }
    });
    $(document).on("change", "#gradeListEdit", function () {
        var grade = $("#gradeListEdit").val();
        if (grade.length > 0) {
            showLoader();
            $.ajax({
                url: "/app/administration/summative-score-template/find-template-grade",
                type: "post",
                data: { gradeIds: grade },
                success: function (data) {
                    hideLoader();
                    $("#grade-alert-banner").html(data.message);
                    if (data.disable == "true") {
                        $("#btnSubmitGrade").prop("disabled", true);
                    } else {
                        $("#btnSubmitGrade").prop("disabled", false);
                    }
                },
                error: function (xhr, textStatus, error) {
                    hideLoader();
                    parseException(xhr);
                },
            });
        } else {
            $("#grade-alert-banner").html("");
            $("#btnSubmitGrade").prop("disabled", false);
        }
    });
    $(document).on('change','#templateOptionIsFinalAndMidTerm',function(){
        var isChecked = $(this).val();
        // alert(isChecked)
        if(isChecked == 1){
             $("#optionTbody").html("");
            $.ajax({
                    url: "/app/administration/summative-score-template/find-final-mid-term-percentage",
                    type: "post",
                    success: function (data) {
                        if (data) {
                        var remainPercentage = calculateTotalPercentage(".optionRow");
                        const finalTitle = 'Final';
                        const midTitle = 'Final/Mid Term';
                        if((remainPercentage + parseInt(data.setting_value_3) + parseInt(data.setting_value_2)) <=100){
                        // Final Row
                        // $("#optionTbody").append(
                        //     '<tr class="optionRow" data-title="' + finalTitle +
                        //     '" data-percent="' + data.setting_value_3 +
                        //     '" data-is-final="1">' +
                        //         '<td style="padding-left: 10px;">' + finalTitle + '</td>' +
                        //         '<td style="padding-left: 10px;">' + data.setting_value_3 + '</td>' +
                        //         '<td style="text-align: right;"><i class="fas fa-check text-success"></i></td>' +
                        //         '<td style="padding-right: 10px; text-align: right;"><a href="#" class="btnRemoveOption"><i class="fas fa-trash"></i></a></td>' +
                        //     '</tr>'
                        // );
                       // Minor Row
                        $("#optionTbody").append(
                            '<tr class="optionRow" data-title="Minor" data-percent="60" data-is-final="0">' +
                                '<td style="padding-left: 10px;">Minor</td>' +
                                '<td style="padding-left: 10px;">60</td>' +
                                '<td style="text-align: right;"><i class="fas fa-xmark text-danger"></i></td>' +
                                '<td style="padding-right: 10px; text-align: right;"></td>' +
                            '</tr>'
                        );
                        // Mid Term Row
                        $("#optionTbody").append(
                            '<tr class="optionRow" data-title="' + midTitle +
                            '" data-percent="' + data.setting_value_2 +
                            '" data-is-final="1">' +
                                '<td style="padding-left: 10px;">' + midTitle + '</td>' +
                                '<td style="padding-left: 10px;">' + data.setting_value_2 + '</td>' +
                                '<td style="text-align: right;"><i class="fas fa-check text-success"></i></td>' +
                                '<td style="padding-right: 10px; text-align: right;"></td>' +
                            '</tr>'
                        );
                        var totalPercentage =calculateTotalPercentage(".optionRow");
                        $("#totalPercentage").html(totalPercentage + "%");                           
                        }else {
                            Swal.fire("Total percentage can not exceed 100% ! Please Select Final and Mid Term % First");
                        }
                    }
                    },
                    error: function (xhr, textStatus, error) {
                        hideLoader();
                        parseException(xhr);
                    },
                });
        }else{
             $("#optionTbody").html("");
                $("#optionTbody").append(
                '<tr class="optionRow" data-title="Minor" data-percent="100" data-is-final="0">' +
                    '<td style="padding-left: 10px;">Minor</td>' +
                    '<td style="padding-left: 10px;">100</td>' +
                    '<td style="text-align: right;"><i class="fas fa-xmark text-danger"></i></td>' +
                    '<td style="padding-right: 10px; text-align: right;"></td>' +
                '</tr>'
            );
        }
});
});

var clickedTemplateId = null;

$(document).on("click", ".templateRow", function () {
    clickedTemplateId = $(this).attr("data-id");
    $(".templateRow").removeClass("activeRow");
    $(this).addClass("activeRow");
    getTemplateDetails(clickedTemplateId);
});

function getTemplateDetails(templateId) {
    $("#templateDetailArea").html(
        '<p style="text-align: center;"><img src="/img/loader.gif" style="width: 30px;" /></p>'
    );
    $("#templateDetailArea").load(
        "/app/administration/summative-score-template/show-template-detail?templateId=" +
            templateId
    );
}

$(document).on("click", "#btnAddTemplate", function () {
    $("#formNewTemplate")[0].reset();
    $("#subjectList").trigger("change.select2");
    $("#gradeList").trigger("change.select2");
    $("#optionTbody").html("");
    $("#totalPercentage").html("");
});

function calculateTotalPercentage(selector) {
    var total = 0;
    $(selector).each(function () {
        total += parseInt($(this).attr("data-percent"));
    });
    return total;
}

$(document).on("click", "#btnAddOption", function () {
    var title = $.trim($("#optionTitle").val());
    var percent = $("#optionPercentage").val();
    // var isFinalExamPercentage = $("#isFinalExamPercentage").val();
    if (percent == "") {
        percent = 0;
    }
    var isFinalExamIcon = '<i class="fa-solid fa-xmark text-danger"></i>';
    // if (isFinalExamPercentage == 1) {
    //     isFinalExamIcon = '<i class="fa-solid fa-check text-success"></i>';
    // }

    var totalPercentage =
        calculateTotalPercentage(".optionRow") + parseInt(percent);

    if (title != "") {
        if (parseInt(totalPercentage) <= 100) {
            $("#optionTbody").append(
                '<tr class="optionRow" data-title="' +
                    title +
                    '" data-percent="' +
                    percent +
                    '" data-is-final="' +
                    0 +
                    '"><td style="padding-left: 10px;">' +
                    title +
                    '</td><td style="padding-left: 10px;">' +
                    percent +
                    '</td><td style="text-align: right;">' +
                    isFinalExamIcon +
                    '</td><td style="padding-right: 10px; text-align: right;><a href="#" class="btnRemoveOption"><i class="fas fa-trash"></i></a></td></tr>'
            );
            $("#optionTitle").val("");
            $("#optionPercentage").val("");
            $("#totalPercentage").html(totalPercentage + "%");
        } else {
            Swal.fire("Total percentage can not exceed 100% !");
        }
    } else {
        Swal.fire("Title is required!");
    }
});

$(document).on("click", ".btnRemoveOption", function () {
    if (confirm("Are you sure to delete ?")) {
        $(this).closest(".optionRow").remove();
        $("#totalPercentage").html(calculateTotalPercentage() + "%");
    }
});

$(document).on("click", "#btnSubmitTemplate", function () {
    $("#subjectList").trigger("change.select2");
    $("#gradeList").trigger("change.select2");

    var optionData = "";
    $(".optionRow").each(function () {
        optionData +=
            $(this).attr("data-title") +
            "/*/" +
            $(this).attr("data-percent") +
            "/*/" +
            $(this).attr("data-is-final") +
            "|*|";
    });
    $("#optionData").val(optionData);

    var validated = validateForm("#formNewTemplate");
    if (validated !== "fail") {
        showLoader();
        $.ajax({
            type: "POST",
            url: "/app/administration/summative-score-template/create",
            data: validated,
            contentType: false,
            processData: false,
            cache: false,
            enctype: "multipart/form-data",
            success: function (msg) {
                hideLoader();
                location.reload();
            },
            error: function (xhr, status, error) {
                hideLoader();
                parseException(xhr);
            },
        });
    }
});

$(document).on("click", ".btnEditTemplate", function () {
    clickedTemplateId = $(this).attr("data-id");
    $("#modalEditTemplate").modal("show");
    $("#templateEditName").val($(this).attr("data-title"));
});

$(document).on("click", "#btnUpdateTemplate", function () {
    var templateName = $.trim($("#templateEditName").val());
    if (templateName != "") {
        showLoader();
        $.ajax({
            type: "POST",
            url: "/app/administration/summative-score-template/update",
            data: { templateId: clickedTemplateId, templateName: templateName },
            success: function (msg) {
                location.reload();
            },
            error: function (xhr, status, error) {
                hideLoader();
                parseException(xhr);
            },
        });
    } else {
        Swal.fire("Template name is required!");
    }
});

$(document).on("click", ".btnDeleteTemplateGrade", function () {
    if (confirm("Are you sure ?")) {
        showLoader();
        $.ajax({
            type: "POST",
            url: "/app/administration/summative-score-template/delete-grade",
            data: {
                templateId: clickedTemplateId,
                gradeOptionId: $(this).attr("data-id"),
            },
            success: function (msg) {
                hideLoader();
                getTemplateDetails(clickedTemplateId);
            },
            error: function (xhr, status, error) {
                hideLoader();
                parseException(xhr);
            },
        });
    }
});

$(document).on("click", "#btnAddTempplateGrade", function () {
    showLoader();
    $.ajax({
        url: "/app/administration/summative-score-template/get-subject-grade",
        type: "post",
        data: { subjectId: $(this).attr("data-subject") },
        success: function (data) {
            hideLoader();
            $("#gradeListEdit").html(data);
            $("#gradeListEdit").trigger("change.select2");
            $("#modalAddGrade").modal("show");
            $("#grade-alert-banner").html("");
        },
        error: function (xhr, textStatus, error) {
            hideLoader();
            parseException(xhr);
        },
    });
});

$(document).on("click", "#btnSubmitGrade", function () {
    showLoader();
    $.ajax({
        url: "/app/administration/summative-score-template/add-grade",
        type: "post",
        data: {
            templateId: clickedTemplateId,
            grades: $("#gradeListEdit").val(),
        },
        success: function (data) {
            hideLoader();
            hideModal("#modalAddGrade");
            getTemplateDetails(clickedTemplateId);
        },
        error: function (xhr, textStatus, error) {
            hideLoader();
            parseException(xhr);
        },
    });
});

$(document).on("click", "#btnAddTempplateOption", function () {
    $("#modalAddOption").modal("show");
    $("#templateOptionTitle").val("");
});

$(document).on("click", "#btnSubmitOption", function () {
    var title = $.trim($("#templateOptionTitle").val());
    var percent = $("#templateOptionPercentage").val();
    // var isFinalExamPercentage = $("#templateOptionIsFinalExamPercentage").val();
    if (percent == "") {
        percent = 0;
    }

    var totalPercentage =
        calculateTotalPercentage(".optionRowEdit") + parseInt(percent);

    if (title != "") {
        if (totalPercentage <= 100) {
            showLoader();
            $.ajax({
                url: "/app/administration/summative-score-template/add-option",
                type: "post",
                data: {
                    templateId: clickedTemplateId,
                    title: title,
                    percent: percent,
                    isFinalExamPercentage: 0,
                },
                success: function (data) {
                    hideLoader();
                    hideModal("#modalAddOption");
                    getTemplateDetails(clickedTemplateId);
                },
                error: function (xhr, textStatus, error) {
                    hideLoader();
                    parseException(xhr);
                },
            });
        } else {
            Swal.fire("Total percentage can not exceed 100% !");
        }
    } else {
        Swal.fire("Title is required!");
    }
});

$(document).on("click", ".btnDeleteTemplateOption", function () {
    Swal.fire({
        title: "Are you sure?",
        text: "All assessments related to this option will be DELETED also!",
        icon: "warning",
        showCancelButton: true,
        confirmButtonColor: "#3085d6",
        cancelButtonColor: "#d33",
        confirmButtonText: "Yes, delete it!",
    }).then((result) => {
        showLoader();
        $.ajax({
            url: "/app/administration/summative-score-template/delete-option",
            type: "post",
            data: {
                templateId: clickedTemplateId,
                optionId: $(this).attr("data-id"),
            },
            success: function (data) {
                hideLoader();
                getTemplateDetails(clickedTemplateId);
            },
            error: function (xhr, textStatus, error) {
                hideLoader();
                parseException(xhr);
            },
        });
    });
});

$(document).on("click", ".btnDeleteTemplate", function () {
    Swal.fire({
        title: "Are you sure?",
        text: "All assessments related to this option will be DELETED also!",
        icon: "warning",
        showCancelButton: true,
        confirmButtonColor: "#3085d6",
        cancelButtonColor: "#d33",
        confirmButtonText: "Yes, delete it!",
    }).then((result) => {
        showLoader();
        $.ajax({
            url: "/app/administration/summative-score-template/delete-template",
            type: "post",
            data: {
                templateId: $(this).attr("data-id"),
            },
            success: function (data) {
                location.reload();
            },
            error: function (xhr, textStatus, error) {
                hideLoader();
                parseException(xhr);
            },
        });
    });
});

