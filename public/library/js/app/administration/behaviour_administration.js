$.ajaxSetup({
  headers: { "X-CSRF-Token": $("meta[name=_token]").attr("content") },
});
$(document).ready(function () {
  $("#menuAdministration").addClass("here");
  $("#menuAdministration").addClass("show");
  $("#menuBehaviourAdministration").addClass("active");
  makeDataTable("tableBpsItemList");
  makeDataTable("tableDetentionDutyList");
    //   $('#modalAddDetentionDutyList').on('shown.bs.modal', function () {
    //     $('#dutyUser').select2({
    //         dropdownParent: $('#modalAddDetentionDutyList')
    //     });
    // });
});
$(document).on("click", "#tabBPS", function () {
  $("#btnAddBpsItem").show();
  $("#btnViewBpsArchive").show();
  $("#btnAddDetentionDuty").hide();
});
$(document).on("click", "#tabDetention", function () {
  $("#btnAddBpsItem").hide();
  $("#btnViewBpsArchive").hide();
  $("#btnAddDetentionDuty").show();
});

$(document).on("click", "#btnAddBpsItem", function (e) {
  $("#modalAddBpsItem").modal("show");
});
$(document).on("click", "#btnAddDetentionDuty", function (e) {
  $("#modalAddDetentionDutyList").modal("show");
});
$(document).on("click", "#btnViewBpsArchive", function (e) {
  $("#modalBpsArchiveItem").modal("show");
  $("#bpsArchiveItemListArea").html(
    '<p style="text-align: center;"><img src="/img/loader.gif" style="width: 30px;" /></p>'
);
  $('#bpsArchiveItemListArea').load('/app/administration/behaviour-administration/show-archive-bps-item',function () {
    makeDataTable('tableArchiveItemList')
  });
});
$(document).on("click", ".btnEditBpsItem", function (e) {
  $("#modalBpsItemEdit").modal("show");
  $("#bpsItemEditArea").html(
    '<p style="text-align: center;"><img src="/img/loader.gif" style="width: 30px;" /></p>'
);
  $('#bpsItemEditArea').load('/app/administration/behaviour-administration/show-edit-bps-item?itemId='
    +$(this).attr('data-id'));
});
$(document).on("click", ".btnEditDutyTeacher", function (e) {
  $("#modalDetentionDutyEdit").modal("show");
  $("#detentionDutyEditArea").html(
    '<p style="text-align: center;"><img src="/img/loader.gif" style="width: 30px;" /></p>'
);
  $('#detentionDutyEditArea').load('/app/administration/behaviour-administration/show-edit-detention-duty?dutyId='
    +$(this).attr('data-id'),function () {
           $("#detentionDutyEditArea select").select2();
    });
});

$(document).on("click", "#btnSubmitBpsItem", function () {
  showLoader();
  var type = $("#bpsType").val();
  var title = $("#bpsTitle").val();
  var point = $("#bpsPoint").val();
  if (!type || !title || !point) {
    hideLoader();
    Swal.fire("Please Fill All Field");
  } else {
    $.ajax({
        url: "/app/administration/behaviour-administration/store-bps-item",
        type: "post",
        data: {
            item_type: type,
            item_title: title,
            item_point: point,
        },
        success: function (data) {
            hideLoader();
            hideModal("#modalAddBpsItem");
            window.location.reload();
        },
        error: function (xhr, textStatus, error) {
            hideLoader();
            parseException(xhr);
        },
    });
  }
});
$(document).on('click','.btnArchiveBpsItem',function(){
        Swal.fire({
        title: "Are you sure?",
        text: "You Can Restore This in Archive tab!",
        icon: "info",
        showCancelButton: true,
        confirmButtonColor: "#3085d6",
        cancelButtonColor: "#d33",
        confirmButtonText: "Yes, Archive it!",
    }).then((result) => {
        if (result.isConfirmed) {
            showLoader();
            $.ajax({
                url: "/app/administration/behaviour-administration/archive-bps-item",
                type: "post",
                data: { 
                    bpsItemId: $(this).attr("data-id") ,
                    status : 0
                },
                success: function (data) {
                    hideLoader();
                    window.location.reload();
                },
                error: function (xhr, textStatus, error) {
                    hideLoader();
                    parseException(xhr);
                },
            });
        }
    });
});
$(document).on('click','.btnRestoreBpsItem',function(){
        Swal.fire({
        title: "Are you sure?",
        text: "Item will be shown at the BPS Option!",
        icon: "info",
        showCancelButton: true,
        confirmButtonColor: "#3085d6",
        cancelButtonColor: "#d33",
        confirmButtonText: "Yes, Restore it!",
    }).then((result) => {
        if (result.isConfirmed) {
            showLoader();
            $.ajax({
                url: "/app/administration/behaviour-administration/archive-bps-item",
                type: "post",
                data: { 
                    bpsItemId: $(this).attr("data-id") ,
                    status : 1
                },
                success: function (data) {
                    hideLoader();
                    window.location.reload();
                },
                error: function (xhr, textStatus, error) {
                    hideLoader();
                    parseException(xhr);
                },
            });
        }
    });
});
$(document).on("click", "#btnEditBpsItem", function () {
    var itemTitle = $("#itemTitle").val();
    var itemId = $("#itemId").val();
    if (itemTitle !== "" && itemId !== "") {
        showLoader();
        $.ajax({
            url: "/app/administration/behaviour-administration/update-bps-item",
            type: "post",
            data: {
                itemTitle: itemTitle,
                itemId: itemId,
            },
            success: function (data) {
                hideLoader();
                window.location.reload();
            },
            error: function (xhr, textStatus, error) {
                hideLoader();
                parseException(xhr);
            },
        });
    } else {
        Swal.fire("All Fields are required!");
    }
});
$(document).on("click", "#btnSubmitDetentionDutyList", function () {
    var dutyUserId = $("#dutyUserId").val();
    var detentionTypeId = $("#detentionTypeId").val();
    if (dutyUserId !== "" && detentionTypeId !== "") {
        showLoader();
        $.ajax({
            url: "/app/administration/behaviour-administration/store-detention-duty",
            type: "post",
            data: {
                dutyUserId: dutyUserId,
                detentionTypeId: detentionTypeId,
            },
            success: function (data) {
                hideLoader();
                window.location.reload();
            },
            error: function (xhr, textStatus, error) {
                hideLoader();
                parseException(xhr);
            },
        });
    } else {
        Swal.fire("All Fields are required!");
    }
});
$(document).on("click", "#btnEditDetentionDuty", function () {
    var editDutyUserId = $("#editDutyUserId").val();
    var editDetentionTypeId = $("#editDetentionTypeId").val();
    var dutyId = $("#dutyId").val();
    if (editDutyUserId !== "" && editDetentionTypeId !== "") {
        showLoader();
        $.ajax({
            url: "/app/administration/behaviour-administration/update-detention-duty",
            type: "post",
            data: {
                editDutyUserId: editDutyUserId,
                editDetentionTypeId: editDetentionTypeId,
                dutyId: dutyId,
            },
            success: function (data) {
                hideLoader();
                window.location.reload();
            },
            error: function (xhr, textStatus, error) {
                hideLoader();
                parseException(xhr);
            },
        });
    } else {
        Swal.fire("All Fields are required!");
    }
});
$(document).on('click','.btnArchiveDutyTeacher',function(){
        Swal.fire({
        title: "Are you sure?",
        text: "You won't be able to revert this!",
        icon: "info",
        showCancelButton: true,
        confirmButtonColor: "#3085d6",
        cancelButtonColor: "#d33",
        confirmButtonText: "Yes, Delete it!",
    }).then((result) => {
        if (result.isConfirmed) {
            showLoader();
            $.ajax({
                url: "/app/administration/behaviour-administration/archive-detention-duty",
                type: "post",
                data: { 
                    dutyId: $(this).attr("data-id") ,
                    status : 0
                },
                success: function (data) {
                    hideLoader();
                    window.location.reload();
                },
                error: function (xhr, textStatus, error) {
                    hideLoader();
                    parseException(xhr);
                },
            });
        }
    });
});
