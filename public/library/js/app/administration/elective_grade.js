$.ajaxSetup({
    headers: { "X-CSRF-Token": $("meta[name=_token]").attr("content") },
});

$(document).ready(function () {
    $("#menuAdministration").addClass("here");
    $("#menuAdministration").addClass("show");
    $("#menuElectiveGrades").addClass("active");

    makeDataTable("gradeTable");

    $(document.body).on("change", "#classroomList", function () {
        $("#students > option").attr("selected", false);
        $("#students").val(null).trigger("change");

        if ($("#classroomList").val() > 0) {
            $("#classroom_id").val($("#classroomList").val());
            showLoader();
            $.ajax({
                type: "POST",
                url: "/app/administration/elective-grades/get-classroom-students",
                data: { classroomId: $("#classroomList").val() },
                success: function (data) {
                    hideLoader();
                    if (data != 0) {
                        var arr = data.trim().split(",");
                        console.log(arr);
                        
                        $("#students").val(arr).trigger("change");
                    }
                },
                error: function (xhr, status, error) {
                    hideLoader();
                    parseException(xhr);
                },
            });
        }
    });

    $(document).on("click", "#btnAddGradeRow", function () {
        var cloneRow = $("#gradeNameBodyArea tr:first").clone();
        $("#gradeNameBodyArea").append(cloneRow);
    });
});

var clickedGradeId = null;

$(document).on("click", ".gradeRow", function () {
    $(".gradeRow").removeClass("activeRow");
    $(this).addClass("activeRow");
    clickedGradeId = $(this).attr("data-id");
    getGradeStudents(clickedGradeId);
});

function getGradeStudents(gradeId) {
    // showLoader();
    $("#gradeDetailArea").html(
        '<tr><td colspan="3" style="text-align: center;"><img src="/img/loader.gif" style="width: 30px;" /></td></tr>'
    );
    $.ajax({
        url: "/app/administration/elective-grades/get-students",
        type: "post",
        data: { gradeId: gradeId },
        success: function (data) {
            hideLoader();
            $("#gradeDetailArea").html(data);
            makeDataTable("gradeDetailTable");
            $("#addGradeStudent").show("fast");
        },
        error: function (xhr, textStatus, error) {
            hideLoader();
            parseException(xhr);
        },
    });
}

$(document).on("click", "#btnSaveGrade", function () {
    var gradeData = "";
    $(".gradeDataRow").each(function () {
        var gradeName = $.trim($(this).find(".gradeDataName").val());
        var subject = $(this).find(".gradeDataSubject").val();

        if (gradeName != "" && subject > 0) {
            gradeData += gradeName + "/*/" + subject + "|*|";
        }
    });
    $("#grade_data").val(gradeData);
    var validated = validateForm("#formNewGrade");
    if (validated !== "fail") {
        showLoader();
        $.ajax({
            type: "POST",
            url: "/app/administration/elective-grades/create",
            data: validated,
            contentType: false,
            processData: false,
            cache: false,
            enctype: "multipart/form-data",
            success: function (msg) {
                location.reload();
            },
            error: function (xhr, status, error) {
                hideLoader();
                parseException(xhr);
            },
        });
    }
});

$(document).on("click", ".btnDeleteStudent", function () {
    if (confirm("Are you sure to delete ?")) {
        $.ajax({
            type: "POST",
            url: "/app/administration/elective-grades/delete-student",
            data: {
                gradeId: clickedGradeId,
                studentId: $(this).attr("data-id"),
            },
            success: function (msg) {
                getGradeStudents(clickedGradeId);
            },
            error: function (xhr, status, error) {
                hideLoader();
                parseException(xhr);
            },
        });
    }
});

$(document).on("click", "#btnSaveStudents", function () {
    var students = $("#studentList").val();
    if (students.length > 0) {
        showLoader();
        $.ajax({
            type: "POST",
            url: "/app/administration/elective-grades/add-student",
            data: { gradeId: clickedGradeId, students: students },
            success: function (msg) {
                hideLoader();
                $("#modalAddStudents").modal("hide");
                $(".modal-backdrop").remove();
                getGradeStudents(clickedGradeId);
            },
            error: function (xhr, status, error) {
                hideLoader();
                parseException(xhr);
            },
        });
    } else {
        alert("You need to chose at least 1 student!");
    }
});

$(document).on("click", ".btnDeleteGrade", function () {
    var gradeName = $(this).attr("data-name");
    Swal.fire({
        title: "Are you sure to delete <b><u>" + gradeName + "</u></b> grade ?",
        text: "You won't be able to revert this!",
        icon: "warning",
        showCancelButton: true,
        confirmButtonColor: "#3085d6",
        cancelButtonColor: "#d33",
        confirmButtonText: "Yes, delete it!",
    }).then((result) => {
        if (result.isConfirmed) {
            showLoader();
            $.ajax({
                type: "POST",
                url: "/app/administration/elective-grades/delete-grade",
                data: { gradeId: $(this).attr("data-id") },
                success: function (msg) {
                    hideLoader();
                    if (msg == true) {
                        Swal.fire(
                            "Please remove all related data, such as the grade in the Assessment Template, before deleting the Elective Grade."
                        );
                    } else {
                        hideLoader();
                        location.reload();
                    }
                },
                error: function (xhr, status, error) {
                    hideLoader();
                    parseException(xhr);
                },
            });
        }
    });
});

$(document).on("click", ".btnEditGrade", function () {
    clickedGradeId = $(this).attr("data-id");
    $("#modalEditGrade").modal("show");
    $("#gradeEditName").val($(this).attr("data-name"));
    $("#gradeEditSubject").val($(this).attr("data-subject"));
});

$(document).on("click", "#btnSaveUpdateGrade", function () {
    showLoader();
    $.ajax({
        type: "POST",
        url: "/app/administration/elective-grades/update-grade",
        data: {
            gradeId: clickedGradeId,
            gradeName: $.trim($("#gradeEditName").val()),
            gradeSubject: $("#gradeEditSubject").val(),
        },
        success: function (msg) {
            location.reload();
        },
        error: function (xhr, status, error) {
            hideLoader();
            parseException(xhr);
        },
    });
});
