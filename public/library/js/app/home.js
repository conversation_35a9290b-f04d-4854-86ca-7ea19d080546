$(document).ready(function () {
  var totalAnnouncements = $("#announcementCount").val();
  if (totalAnnouncements > 0) {
    $("#modalAnnouncements").modal("show");
  }
  stepCounter = 1;
  $("#menuDashboard").addClass("active");
  makeDateRange();
  var userBranch = $("#userBranch").attr("data-id");
  var userWeekInfo = $("#userBranch").attr("data-week");
  if (userBranch != 4) {
    if (userWeekInfo) {
      getTimetable();
    }
  }
  setStep();
  showAlert("Detention Duty", "info");
  $(document).on("change", "#leave_type", function () {
    var type = $("#leave_type").val();
    resetRequestDuration();
    if (type == "long_time" || type == "special_leave") {
      $("#divDateRange").show("fast");
    } else if (
      type == "sick_leave" ||
      type == "short_time" ||
      type == "early_leave"
    ) {
      $("#divDate").show("fast");
      $("#divTimeFrom").show("fast");
      $("#divTimeTo").show("fast");
    }
  });

  $(document).on("change", "#any_lesson", function () {
    if ($("#any_lesson").val() == 1) {
      $("#divLessonMaterials").css("opacity", "1");
      $("#divLessonMaterials").css("pointer-events", "auto");
    } else {
      $("#divLessonMaterials").css("opacity", "0.5");
      $("#divLessonMaterials").css("pointer-events", "none");
    }
  });

  $(document).on("change", "#any_club", function () {
    if ($("#any_club").val() == 1) {
      $("#divClubInformation").css("opacity", "1");
      $("#divClubInformation").css("pointer-events", "auto");
    } else {
      $("#divClubInformation").css("opacity", "0.5");
      $("#divClubInformation").css("pointer-events", "none");
    }
  });

  $(document).on("change", "#duty_responsibility", function () {
    if ($("#duty_responsibility").val() == 1) {
      $("#divSubstitude").css("opacity", "1");
      $("#divSubstitude").css("pointer-events", "auto");
    } else {
      $("#divSubstitude").css("opacity", "0.5");
      $("#divSubstitude").css("pointer-events", "none");
    }
  });

  $(document).on("change", "#any_meeting", function () {
    if ($("#any_meeting").val() == 1) {
      $("#divMeetingPermission").css("opacity", "1");
      $("#divMeetingPermission").css("pointer-events", "auto");
    } else {
      $("#divMeetingPermission").css("opacity", "0.5");
      $("#divMeetingPermission").css("pointer-events", "none");
    }
  });

  $(document).on("change", "#obligation", function () {
    if ($("#obligation").val() == 1) {
      $("#divObligationDetail").css("opacity", "1");
      $("#divObligationDetail").css("pointer-events", "auto");
    } else {
      $("#divObligationDetail").css("opacity", "0.5");
      $("#divObligationDetail").css("pointer-events", "none");
    }
  });
  $(document).on("click", "#btnDetentionList", function () {
    // $("#detentionListModal").toggle('hide');
    $("#detentionListModal").modal("show");
  });
  makeDataTable("detentionListTable");
  var userCurrentBranch = $("#userCurrentBranch").attr("data-id");
  const today = new Date().toISOString().split("T")[0];
  const lastShown = localStorage.getItem(
    "birthday_last_shown_date" + userCurrentBranch
  );
  if (lastShown !== today) {
    showBalloonsWithSparkles();
    showBirthdayPopup();
    localStorage.setItem("birthday_last_shown_date" + userCurrentBranch, today);
  }
});
function showAlert(message, type = "success") {
  const $alert = $("#customAlert");

  // Set alert type and message
  $alert
    .removeClass("alert-success alert-danger alert-warning alert-info")
    .addClass("alert-" + type);
  $alert.find(".alert-message").text(message);

  // Show alert with fade-in effect
  $alert.fadeIn(300).css({ top: "10px", right: "20px" });

  // Hide after 3 seconds
  setTimeout(() => {
    $alert.fadeOut(300);
  }, 3000);
}

function resetRequestDuration() {
  $("#divDateRange").hide();
  $("#divDate").hide();
  $("#divTimeFrom").hide();
  $("#divTimeTo").hide();
}

// Get Current week Start date and End Date
function getCurrentWeekDates() {
  const today = new Date();
  const dayOfWeek = today.getDay();
  const monday = new Date(today);
  monday.setDate(today.getDate() - (dayOfWeek === 0 ? 6 : dayOfWeek - 1)); // Calculate Monday
  const sunday = new Date(monday);
  sunday.setDate(monday.getDate() + 6); // Calculate Sunday

  return { startDate: monday, endDate: sunday };
}

//  get all dates between the start and end dates of the current week
function getAllDatesOfWeek(startDate, endDate) {
  const allDates = {};

  for (let i = 0; i < 6; i++) {
    const currentDate = new Date(startDate);
    currentDate.setDate(startDate.getDate() + i);
    allDates[i + 1] = new Date(currentDate).toISOString().split("T")[0];
  }

  return allDates;
}

function getTimetable() {
  $.ajax({
    url: "/app/home/<USER>",
    type: "post",
    success: async function (data) {
      hideLoader();
      var all_rows = data.split("|**|");
      var ph_array = all_rows[1].split(",");
      // console.log(ph_array);

      const { startDate, endDate } = getCurrentWeekDates();
      const allDatesOfWeek = getAllDatesOfWeek(startDate, endDate);

      var rows = all_rows[0].split("*-*");
      const newDate = new Date();
      todayDate = newDate.toJSON().slice(0, 10);
      for (var i = 0; i < rows.length; i++) {
        if (rows[i] != "") {
          let date = null;
          let isPublicHoliday = false;
          var rowData = rows[i].split("/*/");

          // finding public_holiday
          for (const key in allDatesOfWeek) {
            if (rowData[1] == key) {
              date = allDatesOfWeek[key];
              break;
            }
          }
          for (const key in ph_array) {
            if (ph_array[key] == date) {
              isPublicHoliday = true;
              break;
            }
          }

          // return date;
          var timetableId = rowData[0];
          var cellId = rowData[1] + "_" + rowData[2];
          var gradeName = rowData[3];
          var gradeId = rowData[4];
          var subjectName = rowData[5];
          var subjectId = rowData[6];
          var isAttendance = rowData[7];
          var btnClass = isAttendance == 1 ? " btn-secondary " : " btn-danger ";
          //  make disable button
          var isDisabled = " ";
          if (todayDate < date || isPublicHoliday === true) {
            isDisabled = "disabled";
          } else {
            isDisabled = "";
          }
          // console.log(isDisabled);
          var tempBtnId = "dashboard-timetable-" + i;
          var htmlButton =
            '<a id="' +
            tempBtnId +
            '" class="btn btn-sm w-100 ' +
            btnClass +
            isDisabled +
            ' btn-block btnGradeAttendance" data-timetable-id="' +
            timetableId +
            '">' +
            gradeName +
            "</a>";
          // console.log(htmlButton);
          $("#" + cellId).html(htmlButton);
          if (isPublicHoliday === true) {
            if (
              !$("#" + rowData[1])
                .html()
                .includes("( holiday )")
            ) {
              $("#" + rowData[1]).append(
                " <br> <span class='text-secondary'>( holiday )</span>"
              );
            }
          }
        }
      }
      $("#timeTableLoader").hide();
    },
    error: function (xhr, textStatus, error) {
      hideLoader();
      parseException(xhr);
    },
  });
}

$(document).on("click", "#btnRequestLeave", function () {
  showLoader();
  $("#modalRequestLeave").modal("show");
  $("#modalRequestLeaveArea").load(
    "/app/home/<USER>/create",
    function () {
      $("#formLeaveRequest select").select2();
      hideLoader();
      setStep();
      makeDate();
      makeDateRangeBySelector("#requestDateRange");
    }
  );
});

$(document).on("click", "#btnLeaveRequestList", function () {
  showLoader();
  $("#modalLeaveRequestList").modal("show");
  $("#modalLeaveRequestListArea").load(
    "/app/home/<USER>/list",
    function () {
      hideLoader();
      makeDataTable("tableRequestList");
    }
  );
});

$(document).on("click", "#btnRequestLeave", function () {
  showLoader();
  $("#modalRequestLeave").modal("show");
  $("#modalRequestLeaveArea").load(
    "/app/home/<USER>/create",
    function () {
      $("#formLeaveRequest select").select2();
      hideLoader();
      setStep();
      makeDate();
      makeDateRangeBySelector("#requestDateRange");
    }
  );
});

//-- Leave request:START

$(document).on("click", ".stepper-item", function () {
  stepCounter = $(this).attr("data-step");
  setStep();
});

$(document).on("click", ".btnStep", function () {
  if ($(this).attr("data-type") == "next") {
    stepCounter++;
  } else if ($(this).attr("data-type") == "back") {
    stepCounter--;
  }
  setStep();
});

function setStep() {
  if (stepCounter >= 6) {
    stepCounter = 6;
    $("#btnRequestNext").hide();
  } else {
    $("#btnRequestNext").show();
  }

  if (stepCounter <= 1) {
    stepCounter = 1;
    $("#btnRequestBack").hide();
  } else {
    $("#btnRequestBack").show();
  }

  $(".stepContent").removeClass("current");
  $("#step_content_" + stepCounter).addClass("current");

  $(".stepper-item").removeClass("current");
  $("#step_menu_" + stepCounter).addClass("current");
}

$(document).on("click", "#btnStoreRequest", function () {
  var validated = validateForm("#formLeaveRequest");
  if (validated !== "fail") {
    showLoader();
    $.ajax({
      type: "POST",
      url: "/app/home/<USER>/store",
      data: validated,
      contentType: false,
      processData: false,
      cache: false,
      enctype: "multipart/form-data",
      success: function (msg) {
        hideLoader();
        Swal.fire({
          position: "top-end",
          icon: "success",
          title: "Your leave request has been submitted",
          showConfirmButton: false,
          timer: 1500,
        });
        $("#modalRequestLeave").modal("hide");
        $("#formLeaveRequest")[0].reset();
        stepCounter = 1;
      },
      error: function (xhr, status, error) {
        hideLoader();
        parseException(xhr);
      },
    });
  }
});
//-- Leave requet:FINISH

var clickedTimetableBtnId = "";
$(document).on("click", ".btnGradeAttendance", function () {
  $("#modalAttendanceArea").html(
    '<p class="text-center"><img src="/img/loader.gif" style="width:30px;" /></p>'
  );
  $("#modalAttendance").modal("show");
  $("#modalAttendanceArea").load(
    "/app/home/<USER>/show?timetableId=" +
      $(this).attr("data-timetable-id")
  );
  clickedTimetableBtnId = $(this).attr("id");
});

$(document).on("click", ".attendanceBtn", function () {
  var studentId = $(this).attr("data-student-id");
  $(".attendanceBtn_" + studentId).removeClass("btn-primary");
  $(".attendanceBtn_" + studentId).removeClass("btn-secondary");
  $(".attendanceBtn_" + studentId).addClass("btn-secondary");
  $(this).removeClass("btn-secondary");
  $(this).addClass("btn-primary");
});

$(document).on("click", "#btnSubmitAttendance", function () {
  var timetableId = $("#attendanceTimetableId").val();
  var attendanceTimetableDate = $("#attendanceTimetableDate").val();
  var attendanceData = "";
  var topic = "";

  $(".attendanceRow").each(function () {
    var studentId = $(this).attr("data-id");
    $(".attendanceBtn_" + studentId).each(function () {
      if ($(this).hasClass("btn-primary")) {
        var type = $(this).attr("data-type");
        var attendanceNote = $.trim($("#attendanceNote_" + studentId).val());
        attendanceData += studentId + "|" + type + "|" + attendanceNote + "/";
      }
    });
  });

  showLoader();
  $.ajax({
    url: "/app/home/<USER>/store",
    type: "post",
    data: {
      timetable: timetableId,
      attendance: attendanceData,
      topic: topic,
      date: attendanceTimetableDate,
    },
    success: function (data) {
      hideLoader();
      $("#modalAttendance").modal("hide");
      $("#modalAttendanceArea").html(
        '<p class="text-center"><img src="/img/loader.gif" style="width:30px;" /></p>'
      );
      Swal.fire({
        position: "top-end",
        icon: "success",
        title: "Attendance has been saved",
        showConfirmButton: false,
        timer: 1500,
      });
      $("#" + clickedTimetableBtnId).removeClass("btn-danger");
      $("#" + clickedTimetableBtnId).addClass("btn-secondary");
    },
    error: function (xhr, textStatus, error) {
      hideLoader();
      Swal.fire("Error occured", xhr.responseText);
    },
  });
});

$(document).on("click", "#btnReadAllAnnouncements", function () {
  Swal.fire({
    title: "Do you want to mark as read all announcements ?",
    showDenyButton: false,
    showCancelButton: true,
    confirmButtonText: "Yes",
  }).then((result) => {
    if (result.isConfirmed) {
      showLoader();
      $.ajax({
        url: "/app/home/<USER>/read-all",
        type: "post",
        success: function (data) {
          location.reload();
        },
        error: function (xhr, textStatus, error) {
          hideLoader();
          Swal.fire("Error occured", xhr.responseText);
        },
      });
    }
  });
});
$(document).on("click", ".btnSubmitDetentionServe", function () {
  Swal.fire({
    title: "Are you sure to Served ?",
    text: "You won't be able to revert this!",
    icon: "warning",
    showCancelButton: true,
    confirmButtonColor: "#3085d6",
    cancelButtonColor: "#d33",
    confirmButtonText: "Yes, Serve it!",
  }).then((result) => {
    if (result.isConfirmed) {
      $.ajax({
        url: "/app/discipline/detention-report/serve-record",
        type: "post",
        data: { recordId: $(this).attr("data-id") },
        success: function (data) {
          Swal.fire({
            position: "top-end",
            icon: "success",
            title: "Your work has been saved",
            showConfirmButton: false,
            timer: 1500,
          });
          location.reload();
        },
        error: function (xhr, textStatus, error) {
          hideLoader();
          parseException(xhr);
        },
      });
    }
  });
});
function showBalloons() {
  const balloonWrapper = $("#balloonWrapper");
  balloonWrapper.empty().show();

  const balloonColors = [
    "#FF69B4",
    "#FFD700",
    "#00BFFF",
    "#32CD32",
    "#FFA500",
    "#FF4500",
  ];

  for (let i = 0; i < 20; i++) {
    const balloon = $('<div class="balloon"></div>');
    const left = Math.random() * 100;
    const color =
      balloonColors[Math.floor(Math.random() * balloonColors.length)];
    const delay = Math.random() * 1;

    balloon.css({
      left: `${left}%`,
      background: color,
      animationDelay: `${delay}s`,
    });

    balloonWrapper.append(balloon);
  }

  setTimeout(() => {
    balloonWrapper.fadeOut(500);
  }, 5000);
}
function showFullPageSparkles() {
  const container = $("#fullPageSparkles");
  container.empty().show();

  const sparkleColors = ["#FFD700", "#FFFACD", "#FFFAF0", "#FFFFFF", "#E6E6FA"];

  for (let i = 0; i < 120; i++) {
    const sparkle = $('<div class="full-sparkle"></div>');

    const startX = Math.random() * window.innerWidth;
    const startY = Math.random() * window.innerHeight;

    const dx = (Math.random() - 0.5) * 300 + "px";
    const dy = (Math.random() - 0.5) * 300 + "px";

    const delay = Math.random() * 0.5; // up to 0.5s delay

    sparkle.css({
      left: `${startX}px`,
      top: `${startY}px`,
      "--dx": dx,
      "--dy": dy,
      backgroundColor:
        sparkleColors[Math.floor(Math.random() * sparkleColors.length)],
      animationDelay: `${delay}s`,
    });

    container.append(sparkle);
  }

  setTimeout(() => {
    container.fadeOut(500);
  }, 5000);
}
function showBirthdayPopup() {
  $("#birthdayPopup").fadeIn(500);

  setTimeout(() => {
    $("#birthdayPopup").fadeOut(500);
  }, 5000);
}
function showBalloonsWithSparkles() {
  showBalloons();
  showFullPageSparkles();
}
