$.ajaxSetup({
    headers: { 'X-CSRF-Token' : $('meta[name=_token]').attr('content') }
});

$(document).ready(function(){
    $('#menuReports').addClass('here');
    $('#menuReports').addClass('show');
    $('#menuReportsKg').addClass('active');


    $(document).on('change', '#classroom', function(){
        showLoader();
        $.ajax({
            type: 'POST',
            url: '/app/reports/kg-report/classroom-students',
            data: { 'classroomId': $('#classroom').val()},
            success: function (data) {
                hideLoader();
                $('#students').html(data);
                $('#students').select2();
            },
            error: function (xhr, status, error) {
              hideLoader();
              parseException(xhr);
          }
        });
    });


  $(document).on('click', '#btnClearSelection', function(){
    $('#students option:selected').prop('selected', false);
    $('#students').select2();
  });

  $(document).on('click', '#btnGenerageReportCard', function() {
    var validated = validateForm('#formKGReportCard');
    if(validated !== 'fail') {
        showLoader();
        $.ajax({
            type: 'POST',
            url: '/app/reports/kg-report/pre-check',
            data: validated,
            contentType: false,
            processData: false,
            cache: false,
            enctype: "multipart/form-data",
            success: function (data) {
                hideLoader();
                let a= document.createElement('a');
                a.target= '_blank';
                a.href= '/app/reports/kg-report/generate?uid=' + data;
                a.click();
            },
            error: function (xhr, status, error) {
                hideLoader();
                parseException(xhr);
            }
        });
    }
  });

});