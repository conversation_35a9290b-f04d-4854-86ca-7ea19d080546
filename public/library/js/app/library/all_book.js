$(document).ready(function () {
    $("#menuLibrary").addClass("active");
    makeDataTable("homeIssuesTable");
    loadAllBooks();
});
function loadAllBooks(data) {
    var $isVicePrincipal = $("#kt_app_main").data("role");
    var $isVicePrincipal = $("#kt_app_main").attr("data-role");
    if ($.fn.DataTable.isDataTable("#tableRecordList")) {
        $("#tableRecordList").DataTable().destroy();
    }
    $("#tableRecordList").DataTable({
        language: {
            lengthMenu: "Show _MENU_",
        },
        buttons: [
            {
                extend: "excelHtml5",
                text: '<i class="fas fa-file-excel"></i>',
                className: "btn-sm btn-primary",
            },
            {
                extend: "pdfHtml5",
                text: '<i class="fas fa-file-pdf"></i>',
                className: "btn-sm btn-primary",
            },
        ],
        processing: true,
        serverSide: true,
        ajax: {
            url: "/app/library/book/get-all-books",
            type: "POST", // Use POST method if required
            data: data,
        },
        order: [],
        deferRender: false,
        pageLength: 15,
        lengthMenu: [15, 25, 50, 75, 100, 500],
        columnDefs: [{ orderable: false, targets: 0 }],
        columns: [
            {
                data: "book_check_id",
                name: "book_check_id",
                render: function (row, data, dataIndex) {
                    return (
                        ' <input type="checkbox" class="rowCheckbox" id="rowCheckbox' +
                        data.book_check_id +
                        '" />'
                    );
                },
            },
            { data: "photo", name: "photo" },
            { data: "title", name: "title" },
            { data: "author", name: "author" },
            { data: "subject_heading", name: "subject_heading" },
            { data: "isbn", name: "isbn" },
            { data: "call_number", name: "call_number" },
            { data: "barcode", name: "barcode" },
            { data: "price", name: "price" },
            { data: "total_issues", name: "total_issues" },
            { data: "status", name: "status" },
            {
                data: "book_id",
                name: "book_id",
                render: function (row, data, dataIndex) {
                    return '<a href="#"  class="btnViewBook " title="View Book"><i class="fas fa-eye"></i></a> <a href="#"  class="btnDeleteBook" title="Delete Book"><i class="fas fa-trash"></i></a>';
                },
            },
        ],
        createdRow: function (row, data, dataIndex) {
            $(row).addClass("recordRow");
            $(row).attr("data-id", data.book_id);
            $("td", row).eq(10).addClass("text-end").css("width", "80px");
        },
        dom:
            "<'row'<'col-sm-4 d-flex align-items-center justify-conten-start'l>" +
            "<'col-sm-8 d-flex align-items-center justify-content-end'fB>>" +
            "<'table-responsive'tr><'row'" +
            "<'col-sm-12 col-md-5 d-flex align-items-center justify-content-center justify-content-md-start'i>" +
            "<'col-sm-12 col-md-7 d-flex align-items-center justify-content-center justify-content-md-end'p>>",
    });
}
function loadAllBooksAfterEdit(data) {
    if ($.fn.DataTable.isDataTable("#tableRecordList")) {
        currentPage = $("#tableRecordList").DataTable().page.info().page;
        $("#tableRecordList").DataTable().destroy();
    }
    var table = $("#tableRecordList").DataTable({
        language: {
            lengthMenu: "Show _MENU_",
        },
        buttons: [
            {
                extend: "excelHtml5",
                text: '<i class="fas fa-file-excel"></i>',
                className: "btn-sm btn-primary",
            },
            {
                extend: "pdfHtml5",
                text: '<i class="fas fa-file-pdf"></i>',
                className: "btn-sm btn-primary",
            },
        ],
        processing: true,
        serverSide: true,
        ajax: {
            url: "/app/library/book/get-all-books",
            type: "POST", // Use POST method if required
            data: data,
        },
        order: [],
        deferRender: true,
        pageLength: 15,
        lengthMenu: [15, 25, 50, 75, 100, 500],
        columnDefs: [{ orderable: false, targets: 0 }],
        columns: [
            {
                data: "book_check_id",
                name: "book_check_id",
                render: function (row, data, dataIndex) {
                    return (
                        ' <input type="checkbox" class="rowCheckbox" id="rowCheckbox' +
                        data.book_check_id +
                        '" /></td>'
                    );
                },
            },
            { data: "photo", name: "photo" },
            { data: "title", name: "title" },
            { data: "author", name: "author" },
            { data: "subject_heading", name: "subject_heading" },
            { data: "isbn", name: "isbn" },
            { data: "call_number", name: "call_number" },
            { data: "barcode", name: "barcode" },
            { data: "price", name: "price" },
            { data: "total_issues", name: "total_issues" },
            { data: "status", name: "status" },
            {
                data: "book_id",
                name: "book_id",
                render: function (row, data, dataIndex) {
                    return '<a href="#"  class="btnViewBook " title="View Book"><i class="fas fa-eye"></i></a> <a href="#"  class="btnDeleteBook" title="Delete Book"><i class="fas fa-trash"></i></a>';
                },
            },
        ],
        createdRow: function (row, data, dataIndex) {
            $(row).addClass("recordRow");
            $(row).attr("data-id", data.book_id);
            $("td", row).eq(10).addClass("text-end").css("width", "80px");
        },
        dom:
            "<'row'<'col-sm-4 d-flex align-items-center justify-conten-start'l>" +
            "<'col-sm-8 d-flex align-items-center justify-content-end'fB>>" +
            "<'table-responsive'tr><'row'" +
            "<'col-sm-12 col-md-5 d-flex align-items-center justify-content-center justify-content-md-start'i>" +
            "<'col-sm-12 col-md-7 d-flex align-items-center justify-content-center justify-content-md-end'p>>",
        initComplete: function () {
            // After the table is initialized, return to the saved page
            if (currentPage > 0) {
                table.page(currentPage).draw(false);
            }
        },
    });
}
$(document).on("click", "#btnSearchBooks", function () {
    var author = $("#author").val();
    var title = $("#title").val();
    var keyword = $("#keyword").val();
    var barcode_isbn = $("#barcode_isbn").val();
    data = {
        author: author,
        title: title,
        keyword: keyword,
        barcode_isbn: barcode_isbn,
    };
    loadAllBooks(data);
});
$(document).on("click", ".btnDeleteBook", function () {
    var book = $(this).closest("tr").attr("data-id");
    Swal.fire({
        title: "Are you sure?",
        text: "You won't be able to revert this!",
        icon: "warning",
        showCancelButton: true,
        confirmButtonColor: "#3085d6",
        cancelButtonColor: "#d33",
        confirmButtonText: "Yes, delete it!",
    }).then((result) => {
        if (result.isConfirmed) {
            showLoader();
            $.ajax({
                type: "POST",
                url: "/app/library/book/delete",
                data: { bookId: book },
                success: function (msg) {
                    location.reload();
                },
                error: function (xhr, status, error) {
                    hideLoader();
                    parseException(xhr);
                },
            });
        }
    });
});
$(document).on("click", ".btnViewBook", function () {
    var book = $(this).closest("tr").attr("data-id");
    // alert(book);
    $("#modalDetailEditBook").modal("show");
    $("#detailBookResponseArea").empty();
    $("#detailBookResponseArea").load(
        "/app/library/book/show-detail-book-form?book_id=" + book
    );
});
$(document).on("click", "#btnSaveBookInfo", function () {
    var validated = validateForm("#formBookDetail");
    if (validated !== "fail") {
        showLoader();
        $.ajax({
            type: "POST",
            url: "/app/library/book/store",
            data: validated,
            contentType: false,
            processData: false,
            cache: false,
            enctype: "multipart/form-data",
            success: function (msg) {
                hideLoader();
                Swal.fire({
                    position: "top-end",
                    icon: "success",
                    title: "Your work has been saved",
                    showConfirmButton: false,
                    timer: 1500,
                });
                loadAllBooksAfterEdit();
            },
            error: function (xhr, status, error) {
                hideLoader();
                parseException(xhr);
            },
        });
    }
});
var checkedIds = [];
$(document).on("change", "#selectAllRow", function () {
    $(".rowCheckbox").prop("checked", $(this).prop("checked"));

    if ($(this).prop("checked")) {
        $(".rowCheckbox:checked").each(function () {
            var clickedCheckboxId = $(this).closest("tr").attr("data-id");
            checkedIds.push(clickedCheckboxId);
        });
        $("#btnDeleteBooks").removeClass("d-none");
    } else {
        checkedIds = [];
        $("#btnDeleteBooks").addClass("d-none");
    }
});
$(document).on("change", ".rowCheckbox", function () {
    var clickedCheckboxId = $(this).closest("tr").attr("data-id");
    if ($(this).prop("checked")) {
        checkedIds.push(clickedCheckboxId);
    } else {
        // Checkbox is unchecked, remove its ID from the array
        var index = checkedIds.indexOf(clickedCheckboxId);
        if (index !== -1) {
            checkedIds.splice(index, 1);
        }
    }
    if (checkedIds.length != 0) {
        $("#btnDeleteBooks").removeClass("d-none");
    } else {
        $("#btnDeleteBooks").addClass("d-none");
    }
});

$(document).on("click", "#btnDeleteBooks", function () {
    Swal.fire({
        title: "Are you sure?",
        text: "You won't be able to revert this!",
        icon: "warning",
        showCancelButton: true,
        confirmButtonColor: "#3085d6",
        cancelButtonColor: "#d33",
        confirmButtonText: "Yes, delete it!",
    }).then((result) => {
        if (result.isConfirmed) {
            showLoader();
            $.ajax({
                type: "POST",
                url: "/app/library/book/bulk-delete",
                data: { bookIds: checkedIds },
                success: function (msg) {
                    location.reload();
                },
                error: function (xhr, status, error) {
                    hideLoader();
                    parseException(xhr);
                },
            });
        }
    });
});
$(document).on("change", "#inputBookCover", function () {
    var input = this;
    if (input.files && input.files[0]) {
        var reader = new FileReader();

        reader.onload = function (e) {
            $("#bookCover").attr("src", e.target.result);
        };
        reader.readAsDataURL(input.files[0]);
    }
});
$(document).on("click", "#deleteCover", function (e) {
    var bookId = $(this).attr("data-book-id");
    Swal.fire({
        title: "Are you sure?",
        text: "You won't be able to revert this!",
        icon: "warning",
        showCancelButton: true,
        confirmButtonColor: "#3085d6",
        cancelButtonColor: "#d33",
        confirmButtonText: "Yes, delete it!",
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                type: "POST",
                url: "/app/library/book/delete-cover",
                data: { bookId: bookId },
                success: function (msg) {
                    Swal.fire({
                        position: "top-end",
                        icon: "success",
                        title: "Your work has been saved",
                        showConfirmButton: false,
                        timer: 1500,
                    });
                    $("#detailBookResponseArea").empty();
                    $("#detailBookResponseArea").load(
                        "/app/library/book/show-detail-book-form?book_id=" +
                            bookId
                    );
                    loadAllBooksAfterEdit();
                },
                error: function (xhr, status, error) {
                    hideLoader();
                    parseException(xhr);
                },
            });
        }
    });
});

