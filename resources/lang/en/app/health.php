<?php

return [

    'health_manager'                    => 'Health Manager',
    'health_records'                    => 'Health Records',
    'health'                            => 'Health',
    'students_health_info'              => 'Students Health Information',
    'staff_health_info'                 => 'Staff Health Information',
    'guests_health_info'                => 'Guests Health Information',
    'name'                              => 'Name',
    'id'                                => 'ID',
    'student'                           => 'Student',
    'students'                          => 'Students',
    'staff'                             => 'Staff',
    'guest'                             => 'Guest',
    'guests'                            => 'Guests',
    'reports'                           => 'Reports',
    'date'                              => 'Date',
    'reason'                            => 'Reason',
    'pick_reason'                       => 'Pick Reason',
    'pick_date'                         => 'Pick Date',
    'action'                            => 'Action',
    'grade'                             => 'Grade',
    'position'                          => 'Position',
    'username'                          => 'Username',
    'health_info'                       => 'Health Information',
    'admission_date'                    => 'Admission Date',
    'time'                              => 'Time',
    'time_left'                         => 'Time Left In Clinic',
    'admission_time'                    => 'Admission Time',
    'discharge_time'                    => 'Discharge Time',
    'contact_time'                      => 'Contact Time',
    'parent_contact_time'               => 'Parent Contact Time',
    'actions_taken'                     => 'Actions Taken',
    'temperature'                       => 'Temperature',
    'blood_pressure'                    => 'Blood Pressure (BP)',
    'saturation'                        => 'Saturation (SpO2)',
    'pulse'                             => 'Pulse',
    'vitals'                            => 'Vitals',
    'height_weight'                     => 'Height & Weight',
    'height'                            => 'Height (cm)',
    'weight'                            => 'Weight (kg)',
    'medication'                        => 'Medication',
    'comments'                          => 'Comments',
    'add_record'                        => 'Add Record',
    'medical_condition'                 => 'Medical Condition',
    'regularly_used_medication'         => 'Regularly Used Medication',
    'vision_problem'                    => 'Any Vision Problem',
    'vision_check_date'                 => 'Vision Check Date',
    'any_hearing_issue'                 => 'Any Hearing Issue',
    'special_food_consideration'        => 'Special Food Consideration',
    'allergies'                         => 'Allergies',
    'allergy_symptoms'                  => 'Allergy Symptoms',
    'allergy_first_aid'                 => 'Allergy First Aid',
    'allowed_medications'               => 'Allowed Medications',
    'emergency_contact_1'               => 'Emergency Contact 1',
    'emergency_contact_2'               => 'Emergency Contact 2',
    'select_student'                    => 'Please Select Student From The List!',
    'select_staff'                      => 'Please Select Staff From The List!',

];
