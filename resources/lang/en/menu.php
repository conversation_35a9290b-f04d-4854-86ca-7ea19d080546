<?php

return [

    'home' => 'Home',
    'dashboard' => 'Dashboard',
    'my_account' => 'My Account',
    'library' => 'Library Manager',
    'medical_report' => 'Medical Report',
    'journal' => 'Journal',
    'timetable' => 'Timetable',
    'rooms' => 'Rooms',
    'calendar' => 'Calendar',
    'sms' => 'SMS',
    'permission_paper' => 'Permission Paper',
    'student_info' => 'Student Information',
    'class_list' => 'Class list',
    'smart_class' => 'Smart Class',
    'file_manager' => 'File Manager',
    'administration' => 'Administration',
    'messages' => 'Messages',
    'classes' => 'Classses',
    'subjects' => 'Subjects',
    'staff' => 'Staff',
    'students' => 'Students',
    'role_permissions' => 'Role & Permissions',
    'teacher_class_lesson' => 'Teacher Class Lesson',
    'timetable_manager' => 'Timetable Manager',
    'lesson_plan' => 'Lesson Plan',
    'term_plan' => 'Term Plan',
    'academy' => 'Academy',
    'appraisal' => 'Appraisal',
    'teacher_goals' => 'Teacher Goals',
    'student_attendance' => 'Student Attendance',
    'pickup_cards' => 'Schoolbus & Pickup Cards',
    'reports' => 'Reports',
    'report_staff_attendance' => 'Staff Attendance',
    'report_lesson_plan' => 'Lesson Plan',
    'professionalism' => 'Professionalism',
    'report_professionalism' => 'Professionalism',
    'student_goals' => 'Student Goals',
    'report_student_attendance' => 'Student Attendance',
    'feedback' => 'Feedback',
    'report_student_list' => 'Student List',
    'health' => 'Health',
    'report_term_plan' => 'Term Plan',
    'report_teacher_goals' => 'Teacher Goals',
    'report_student_goals' => 'Student Goals',
    'documents' => 'Documents',
    'employment_verification' => 'Employment Verification',
    'student_verification' => 'Student Verification',
    'attendance_settings' => 'Attendance Settings',
    'quarter_marks' => 'Quarter Marks',
    'report_card' => 'Report Card',
    'elective_lessons' => 'Elective Lessons',
    'behaviour' => 'Behaviour',
    'bps_records' => 'BPS Records',
    'bps_reports' => 'BPS Reports',
    'detention_reports' => 'Detention Reports',
    'bps_awards' => 'BPS Awards',
    'behaviour_analysis' => 'Behaviour Analysis',
    'behaviour_administration' => 'Behaviour Administration',
    'elective_grades' => 'Elective Grades',
    'summative_score_template' => 'Assessment Score Templates',
    'assessment' => 'Assessments',
    'major_discipline' => 'Major Discipline',
    'assessment_administration' => 'Assessment Administration',
    'report_leave_request' => 'Leave Requests',
    'report_staff_list' => 'Staff List',
    'report_annual_report_card' => 'Annual Report Card',
    'app_usage' => 'App Usage',
    'activity' => 'Activity',
    'activity_manager' => 'Activity Manager',
    'report_student_class_attendance' => 'Student Class Attendance',
    'student_progress' => 'Student Progress',
    'student_finance' => 'Student Finance',
    'comments' => 'Comments',
    'tools' => 'Tools',
    'homework' => 'Homework',
    'inventory' => 'Inventory',
    'homeroom' => 'Homeroom',
    'report_assessment_reports' => 'Assessment Reports',
    'report_overall_report_card' => 'Overall Report Card',
    'report_overall_subject_report' => 'Overall Subject Report',
    'archived_students' => 'Archived Students',
    'registration_forms' => 'Registration Forms',
    'student_achievement_tracker' => 'Student Achievement Tracker',
    'students_storage' => 'Student Storage',
    'mobile_notification' => 'Mobile Notifications',
    'transcript_generator' => 'Transcript Generator',
    'public_holiday' => 'Public Holiday',
    'discipline' => 'Discipline',
    'health_record' => 'Health Record',
    'logout' => 'Logout',
    'attendance' => 'Attendance',
    'academic_settings' => 'Academic Settings',
    'lbc' => 'LBC',
    'house' => 'House System',
    'visitor_badge' => 'Visitor Badge',
    'head_office' => 'Head Office',
    'head_office_passport' => 'Passport & Visa',
    'head_office_staff' => 'HO Staff',
    'head_office_student' => 'Students',
    'head_office_finance_category'  => 'Finance Category',
    'head_office_finance_safe'  => 'Finance Safe',
    'head_office_finance_template'  => 'Finance Template',
    'head_office_finance_settings'  => 'Finance Settings',
    'head_office_teacher_allocation' => 'Teacher Allocation',
    'head_office_behaviour_analysis' => 'Behaviour Analysis',
    'head_office_academic_year' => 'Academic Year',
    'head_office_mobile_setting' => 'Mobile Setting',

    'pickup_request'    => 'Pickup Requests',

    'finance'               => 'Finance',
    'finance_payments'      => 'Payments',
    'finance_expenses'      => 'Expenses',
    'finance_salary'        => 'Salary',
    'finance_annual_budget'        => 'Annual Budget',



    'attendance'        => 'Attendance',
    'daily_attendance'        => 'Daily Attendance',
    'kg_assessment'        => 'KG Assessment',
    'kg_report'            => 'KG Report',

];
