@extends('layouts.app')
@section('content')
    <?php
    use App\Models\TeacherClassLesson;
    use App\Models\AcademicSemester;
    use App\Models\AcademicWeek;
    use App\Models\DetentionDutyList;
    use App\Models\DetentionRecord;
    use App\Models\StudentInformation;
    use App\Models\UserDetail;
    use App\Library\Helper\AcademicHelper;
    use Carbon\Carbon;
    $ah = new AcademicHelper();
    
    $tcl = TeacherClassLesson::where('teacher_id', \Auth::user()->id)
        ->where('branch_id', $ah->currentBranch())
        ->where('academic_year_id', $ah->academicYear())
        ->first();
    $detention_duties = DetentionDutyList::where('duty_teacher_id', \Auth::user()->id)
        ->where('branch_id', $ah->currentBranch())
        ->where('academic_year_id', $ah->academicYear())
        ->where('status', 1)
        ->get();
    if (count($detention_duties) > 0) {
        $detention_type_ids = $detention_duties->pluck('detention_type_id');
        $detention_lists = DetentionRecord::whereIn('detention_type_id', $detention_type_ids)->where('branch_id', $ah->currentBranch())->where('academic_year_id', $ah->academicYear())->where('is_served', 0)->get();
    }
    $current_semester = AcademicSemester::where('is_default', 1)->where('branch_id', $ah->currentBranch())->first();
    $today = date('Y-m-d');
    $weekInfo = AcademicWeek::where('start_date', '<=', $today)->where('end_date', '>=', $today)->where('branch_id', $ah->currentBranch())->first();
    $birthday_date = new DateTime(date('Y-m-d'));
    $birthdayStudents = StudentInformation::leftJoin('users', 'users.id', 'student_information.id')
        ->where('student_information.birth_date', 'like', '%-' . $birthday_date->format('m-d') . '%')
        ->where('student_information.branch_id', $ah->currentBranch())
        ->where('users.user_status', 1)
        ->get();
    $birthdayStaffs = UserDetail::leftJoin('users', 'users.id', 'users_detail.user_id')
        ->where('users_detail.date_of_birth', 'like', '%-' . $birthday_date->format('m-d') . '%')
        ->where('users.branch_id', $ah->currentBranch())
        ->where('users.user_status', 1)
        ->get();
    $birthdayPeople = collect([
        ...$birthdayStudents->map(
            fn($s) => [
                'name' => $s->name,
                'photo' => $s->photo,
            ],
        ),
        ...$birthdayStaffs->map(
            fn($s) => [
                'name' => $s->name,
                'photo' => $s->photo,
            ],
        ),
    ]);
    ?>
    <style>
        .balloon {
            position: absolute;
            bottom: -100px;
            width: 130px;
            height: 145px;
            background: radial-gradient(circle at 30% 30%, #fff, transparent),
                radial-gradient(circle at 30% 30%, var(--balloon-color), var(--balloon-color));
            background-blend-mode: overlay;
            border-radius: 50% 50% 50% 50%;
            animation: floatUp 4s ease-in forwards;
            /* opacity: 0.9; */
        }

        .balloon::before {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 50%;
            width: 6px;
            height: 6px;
            background-color: var(--balloon-color);
            transform: translateX(-50%) rotate(45deg);
        }

        .balloon::after {
            content: '';
            position: absolute;
            bottom: -30px;
            left: 50%;
            width: 1px;
            height: 25px;
            background-color: #555;
            transform: translateX(-50%);
        }

        @keyframes floatUp {
            0% {
                transform: translateY(0);
                opacity: 1;
            }

            100% {
                transform: translateY(-120vh);
                opacity: 0;
            }
        }

        .full-sparkle {
            position: absolute;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: gold;
            /* opacity: 0.95; */
            animation: sparkle-burst 4s ease-out forwards;
            box-shadow: 0 0 10px #fff5cc;
        }

        @keyframes sparkle-burst {
            0% {
                transform: scale(1) translate(0, 0);
                opacity: 1;
            }

            100% {
                transform: scale(0.8) translate(var(--dx), var(--dy));
                opacity: 0;
            }
        }

        .studentCard {
            width: 250px;
            text-align: center;
            background: #fff;
            border-radius: 12px;
            padding: 10px;
            box-shadow: 0 0 10px rgba(255, 255, 255, 0.8);
            animation: fade-in-up 1s ease-out;
        }

        .studentImage {
            width: 100%;
            height: 250px;
            object-fit: cover;
            border-radius: 50%;
            /* border: 3px solid #FFD700; */
            margin-bottom: 8px;
        }

        .studentName {
            font-size: 16px;
            font-weight: bold;
            color: #333;
        }

        @keyframes fade-in-up {
            from {
                opacity: 0;
                transform: translateY(30px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>

    <div class="d-none" id="userCurrentBranch" data-id="{{ $ah->currentBranch() }}"></div>
    <div class="d-none" id="userBranch" data-id="{{ Auth::user()->branch_id }}" data-week={{ $weekInfo }}></div>
    <link href="/theme/dist/assets/plugins/custom/fullcalendar/fullcalendar.bundle.css" rel="stylesheet" type="text/css" />
    @if (count($detention_duties) > 0)
        {{-- {{$detention_lists}} --}}
        {{-- <div class="text-center text-secondary fs-3">Welcome</div> --}}
        <div id="customAlert" class="alert alert-warning"
            style="display: none; position: fixed; top: 20px; right: 20px; z-index: 9999; width:400px; text-align:center;">
            <strong>You have</strong> <span class="alert-message"> Detention Duty!!</span>
        </div>
        <div class="col-12 pe-10 pt-4 text-end">
            <button class="btn btn-outline-dark position-relative btn-sm" title="You have Detention Duty!!" type="button"
                id="btnDetentionList">
                {{-- Detention Lists --}}
                <i class="fa-solid fa-circle-exclamation fs-2"></i>
                <span
                    class="bullet bullet-dot bg-danger h-10px w-10px position-absolute translate-middle top-0 end-0  start-90 animation-blink"></span>
            </button>
        </div>
    @endif
    <div class="app-main flex-column flex-row-fluid" id="kt_app_main">
        <div class="d-flex flex-column flex-column-fluid">
            <div id="kt_app_toolbar" class="app-toolbar py-3 py-lg-6">

            </div>
            <div id="kt_app_content" class="app-content flex-column-fluid">
                <div id="kt_app_content_container" class="app-container container-fluid">
                    <div class="card card-flush h-md-100"
                        style="border-top-right-radius: 0px; border-bottom-right-radius: 0px;">
                        <div class="card-body" style="padding: 0px;">
                            <div class="row">
                                @if ($tcl)
                                    <div class="col-12">
                                        @include('partial_view.app.home.general_widget_teacher')
                                    </div>
                                    <div class="col-md-8 col-sm-12" style="padding: 0px; margin: 0px;">
                                        @include('partial_view.app.home.staff_timetable')
                                        {{-- </div> --}}
                                    @else
                                        <div class="col-md-8 col-sm-12"
                                            style="padding: 0px; margin: 0px; background: linear-gradient(112.14deg, #00D2FF 0%, #3A7BD5 100%);">
                                            @include('partial_view.app.home.general_widget')
                                            {{-- <div> --}}
                                @endif
                            </div>

                            <div class="col-md-4 col-sm-12">
                                @include('partial_view.app.home.staff_attendance')
                            </div>

                            {{-- <div class="col-12" style="padding: 0px; margin: 0px;">
                                    <iframe
                                        src="https://webchat.botframework.com/embed/SISBotCambodia?s=UH5I8bnqwsk.obvbNDPm5-LzuBfykwaHmvfUhQ-h3NUDCgA66pKW7YI"
                                        style='min-width: 400px; width: 100%; min-height: 500px;'></iframe>
                                </div> --}}
                        </div>


                    </div>
                </div>
            </div>
        </div>


    </div>
    </div>

    <div class="modal fade" tabindex="-1" id="modalRequestLeave">
        <div class="modal-dialog modal-xl modal-dialog-centered modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">{{ __('app/app.new_leave_request') }}</h5>
                    <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal"
                        aria-label="Close">
                        <i class="fa fa-times"></i>
                    </div>
                </div>
                <div class="modal-body" id="modalRequestLeaveArea">

                </div>
                <div class="modal-footer justify-content-between">
                    <div>
                        <button type="button" id="btnRequestBack" style="display: none;" class="btnStep btn btn-primary"
                            data-type="back"><i class="fas fa-arrow-left"></i> {{ __('general.back') }}</button>
                        <button type="button" id="btnRequestNext" class="btnStep btn btn-primary"
                            data-type="next">{{ __('general.continue') }} <i class="fas fa-arrow-right"></i></button>
                    </div>
                    <div>
                        <button type="button" class="btn btn-light"
                            data-bs-dismiss="modal">{{ __('general.close') }}</button>
                        <button type="button" class="btn btn-danger"
                            id="btnStoreRequest">{{ __('general.submit_request') }}</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" tabindex="-1" id="modalLeaveRequestList">
        <div class="modal-dialog modal-xl modal-dialog-centered modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">{{ __('app/app.my_leave_request') }}</h5>
                    <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal"
                        aria-label="Close">
                        <i class="fa fa-times"></i>
                    </div>
                </div>
                <div class="modal-body" id="modalLeaveRequestListArea">

                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">{{ __('general.close') }}</button>
                </div>
            </div>
        </div>
    </div>

    <div class="modal bg-white fade" tabindex="-1" id="modalAttendance">
        <div class="modal-dialog modal-fullscreen">
            <div class="modal-content shadow-none">
                <div class="modal-header">
                    <h5 class="modal-title">{{ __('app/app.attendance') }}</h5>
                    <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal"
                        aria-label="Close">
                        <span class="svg-icon svg-icon-2x"></span>
                    </div>
                </div>
                <div class="modal-body" id="modalAttendanceArea">
                    <p class="text-center"><img src="/img/loader.gif" style="width:30px;" /></p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal"> {{ __('general.close') }}
                    </button>
                    <button type="button" class="btn btn-primary" id="btnSubmitAttendance">
                        {{ __('general.save_changes') }} </button>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" tabindex="-1" style="opacity: 0.95;" id="modalAnnouncements">
        <div class="modal-dialog modal-lg modal-dialog-centered modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">{{ __('app/app.announcements') }}</h5>
                    <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal"
                        aria-label="Close">
                        <i class="fa fa-times"></i>
                    </div>
                </div>
                <div class="modal-body">
                    @include('partial_view.app.home.announcement')
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal"> {{ __('general.close') }}
                    </button>
                    <button type="button" class="btn btn-primary" id="btnReadAllAnnouncements">
                        {{ __('general.mark_read') }} </button>
                </div>
            </div>
        </div>
    </div>
    @if (count($detention_duties) > 0)
        <div class="modal fade" tabindex="-1" id="detentionListModal">
            <div class="modal-dialog modal-fullscreen modal-dialog-centered modal-dialog-scrollable ">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Detention Lists</h5>
                        <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal"
                            aria-label="Close">
                            <i class="fa fa-times"></i>
                        </div>
                    </div>
                    <div class="modal-body">
                        {{-- @include('partial_view.app.home.announcement') --}}
                        <div class=" mb-3">
                            <div class="table-responsive p-3 rounded">
                                <h5 class="text-danger text-center">Detention Lists</h5>
                                <table class="table table-rounded table-striped" id="detentionListTable">
                                    <thead>
                                        <tr class="fw-bold">
                                            <th style="padding-left: 10px; white-space: nowrap;">Student</th>
                                            <th>Type</th>
                                            <th>System Note</th>
                                            <th>Date</th>
                                            <th>Served?</th>
                                            <th></th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach ($detention_lists as $detention_list)
                                            <tr>
                                                <td class="fw-bold text-danger"
                                                    style="padding-left: 10px; white-space: nowrap;">
                                                    {{ $detention_list->student->name }}
                                                </td>
                                                <td>
                                                    {{ $detention_list->type->detention_type_setting_label }}
                                                </td>
                                                <td>
                                                    {{ $detention_list->system_note }}
                                                </td>
                                                <td>
                                                    {{ $detention_list->date }}
                                                </td>
                                                <td>
                                                    @if ($detention_list->is_served)
                                                        <i class="fa-solid fa-check ms-2 text-success h5"></i>
                                                    @else
                                                        <i class="fa-solid fa-xmark ms-2 text-danger h5"></i>
                                                    @endif
                                                </td>
                                                <td>
                                                    <a class="btnSubmitDetentionServe cursor-pointer"
                                                        title="Click to Mark as Served"
                                                        data-id="{{ $detention_list->detention_record_id }}"><i
                                                            class="fa-solid fa-check ms-2 h5"></i></a>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-light" data-bs-dismiss="modal"> {{ __('general.close') }}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    @endif
    @if (count($birthdayPeople) > 0)
        <div id="birthdayPopup"
            style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; z-index: 99999;  text-align: center; pointer-events: none;">
            <h1 id="birthdayTitle"
                style="margin-top: 40px; font-size: 60px; font-weight: bold; color: transparent; background-clip: text; background-image: linear-gradient(45deg, #FF69B4, #FFD700, #ff1616, #cacd32, #FFA500); -webkit-background-clip: text;">

                @if (count($birthdayPeople) >= 1)
                    TODAY’S BIRTHDAY STARS!
                @else
                    TODAY’S BIRTHDAY STAR!
                @endif
            </h1>
            <div id="birthdayStudents"
                style="margin-top: 30px; display: flex; flex-wrap: wrap; justify-content: center; gap: 20px;">
                @foreach ($birthdayPeople as $student)
                    <div class="studentCard">
                        <img src="{{ $student['photo'] }}" class="studentImage" />
                        <p class="studentName">{{ $student['name'] }}</p>
                    </div>
                @endforeach
            </div>
            <h1 id="birthdayTitle"
                style="margin-top: 40px; font-size: 60px; font-weight: bold; color: transparent; background-clip: text; background-image: linear-gradient(45deg, #FF69B4, #ff00b3, #16c5ff, #32CD32, #FFA500); -webkit-background-clip: text;">
                HAPPY BIRTHDAY
            </h1>
        </div>
        <div id="fullPageSparkles"
            style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; z-index: 9999; pointer-events: none;">
        </div>
        <div id="balloonWrapper"
            style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; pointer-events: none; z-index: 9999;">
        </div>
    @endif
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.1/jquery.min.js"
        integrity="sha512-aVKKRRi/Q/YV+4mjoKBsE4x3H+BkegoM/em46NNlCqNTmUYADjBbeNefNxYV7giUp0VxICtqdrbqU7iVaeZNXA=="
        crossorigin="anonymous" referrerpolicy="no-referrer"></script>

    <script src="/library/js/app/home.js"></script>
@endsection
