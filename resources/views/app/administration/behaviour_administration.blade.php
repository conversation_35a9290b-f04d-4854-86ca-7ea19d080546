@extends('layouts.app')
@section('content')
    <div class="app-main flex-column flex-row-fluid" id="kt_app_main">
        <div class="d-flex flex-column flex-column-fluid">
            <div id="kt_app_toolbar" class="app-toolbar py-3 py-lg-6">
                <div id="kt_app_toolbar_container" class="app-container container-xxl d-flex flex-stack">
                    <div class="page-title d-flex flex-column justify-content-center flex-wrap me-3">
                        <h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">
                            {{ __('app/administration/behaviour_administration.behaviour_administration') }} </h1>
                        <ul class="breadcrumb breadcrumb-separatorless fw-semibold fs-7 my-0 pt-1">
                            <li class="breadcrumb-item text-muted">
                                <a href="/app/home" class="text-muted text-hover-primary"> {{ __('general.home') }} </a>
                            </li>
                            <li class="breadcrumb-item">
                                <span class="bullet bg-gray-400 w-5px h-2px"></span>
                            <li class="breadcrumb-item text-muted"> {{ __('general.administration') }} </li>
                            <li class="breadcrumb-item">
                                <span class="bullet bg-gray-400 w-5px h-2px"></span>
                            <li class="breadcrumb-item text-muted">
                                {{ __('app/administration/behaviour_administration.behaviour_administration') }} </li>
                        </ul>
                    </div>
                    <div class="d-flex align-items-center gap-2 gap-lg-3">
                        {{-- <a href="#" class="btn btn-sm fw-bold btn-primary" id="btnAddClassroom" data-bs-toggle="modal" data-bs-target="#modalAddClassroom">Create</a> --}}
                        <a href="#" class="btn btn-sm fw-bold btn-primary" id="btnAddBpsItem"
                            data-bs-toggle="modal"><i class="fas fa-plus"></i>
                            {{ __('app/administration/behaviour_administration.add_item') }} </a>
                        <a href="#" class="btn btn-sm fw-bold btn-dark" id="btnViewBpsArchive"
                            data-bs-toggle="modal"><i class="fas fa-archive"></i>
                            {{ __('app/administration/behaviour_administration.archive') }} </a>
                        <a href="#" class="btn btn-sm fw-bold btn-primary" id="btnAddDetentionDuty"
                            style="display: none;" data-bs-toggle="modal"><i class="fas fa-plus"></i>
                            {{ __('app/administration/behaviour_administration.add_duty') }} </a>
                        {{-- <a href="#" class="btn btn-sm fw-bold btn-dark" id="btnViewBpsArchive" data-bs-toggle="modal" ><i class="fas fa-archive"></i>
                            {{ __('app/administration/behaviour_administration.archive') }} </a> --}}
                    </div>
                </div>
            </div>


            <div id="kt_app_content" class="app-content flex-column-fluid">
                <div id="kt_app_content_container" class="app-container container-fluid">
                    <div class="card card-flush h-md-100"
                        style="border-top-right-radius: 0px; border-bottom-right-radius: 0px;">
                        <div class="card-body" style="padding: 15px;">
                            <ul class="nav nav-tabs nav-line-tabs mb-5 fs-6">
                                <li class="nav-item">
                                    <a class="nav-link active" id="tabBPS" data-bs-toggle="tab"
                                        href="#tabBpsAndDpsSetting">
                                        {{ __('app/administration/behaviour_administration.bdp_and_dps_setting') }}</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link " id="tabDetention" data-bs-toggle="tab" href="#tabDetentionSetting">
                                        {{ __('app/administration/behaviour_administration.detention_setting') }} </a>
                                </li>
                            </ul>

                            <div class="tab-content" id="myTabContent">
                                <div class="tab-pane show active " id="tabBpsAndDpsSetting" role="tabpanel">
                                    <div class="row">
                                        <div class="col-12">
                                            @include('partial_view.app.administration.behaviour_administration.bps_list')
                                        </div>
                                        {{-- <div id="academicSemesterArea" class="col-6"> --}}
                                        {{-- @include('partial_view.app.administration.settings_strand_skill_list') --}}
                                        {{-- </div> --}}
                                    </div>
                                </div>
                                <div class="tab-pane " id="tabDetentionSetting" role="tabpanel">
                                    <div class="row">
                                        <div class="col-12">
                                            @include('partial_view.app.administration.behaviour_administration.detention_duty_list')
                                        </div>
                                        {{-- <div id="strandSkillArea" class="col-4"> --}}
                                        {{-- @include('partial_view.app.administration.settings_strand_skill_list') --}}
                                        {{-- </div> --}}
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!--end:::Main-->

    <div class="modal fade" tabindex="-1" id="modalAddBpsItem">
        <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">{{ __('app/administration/behaviour_administration.add_item') }}</h5>
                    <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal"
                        aria-label="Close">
                        <i class="fa fa-times"></i>
                    </div>
                </div>

                <div class="modal-body">
                    <div class="row mb-3">
                        <label for="inputEmail3"
                            class="col-3 col-form-label">{{ __('app/administration/behaviour_administration.type') }}</label>
                        <div class="col-9">
                            <select class="form-control required " data-control="select2" data-dropdown-parent="#modalAddBpsItem"
                                data-placeholder="{{ __('app/administration/behaviour_administration.select_type') }}"
                                id="bpsType">
                                <option value="dps">{{ __('app/administration/behaviour_administration.dps') }}</option>
                                <option value="prs">{{ __('app/administration/behaviour_administration.prs') }}</option>
                            </select>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <label for="inputEmail3"
                            class="col-3 col-form-label">{{ __('app/administration/behaviour_administration.title') }}</label>
                        <div class="col-9">
                            <input type="text" id="bpsTitle" class="form-control required" />
                        </div>
                    </div>
                    <div class="row mb-3">
                        <label for="inputEmail3"
                            class="col-3 col-form-label">{{ __('app/administration/behaviour_administration.point') }}</label>
                        <div class="col-9">
                            <input type="number" id="bpsPoint" class="form-control required" />
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal"> {{ __('general.close') }}
                    </button>
                    <button type="button" class="btn btn-primary" id="btnSubmitBpsItem">
                        {{ __('general.save') }} </button>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" tabindex="-1" id="modalBpsArchiveItem">
        <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">{{ __('app/administration/academic_settings.academic_semester') }}</h5>
                    <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal"
                        aria-label="Close">
                        <i class="fa fa-times"></i>
                    </div>
                </div>
                <div class="modal-body" id="bpsArchiveItemListArea">

                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal"> {{ __('general.close') }}
                    </button>
                    <button type="button" class="btn btn-primary" id="btnEditAcademicSemester">
                        Edit </button>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" tabindex="-1" id="modalBpsItemEdit">
        <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">{{ __('app/administration/behaviour_administration.edit_item') }}</h5>
                    <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal"
                        aria-label="Close">
                        <i class="fa fa-times"></i>
                    </div>
                </div>
                <div class="modal-body" id="bpsItemEditArea">

                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal"> {{ __('general.close') }}
                    </button>
                    <button type="button" class="btn btn-primary" id="btnEditBpsItem">
                        Edit </button>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" tabindex="-1" id="modalAddDetentionDutyList">
        <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">{{ __('app/administration/behaviour_administration.add_duty') }}</h5>
                    <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal"
                        aria-label="Close">
                        <i class="fa fa-times"></i>
                    </div>
                </div>

                <div class="modal-body">
                    <div class="row mb-3">
                        <label for="inputEmail3"
                            class="col-3 col-form-label">{{ __('app/administration/behaviour_administration.teacher_name') }}</label>
                        <div class="col-9">
                            <select class="form-control required " data-control="select2" data-dropdown-parent="#modalAddDetentionDutyList"
                                data-placeholder="{{ __('app/administration/behaviour_administration.teacher_name') }}"
                                id="dutyUserId">
                                <option value=""></option>
                                @foreach ($branch_users as $branch_user)
                                <option value="{{$branch_user->id}}">{{$branch_user->name}}
                                </option>
                                @endforeach
                            </select>
                        </div>
                    </div>

                <div class="row mb-3">
                        <label for="inputEmail3"
                            class="col-3 col-form-label">{{ __('app/administration/behaviour_administration.detention_type') }}</label>
                        <div class="col-9">
                            <select class="form-control required " data-control="select2" data-dropdown-parent="#modalAddDetentionDutyList"
                                data-placeholder="{{ __('app/administration/behaviour_administration.detention_type') }}"
                                id="detentionTypeId">
                                <option value=""></option>
                                @foreach ($dentention_types as $dentention_type)
                                <option value="{{$dentention_type->detention_type_setting_id}}">{{$dentention_type->detention_type_setting_label}}
                                </option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal"> {{ __('general.close') }}
                    </button>
                    <button type="button" class="btn btn-primary" id="btnSubmitDetentionDutyList">
                        {{ __('general.save') }} </button>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" tabindex="-1" id="modalDetentionDutyEdit">
        <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">{{ __('app/administration/behaviour_administration.edit_detention_duty') }}</h5>
                    <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal"
                        aria-label="Close">
                        <i class="fa fa-times"></i>
                    </div>
                </div>
                <div class="modal-body" id="detentionDutyEditArea">

                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal"> {{ __('general.close') }}
                    </button>
                    <button type="button" class="btn btn-primary" id="btnEditDetentionDuty">
                        Edit </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.1/jquery.min.js"
        integrity="sha512-aVKKRRi/Q/YV+4mjoKBsE4x3H+BkegoM/em46NNlCqNTmUYADjBbeNefNxYV7giUp0VxICtqdrbqU7iVaeZNXA=="
        crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script src="/library/js/app/administration/behaviour_administration.js"></script>
@endsection
