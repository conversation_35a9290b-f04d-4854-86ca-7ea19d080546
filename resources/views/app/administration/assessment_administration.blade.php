@extends('layouts.app')
@section('content')
<?php
use App\Library\Helper\AcademicHelper;
use App\Http\Controllers\GlobalController;
use App\Models\SummativeAssessment;
use App\Models\FormativeAssessment;
use App\Models\User;
$academicHelper = new AcademicHelper();
        $branchId = $academicHelper->currentBranch();
        $academicYearId = $academicHelper->academicYear();
?>
<div class="app-main flex-column flex-row-fluid" id="kt_app_main">
    <div class="d-flex flex-column flex-column-fluid">
        <div id="kt_app_toolbar" class="app-toolbar py-3 py-lg-6">
            <div id="kt_app_toolbar_container" class="app-container container-xxl d-flex flex-stack">
                <div class="page-title d-flex flex-column justify-content-center flex-wrap me-3">
                    <h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">
                        {{ __('app/administration/assessment_administration.assessment_administration') }} </h1>
                    <ul class="breadcrumb breadcrumb-separatorless fw-semibold fs-7 my-0 pt-1">
                        <li class="breadcrumb-item text-muted">
                            <a href="/app/home" class="text-muted text-hover-primary"> {{ __('general.home') }} </a>
                        </li>
                        <li class="breadcrumb-item">
                            <span class="bullet bg-gray-400 w-5px h-2px"></span>
                        <li class="breadcrumb-item text-muted"> {{ __('general.administration') }} </li>
                        <li class="breadcrumb-item">
                            <span class="bullet bg-gray-400 w-5px h-2px"></span>
                        <li class="breadcrumb-item text-muted">
                            {{ __('app/administration/assessment_administration.assessment_administration') }} </li>
                    </ul>
                </div>
                <div class="d-flex align-items-center gap-2 gap-lg-3">
                </div>
            </div>
        </div>


        <div id="kt_app_content" class="app-content flex-column-fluid">
            <div id="kt_app_content_container" class="app-container container-fluid">
                <div class="card card-flush h-md-100"
                    style="border-top-right-radius: 0px; border-bottom-right-radius: 0px;">
                    <div class="card-body" style="padding: 15px;">

                        <ul class="nav nav-tabs nav-line-tabs mb-5 fs-6">
                            <li class="nav-item">
                                <a class="nav-link active" data-bs-toggle="tab" href="#tabSearch">
                                    {{ __('app/administration/assessment_administration.search_edit') }} </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" data-bs-toggle="tab" href="#tabLogs">
                                    {{ __('app/administration/assessment_administration.logs') }} </a>
                            </li>
                        </ul>

                        <div class="tab-content" id="myTabContent">
                            {{-- <div class="tab-pane fade show active" id="tabSearch" role="tabpanel">
                                <div class="row">
                                    <div class="col-2">
                                        <div class="row mb-3">
                                            <label for="inputEmail3" class="col-form-label">{{
                                                __('app/administration/assessment_administration.type') }}</label>
                                            <div class="col">
                                                <select class="form-control" data-control="select2"
                                                    data-placeholder="Assessment Type" id="assessmentType">
                                                    <option value="summative">
                                                        {{ __('app/administration/assessment_administration.summative')
                                                        }}
                                                    </option>
                                                    <option value="formative">
                                                        {{ __('app/administration/assessment_administration.formative')
                                                        }}
                                                    </option>
                                                </select>
                                            </div>
                                        </div>

                                        <div class="row mb-3">
                                            <label for="inputEmail3" class="col-form-label">{{
                                                __('app/administration/assessment_administration.subject') }}</label>
                                            <div class="col">
                                                <select class="form-control" data-control="select2"
                                                    data-placeholder="Select subject" id="subjectList">
                                                    @foreach ($subjects as $subject)
                                                    <option value="{{$subject->subject_id}}">{{$subject->subject_name}}
                                                    </option>
                                                    @endforeach
                                                </select>
                                            </div>
                                        </div>

                                        <div class="row mb-3">
                                            <div class="col">
                                                <a class="btn btn-dark w-100" id="searchAssessment"><i
                                                        class="fas fa-search"></i> {{ __('general.search') }}</a>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-10">
                                        <table class="table table-striped table-hover border" id="assessmentTable">
                                            <thead>
                                                <th style="padding-left: 10px;">{{
                                                    __('app/administration/assessment_administration.assessment') }}
                                                </th>
                                                <th> {{ __('app/administration/assessment_administration.type') }}</th>
                                                <th> {{ __('app/administration/assessment_administration.date') }}</th>
                                                <th> {{ __('app/administration/assessment_administration.subject') }}
                                                </th>
                                                <th> {{ __('app/administration/assessment_administration.teacher') }}
                                                </th>
                                                <th> {{ __('app/administration/assessment_administration.grade') }}</th>
                                                <th></th>
                                            </thead>
                                            <tbody id="assessmentListArea">

                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div> --}}
                            <div class="tab-pane fade show active" id="tabSearch" role="tabpanel">
                                <div class="row">
                                    <div class="col-3">
                                        <table class="table table-striped table-hover border table-responsive"
                                            id="teacherAssessmentTable">
                                            <thead>
                                                <tr>

                                                    <th style="padding-left: 10px;">{{
                                                        __('app/administration/assessment_administration.teacher') }}
                                                    </th>
                                                    <th> {{
                                                        __('app/administration/assessment_administration.assessment')
                                                        }}</th>
                                                    {{-- <th> {{
                                                        __('app/administration/assessment_administration.formative')
                                                        }}</th> --}}
                                                    {{-- <th></th> --}}
                                                </tr>
                                            </thead>
                                            <tbody id="teacherAssessmentListArea">
                                                <?php
                                                    $assessmentData = SummativeAssessment::select('academic_summative_assessments.*','academic_elective_grade.grade_id','academic_elective_grade.grade_name')
                                                                                    ->leftJoin('academic_elective_grade','academic_elective_grade.grade_id','academic_summative_assessments.grade_id')
                                                                                    // ->where('academic_summative_assessments.teacher_id',$teacher->id)
                                                                                    ->where('academic_summative_assessments.academic_year_id',$academicYearId)
                                                                                    ->where('academic_summative_assessments.branch_id',$branchId)
                                                                                    ->get()
                                                                                    ->groupby('teacher_id')
                                                                                    ->map(function ($teacherGroup) {
                                                                                        return $teacherGroup->groupBy('grade_id');
                                                                                    })
                                                                                    ->map(function ($gradeGroup) {
                                                                                        return $gradeGroup->groupBy('template_id');
                                                                                    });
                                                ?>
                                                @foreach ($assessmentData as $teacher_id => $assessments)
                                                <?php
                                                    $teacher = User::select('name')->find($teacher_id);
                                                ?>
                                                <tr data-id="{{$teacher_id}}" class="dataRow " style="cursor: pointer">
                                                    <td class="align-middle" style="padding-left: 10px;">
                                                        {{$teacher->name}}</td>

                                                    <?php
                                                        $summativeCount = 0 ;
                                                        $formativeCount = 0 ;
                                                        $rd ='<td>';
                                                        foreach($assessments as $grade_id => $assessment){
                                                            foreach($assessment as $template_id => $ass_detail){
                                                                // $grade_name = $ass_detail[0]['grade_name'] ?? '-';
                                                                // $rd .= $grade_name.' ('.count($ass_detail).')<br>';
                                                                $summativeCount += count($ass_detail);
                                                            }
                                                        }
                                                        $rd .='Assessment - '.$summativeCount.'<br>';


                                                        $formativeAssessmentData = FormativeAssessment::leftJoin('academic_elective_grade','academic_elective_grade.grade_id','academic_formative_assessments.grade_id')
                                                                                    ->where('academic_formative_assessments.teacher_id',$teacher_id)
                                                                                    ->where('academic_formative_assessments.academic_year_id',$academicYearId)
                                                                                    ->where('academic_formative_assessments.branch_id',$branchId)
                                                                                    ->get()
                                                                                    ->groupby('grade_id');
                                                                                    // $rd .='<td>';
                                                        foreach ($formativeAssessmentData as $formative_grade_id => $formativeAssessment) {
                                                            // $formative_grade_name = $formativeAssessment[0]['grade_name'] ?? '-';
                                                            // $rd .= $formative_grade_name.' ('.count($formativeAssessment).')<br>';
                                                            $formativeCount +=count($formativeAssessment);
                                                        }
                                                        $rd .='Life Skill - '.$formativeCount.'</td>';
                                                        echo $rd;
                                                        ?>
                                                    {{-- <td>jhkjhkjh -78</td> --}}
                                                </tr>
                                                @endforeach
                                            </tbody>
                                        </table>
                                    </div>
                                    <div class="col-9" id="assessmentListArea">
                                    </div>
                                </div>
                            </div>
                            <div class="tab-pane fade" id="tabLogs" role="tabpanel">
                                @include('partial_view.app.administration.assessment_logs')
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!--end:::Main-->

<div class="modal fade" tabindex="-1" id="modalViewAssessment">
    <div class="modal-dialog modal-xl modal-dialog-centered modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ __('app/administration/assessment_administration.assessment_details') }}</h5>
                <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal"
                    aria-label="Close">
                    <i class="fa fa-times"></i>
                </div>
            </div>

            <div class="modal-body" id="assessmentDetailArea">

            </div>
            <div class="modal-footer justify-content-between">
                <button type="button" class="btn btn-danger" id="btnDeleteAssessment"><i class="fas fa-trash"></i>
                    {{ __('app/administration/assessment_administration.delete_assessment') }} </button>
                <div>
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal"> {{ __('general.close') }}
                    </button>
                    <button type="button" class="btn btn-primary" id="btnSaveAssessmentDetail">
                        {{ __('general.save_changes') }} </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.1/jquery.min.js"
    integrity="sha512-aVKKRRi/Q/YV+4mjoKBsE4x3H+BkegoM/em46NNlCqNTmUYADjBbeNefNxYV7giUp0VxICtqdrbqU7iVaeZNXA=="
    crossorigin="anonymous" referrerpolicy="no-referrer"></script>
<script src="/library/js/app/administration/assessment_administration.js"></script>

@endsection