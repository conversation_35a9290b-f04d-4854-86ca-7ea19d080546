@extends('layouts.app')
@section('content')
<div class="app-main flex-column flex-row-fluid" id="kt_app_main">
    <div class="d-flex flex-column flex-column-fluid">

        <div id="kt_app_toolbar" class="app-toolbar py-3 py-lg-6">
			<div id="kt_app_toolbar_container" class="app-container container-xxl d-flex flex-stack">
				<div class="page-title d-flex flex-column justify-content-center flex-wrap me-3">
					<h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">{{ __('app/administration/teacher_class_lesson.teacher_class_lesson') }}</h1>
					<ul class="breadcrumb breadcrumb-separatorless fw-semibold fs-7 my-0 pt-1">
						<li class="breadcrumb-item text-muted">
							<a href="/app/home" class="text-muted text-hover-primary">{{ __('general.home') }}</a>
						</li>
						<li class="breadcrumb-item">
							<span class="bullet bg-gray-400 w-5px h-2px"></span>
						<li class="breadcrumb-item text-muted">{{ __('general.administration') }}</li>
                        <li class="breadcrumb-item">
							<span class="bullet bg-gray-400 w-5px h-2px"></span>
						<li class="breadcrumb-item text-muted">{{ __('app/administration/teacher_class_lesson.teacher_class_lesson') }}</li>
					</ul>
				</div>
				<div class="d-flex align-items-center gap-2 gap-lg-3">
					<a href="#" class="btn btn-sm fw-bold btn-primary" data-bs-toggle="modal" data-bs-target="#modalAddTcl">{{ __('app/administration/teacher_class_lesson.add_new') }}</a>
				</div>
			</div>
		</div>


        <div id="kt_app_content" class="app-content flex-column-fluid">
            <div id="kt_app_content_container" class="app-container container-fluid">
                <div class="card card-flush h-md-100" style="border-top-right-radius: 0px; border-bottom-right-radius: 0px;">
                    <div class="card-body" style="padding: 15px;">
                        <table id="tclTable" class="table table-striped border">
                            <thead>
                                <th style="padding-left: 10px;"> {{ __('app/administration/teacher_class_lesson.teacher') }} </th>
                                <th> {{ __('app/administration/teacher_class_lesson.class') }} </th>
                                <th> {{ __('app/administration/teacher_class_lesson.subject') }} </th>
                                <th> {{ __('app/administration/teacher_class_lesson.weekly_count') }} </th>
                                <th> {{ __('app/administration/teacher_class_lesson.start_date') }} </th>
                                <th> {{ __('app/administration/teacher_class_lesson.end_date') }} </th>
                                <th></th>
                            </thead>
                            <tbody>
                                @foreach ($data as $item)
                                    <?php  ?>
                                    <tr>
                                        <td style="padding-left: 10px;">{{$item->user->name ?? 'N/A'}}</td>
                                        <td>{{$item->classroom->classroom_name}}</td>
                                        <td>{{$item->subject->subject_name}}</td>
                                        <td>{{$item->weekly_count}}</td>
                                        <td>{{$item->start_date}}</td>
                                        <td>{{$item->end_date}}</td>
                                        <td style="text-align: right; padding-right: 10px;">
                                            <a href="#" class="btnEditTCL" data-id="{{$item->teacher_class_lesson_id}}"><i class="fas fa-edit"></i></a>
                                            <a href="#" class="btnDeleteTCL" data-id="{{$item->teacher_class_lesson_id}}"><i class="fas fa-trash"></i></a>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!--end:::Main-->

<div class="modal fade" tabindex="-1" id="modalAddTcl">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"> {{ __('app/administration/teacher_class_lesson.new_teacher_lesson') }} </h5>
                <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal" aria-label="Close">
                    <i class="fa fa-times"></i>
                </div>
            </div>

            <div class="modal-body">
                <form id="formNewTcl">

                    <div class="row mb-3">
                        <label for="inputEmail3" class="col-3 col-form-label"> {{ __('app/administration/teacher_class_lesson.teacher') }} </label>
                        <div class="col-9">
                            <select class="form-control" data-control="select2" data-dropdown-parent="#modalAddTcl" data-placeholder=" {{ __('app/administration/teacher_class_lesson.select_teacher') }} " required name="teacher">
                                <option>
                                @foreach($users as $key => $user)
                                    <option value="{{$user->id}}">{{$user->name}}</option>
                                @endforeach
                                </option>
                            </select>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <label for="inputEmail3" class="col-3 col-form-label"> {{ __('app/administration/teacher_class_lesson.class') }} </label>
                        <div class="col-9">
                            <select class="form-control" data-control="select2" multiple data-placeholder=" {{ __('app/administration/teacher_class_lesson.select_classroom') }} " required name="classrooms[]">
                                <option>
                                @foreach($classrooms as $key => $classroom)
                                    <option value="{{$classroom->classroom_id}}">{{$classroom->classroom_name}}</option>
                                @endforeach
                                </option>
                            </select>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <label for="inputEmail3" class="col-3 col-form-label"> {{ __('app/administration/teacher_class_lesson.subject') }} </label>
                        <div class="col-9">
                            <select class="form-control" data-control="select2" multiple data-placeholder=" {{ __('app/administration/teacher_class_lesson.select_subject') }} " name="subjects[]">
                                <option>
                                @foreach($subjects as $key => $subject)
                                    <option value="{{$subject->subject_id}}">{{$subject->subject_name}}</option>
                                @endforeach
                                </option>
                            </select>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <label for="inputEmail3" class="col-3 col-form-label"> {{ __('app/administration/teacher_class_lesson.weekly_count') }} </label>
                        <div class="col-9">
                            <input type="number" class="form-control" name="weekly_count" required />
                        </div>
                    </div>

                    <div class="row mb-3">
                        <label for="inputEmail3" class="col-3 col-form-label"> {{ __('app/administration/teacher_class_lesson.start_date') }} </label>
                        <div class="col-9">
                            <input type="date" class="form-control" name="start_date" />
                        </div>
                    </div>

                    <div class="row mb-3">
                        <label for="inputEmail3" class="col-3 col-form-label"> {{ __('app/administration/teacher_class_lesson.end_date') }} </label>
                        <div class="col-9">
                            <input type="date" class="form-control" name="end_date" />
                        </div>
                    </div>


                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-light" data-bs-dismiss="modal"> {{ __('general.close') }} </button>
                <button type="button" class="btn btn-primary" id="btnSubmitTcl"> {{ __('general.save_changes') }} </button>
            </div>
        </div>
    </div>
</div>


    <!-- Edit Modal -->
    <div class="modal fade" tabindex="-1" id="modalEditTCL">
        <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">{{ __('Learning Area') }}</h5>
                    <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal"
                        aria-label="Close">
                        <i class="fa fa-times"></i>
                    </div>
                </div>

                <div class="modal-body" id="updateTCLArea">

                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal"> {{ __('general.close') }}</button>
                    <button type="button" class="btn btn-primary" id="btnUpdateTCL">
                        {{ __('general.save') }}</button>
                </div>
            </div>
        </div>
    </div>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.1/jquery.min.js" integrity="sha512-aVKKRRi/Q/YV+4mjoKBsE4x3H+BkegoM/em46NNlCqNTmUYADjBbeNefNxYV7giUp0VxICtqdrbqU7iVaeZNXA==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
<script src="/library/js/app/administration/teacher_class_lesson.js"></script>

@endsection
