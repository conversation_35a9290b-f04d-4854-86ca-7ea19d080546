@extends('layouts.app')
@section('content')
    <div class="app-main flex-column flex-row-fluid" id="kt_app_main">
        <div class="d-flex flex-column flex-column-fluid">
            <div id="kt_app_toolbar" class="app-toolbar py-3 py-lg-6">
                <div id="kt_app_toolbar_container" class="app-container container-xxl d-flex flex-stack">
                    <div class="page-title d-flex flex-column justify-content-center flex-wrap me-3">
                        <h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">
                            {{ __('app/administration/summative_score_template.summative_score_templates') }} </h1>
                        <ul class="breadcrumb breadcrumb-separatorless fw-semibold fs-7 my-0 pt-1">
                            <li class="breadcrumb-item text-muted">
                                <a href="/app/home" class="text-muted text-hover-primary"> {{ __('general.home') }} </a>
                            </li>
                            <li class="breadcrumb-item">
                                <span class="bullet bg-gray-400 w-5px h-2px"></span>
                            <li class="breadcrumb-item text-muted"> {{ __('general.administration') }} </li>
                            <li class="breadcrumb-item">
                                <span class="bullet bg-gray-400 w-5px h-2px"></span>
                            <li class="breadcrumb-item text-muted">
                                {{ __('app/administration/summative_score_template.summative_score_templates') }} </li>
                        </ul>
                    </div>
                    <div class="d-flex align-items-center gap-2 gap-lg-3">
                        <a href="#" class="btn btn-sm fw-bold btn-primary" id="btnAddTemplate" data-bs-toggle="modal"
                            data-bs-target="#modelNewTemplate"><i class="fas fa-plus"></i>
                            {{ __('app/administration/summative_score_template.template') }} </a>
                    </div>
                </div>
            </div>


            <div id="kt_app_content" class="app-content flex-column-fluid">
                <div id="kt_app_content_container" class="app-container container-fluid">
                    <div class="card card-flush h-md-100"
                        style="border-top-right-radius: 0px; border-bottom-right-radius: 0px;">
                        <div class="card-body" style="padding: 15px;">
                            @include('partial_view.app.administration.template_list')
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!--end:::Main-->


    <div class="modal fade" tabindex="-1" id="modelNewTemplate">
        <div class="modal-dialog modal-xl modal-dialog-centered modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">{{ __('app/administration/summative_score_template.new_template') }}</h5>
                    <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal"
                        aria-label="Close">
                        <i class="fa fa-times"></i>
                    </div>
                </div>

                <div class="modal-body" id="classroomResponseArea">
                    <form id="formNewTemplate">

                        <div class="row mb-3">
                            <label for="inputEmail3"
                                class="col-3 col-form-label">{{ __('app/administration/summative_score_template.template_name') }}</label>
                            <div class="col-9">
                                <input type="text" class="form-control" name="template_name" required />
                            </div>
                        </div>

                        <div class="row mb-3">
                            <label for="inputEmail3"
                                class="col-3 col-form-label">{{ __('app/administration/summative_score_template.subject') }}</label>
                            <div class="col-9">
                                <select class="form-control" data-control="select2" required
                                    data-dropdown-parent="#modelNewTemplate" data-placeholder="Select Subject"
                                    name="subject" id="subjectList">
                                    <option>-</option>
                                    <option>
                                        @foreach ($subjects as $key => $subject)
                                    <option value="{{ $subject->subject_id }}">{{ $subject->subject_name }}</option>
                                    @endforeach
                                    </option>
                                </select>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <label for="inputEmail3"
                                class="col-3 col-form-label">{{ __('app/administration/summative_score_template.grades') }}</label>
                            <div class="col-9">
                                <select class="form-control" data-control="select2" required multiple
                                    data-placeholder="{{ __('app/administration/summative_score_template.select_grades') }}"
                                    name="grades[]" id="gradeList">
                                </select>
                                <p id="alert-banner">
                                </p>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <label for="inputEmail3" class="col-3 col-form-label">Final & Mid Term ?</label>
                            <div class="col-9">
                                <select class="form-control" data-control="select2" required data-placeholder="Select Type"
                                    name="finalAndMidTerm" id="templateOptionIsFinalAndMidTerm">
                                    <option value="" >Select Type</option>
                                    <option value="0">No</option>
                                    <option value="1">
                                        Yes
                                    </option>
                                </select>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <label for="inputEmail3"
                                class="col-3 col-form-label">{{ __('app/administration/summative_score_template.options') }}</label>
                            <div class="col-9">
                                <div class="row">

                                    {{-- <div class="col-5">
                                        <div class="row mb-3">
                                            <label for="inputEmail3"
                                                class="col-3 col-form-label">{{ __('app/administration/summative_score_template.title') }}</label>
                                            <div class="col-9">
                                                <input type="text" class="form-control" id="optionTitle" />
                                            </div>
                                        </div>
                                        <div class="row mb-3">
                                            <label for="inputEmail3"
                                                class="col-3 col-form-label">{{ __('app/administration/summative_score_template.percentage') }}</label>
                                            <div class="col-9">
                                                <input type="number" class="form-control" id="optionPercentage" />
                                            </div>
                                        </div> --}}
                                        {{-- <div class="row mb-3">
                                            <label for="inputEmail3" class="col-3 col-form-label">Term-End
                                                Percentage ?</label>
                                            <div class="col-9">
                                                <select class="form-control" data-control="select2" required
                                                    data-dropdown-parent="#modelNewTemplate"
                                                    data-placeholder="Select Type" name="is_final_exam_percentage"
                                                    id="isFinalExamPercentage">
                                                    <option value="0">No</option>
                                                    <option value="1">
                                                        Yes
                                                    </option>
                                                </select>
                                            </div>
                                        </div> --}}
                                        {{-- <div class="row mb-3">
                                            <a class="btn btn-dark btn-sm"
                                                id="btnAddOption">{{ __('app/administration/summative_score_template.insert_option') }}</a>
                                        </div>
                                    </div> --}}

                                    <div class="col-12">
                                        <table class="table table-striped table-hover">
                                            <thead>
                                                <th style="padding-left: 10px;">
                                                    {{ __('app/administration/summative_score_template.title') }} </th>
                                                <th style="padding-left: 10px;">
                                                    {{ __('app/administration/summative_score_template.percentage') }}
                                                </th>
                                                <th style="text-align: right;">
                                                    Term-End Percentage ?
                                                </th>
                                                <th></th>
                                            </thead>
                                            <tbody id="optionTbody"></tbody>
                                            <tfoot>
                                                <tr style="background-color: #f0f0f0;">
                                                    <td style="padding-left: 10px; font-weight: bold;">
                                                        {{ __('app/administration/summative_score_template.total') }}</td>
                                                    <td style="padding-left: 10px;font-weight: bold;"
                                                        id="totalPercentage">0
                                                    </td>
                                                    <td></td>
                                                    <td></td>
                                                </tr>
                                            </tfoot>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <input type="hidden" name="option_data" id="optionData" required />
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal"> {{ __('general.close') }}
                    </button>
                    <button type="button" class="btn btn-primary" id="btnSubmitTemplate">
                        {{ __('general.save_changes') }} </button>
                </div>
            </div>
        </div>
    </div>


    <div class="modal fade" tabindex="-1" id="modalEditTemplate">
        <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"> {{ __('app/administration/summative_score_template.edit_template') }} </h5>
                    <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal"
                        aria-label="Close">
                        <i class="fa fa-times"></i>
                    </div>
                </div>

                <div class="modal-body" id="classroomResponseArea">
                    <div class="row mb-3">
                        <label for="inputEmail3" class="col-3 col-form-label">
                            {{ __('app/administration/summative_score_template.template_name') }} </label>
                        <div class="col-9">
                            <input type="text" class="form-control" id="templateEditName" />
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal"> {{ __('general.close') }}
                    </button>
                    <button type="button" class="btn btn-primary" id="btnUpdateTemplate">
                        {{ __('general.save_changes') }} </button>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" tabindex="-1" id="modalAddGrade">
        <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"> {{ __('app/administration/summative_score_template.edit_template') }} </h5>
                    <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal"
                        aria-label="Close"><i class="fa fa-times"></i>
                    </div>
                </div>

                <div class="modal-body">
                    <div class="row mb-3">
                        <label for="inputEmail3" class="col-3 col-form-label">
                            {{ __('app/administration/summative_score_template.grades') }} </label>
                        <div class="col-9">
                            <select class="form-control" data-control="select2" required multiple
                                data-placeholder=" {{ __('app/administration/summative_score_template.select_grades') }} "
                                name="grades[]" id="gradeListEdit">
                            </select>
                            <p id="grade-alert-banner"></p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal"> {{ __('general.close') }}
                    </button>
                    <button type="button" class="btn btn-primary" id="btnSubmitGrade"> {{ __('general.save_changes') }}
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" tabindex="-1" id="modalAddOption">
        <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"> {{ __('app/administration/summative_score_template.add_option') }} </h5>
                    <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal"
                        aria-label="Close"><i class="fa fa-times"></i>
                    </div>
                </div>

                <div class="modal-body">
                    <div class="row mb-3">
                        <label for="inputEmail3" class="col-3 col-form-label">
                            {{ __('app/administration/summative_score_template.title') }} </label>
                        <div class="col-9">
                            <input type="text" id="templateOptionTitle" class="form-control" />
                        </div>
                    </div>

                    <div class="row mb-3">
                        <label for="inputEmail3" class="col-3 col-form-label">
                            {{ __('app/administration/summative_score_template.percentage') }} </label>
                        <div class="col-9">
                            <input type="number" id="templateOptionPercentage" class="form-control" />
                        </div>
                    </div>
                    {{-- <div class="row mb-3">
                        <label for="inputEmail3" class="col-3 col-form-label">Term-End Percentage ?</label>
                        <div class="col-9">
                            <select class="form-control" data-control="select2" required
                                data-dropdown-parent="#modalAddOption" data-placeholder="Select Type"
                                name="is_final_exam_percentage" id="templateOptionIsFinalExamPercentage">
                                <option value="0">No</option>
                                <option value="1">
                                    Yes
                                </option>
                            </select>
                        </div>
                    </div> --}}
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal"> {{ __('general.close') }}
                    </button>
                    <button type="button" class="btn btn-primary" id="btnSubmitOption">
                        {{ __('general.save_changes') }} </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.1/jquery.min.js"
        integrity="sha512-aVKKRRi/Q/YV+4mjoKBsE4x3H+BkegoM/em46NNlCqNTmUYADjBbeNefNxYV7giUp0VxICtqdrbqU7iVaeZNXA=="
        crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script src="/library/js/app/administration/score_template.js"></script>
@endsection
