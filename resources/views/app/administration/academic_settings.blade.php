@extends('layouts.app')
@section('content')
    @php
        use App\Library\Helper\AcademicHelper;
        $ah = new AcademicHelper();
        $academicYear = $ah->academicYear();
    @endphp
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.1/jquery.min.js"
        integrity="sha512-aVKKRRi/Q/YV+4mjoKBsE4x3H+BkegoM/em46NNlCqNTmUYADjBbeNefNxYV7giUp0VxICtqdrbqU7iVaeZNXA=="
        crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script src="/library/js/app/administration/academic_settings.js"></script>
    <!--begin::Main-->
    <div class="app-main flex-column flex-row-fluid" id="kt_app_main">
        <div class="d-flex flex-column flex-column-fluid">
            <div id="kt_app_toolbar" class="app-toolbar py-3 py-lg-6">
                <div id="kt_app_toolbar_container" class="app-container container-xxl d-flex flex-stack">
                    <div class="page-title d-flex flex-column justify-content-center flex-wrap me-3">
                        <h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">
                            {{ __('app/administration/academic_settings.academic_settings') }}
                        </h1>
                        <ul class="breadcrumb breadcrumb-separatorless fw-semibold fs-7 my-0 pt-1">
                            <li class="breadcrumb-item text-muted">
                                <a href="/app/home" class="text-muted text-hover-primary"> {{ __('menu.home') }} </a>
                            </li>
                            <li class="breadcrumb-item">
                                <span class="bullet bg-gray-400 w-5px h-2px"></span>
                            <li class="breadcrumb-item text-muted"> {{ __('menu.administration') }} </li>
                            <li class="breadcrumb-item">
                                <span class="bullet bg-gray-400 w-5px h-2px"></span>
                            <li class="breadcrumb-item text-muted"> {{ __('menu.academic_settings') }} </li>
                        </ul>
                    </div>
                    <div class="d-flex align-items-center gap-2 gap-lg-3">
                        <a href="#" class="btn btn-sm fw-bold btn-primary" style="display: none;" id="btnAssignSS"><i
                                class="fas fa-plus"></i>
                            {{ __('app/administration/academic_settings.assign_ss') }} </a>
                        <a href="#" class="btn btn-sm fw-bold btn-primary" id="btnAddSubject" data-bs-toggle="modal"
                            style="display: none;"><i class="fas fa-plus"></i>
                            {{ __('app/administration/academic_settings.add_subject') }} </a>
                        <a href="#" class="btn btn-sm fw-bold btn-primary" id="btnSaveBellTimes" style="display: none;"><i
                                class="fas fa-save"></i>
                            {{ __('app/administration/academic_settings.save_bell_times') }} </a>
                        <a href="#" class="btn btn-sm fw-bold btn-primary" id="btnAddLearningArea" style="display: none;"><i
                                class="fas fa-plus"></i>
                            {{ __('Add Learning Area') }} </a>
                        <a href="#" class="btn btn-sm fw-bold btn-success" id="btnAddPreviousLearningArea"
                            style="display: none;"><i class="fas fa-plus"></i>
                            {{ __('Import from Previous Year') }} </a>
                    </div>
                </div>
            </div>


            <div id="kt_app_content" class="app-content flex-column-fluid">
                <div id="kt_app_content_container" class="app-container container-fluid">
                    <div class="card card-flush h-md-100"
                        style="border-top-right-radius: 0px; border-bottom-right-radius: 0px;">
                        <div class="card-body" style="padding: 15px;">
                            <ul class="nav nav-tabs nav-line-tabs mb-5 fs-6">
                                <li class="nav-item">
                                    <a class="nav-link active" id="tabAS" data-bs-toggle="tab" href="#tabAcademicSemester">
                                        Academic Semester</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link " id="tabSS" data-bs-toggle="tab" href="#tabStrandSkill">
                                        {{ __('app/administration/academic_settings.skills_strands') }} </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" data-bs-toggle="tab" id="tabExcludedSubjects" href="#tabExcluded">
                                        {{ __('app/administration/academic_settings.excluded_subjects') }} </a>
                                </li>
                                {{-- <li class="nav-item">
                                    <a class="nav-link" id="tabBellTimes" data-bs-toggle="tab" href="#tabBelltimes">
                                        {{ __('app/administration/academic_settings.bell_times') }} </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" id="tabLbcSettings" data-bs-toggle="tab" href="#tabLBC">
                                        {{ __('app/administration/academic_settings.lbc_settings') }} </a>
                                </li> --}}
                                <li class="nav-item">
                                    <a class="nav-link" data-bs-toggle="tab" id="tabAssessments" href="#tabAssessmentsDiv">
                                        {{ __('app/administration/academic_settings.assessments') }} </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" id="tabLearningArea" data-bs-toggle="tab" href="#tabLearning">
                                        {{ __('Learning Areas (KG)') }} </a>
                                </li>
                            </ul>

                            <div class="tab-content" id="myTabContent">
                                <div class="tab-pane show active " id="tabAcademicSemester" role="tabpanel">
                                    <div class="row">
                                        <div class="col-6">
                                            @include('partial_view.app.administration.academic_settings.academic_year')
                                        </div>
                                        <div id="academicSemesterArea" class="col-6">
                                            {{-- @include('partial_view.app.administration.settings_strand_skill_list') --}}
                                        </div>
                                    </div>
                                </div>
                                <div class="tab-pane " id="tabStrandSkill" role="tabpanel">
                                    <div class="row">
                                        <div class="col-8">
                                            @include('partial_view.app.administration.settings_assign_skill_strand')
                                        </div>
                                        <div id="strandSkillArea" class="col-4">
                                            @include('partial_view.app.administration.settings_strand_skill_list')
                                        </div>
                                    </div>
                                </div>
                                <div class="tab-pane fade " id="tabExcluded" role="tabpanel">
                                    @include('partial_view.app.administration.settings_excluded_subjects')
                                </div>
                                {{-- <div class="tab-pane fade" id="tabBelltimes" role="tabpanel">
                                    @include('partial_view.app.administration.settings_bell_times')
                                </div>
                                <div class="tab-pane fade" id="tabLBC" role="tabpanel">
                                    @include('partial_view.app.administration.settings_lbc')
                                </div> --}}
                                <div class="tab-pane fade " id="tabAssessmentsDiv" role="tabpanel">
                                    @include('partial_view.app.administration.assessments')
                                </div>

                                <div class="tab-pane fade" id="tabLearning" role="tabpanel">
                                    <div id="learning-area-content" class="tab-pane">
                                        <!-- Partial content will load here via AJAX -->
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!--end:::Main-->

    <div class="modal fade" tabindex="-1" id="modalAddSubject">
        <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">{{ __('app/administration/academic_settings.add_excluded_subject') }}</h5>
                    <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal"
                        aria-label="Close">
                        <i class="fa fa-times"></i>
                    </div>
                </div>

                <div class="modal-body">
                    <div class="row mb-3">
                        <label for="inputEmail3"
                            class="col-3 col-form-label">{{ __('app/administration/academic_settings.subject') }}</label>
                        <div class="col-9">
                            <select class="form-control" data-control="select2" data-dropdown-parent="#modalAddSubject"
                                data-placeholder="{{ __('app/administration/academic_settings.select_subject') }}"
                                id="excludedSubjectList">
                                <option>
                                    @foreach ($subjects as $key => $subject)
                                        <option value="{{ $subject->subject_id }}">{{ $subject->subject_name }}</option>
                                    @endforeach
                                </option>
                            </select>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <label for="inputEmail3"
                            class="col-3 col-form-label">{{ __('app/administration/academic_settings.grade') }}</label>
                        <div class="col-9">
                            <select class="form-control" data-control="select2" multiple
                                data-dropdown-parent="#modalAddSubject"
                                data-placeholder="{{ __('app/administration/academic_settings.select_grade') }}"
                                id="excludedClassroomList">
                                <option>
                                    @foreach ($classrooms as $key => $classroom)
                                        <option value="{{ $classroom->classroom_id }}">{{ $classroom->classroom_name }}</option>
                                    @endforeach
                                </option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal"> {{ __('general.close') }}
                    </button>
                    <button type="button" class="btn btn-primary" id="btnSubmitExcludedSubject">
                        {{ __('general.save_changes') }} </button>
                </div>
            </div>
        </div>
    </div>


    {{--
    <div class="modal fade" tabindex="-1" id="modalEditAttribute">
        <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">{{ __('app/administration/academic_settings.edit_learner_attribute') }}</h5>
                    <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal"
                        aria-label="Close">
                        <i class="fa fa-times"></i>
                    </div>
                </div>

                <div class="modal-body">
                    <div class="row mb-3">
                        <label for="inputEmail3" class="col-3 col-form-label">{{
                            __('app/administration/academic_settings.title') }}</label>
                        <div class="col-9">
                            <input type="text" id="learnerAttributeTitle" class="form-control" />
                        </div>
                    </div>

                    <div class="row mb-3">
                        <label for="inputEmail3" class="col-3 col-form-label">{{
                            __('app/administration/academic_settings.info') }}</label>
                        <div class="col-9">
                            <input type="text" id="learnerAttributeInfo" class="form-control" />
                        </div>
                    </div>


                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal"> {{ __('general.close') }}
                    </button>
                    <button type="button" class="btn btn-primary" id="btnSaveAtribute">
                        {{ __('general.save_changes') }} </button>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" tabindex="-1" id="modalEditPoint">
        <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">{{ __('app/administration/academic_settings.edit_points') }} </h5>
                    <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal"
                        aria-label="Close">
                        <i class="fa fa-times"></i>
                    </div>
                </div>

                <div class="modal-body">
                    <div class="row mb-3">
                        <label for="inputEmail3" class="col-3 col-form-label">{{
                            __('app/administration/academic_settings.title') }}</label>
                        <div class="col-9">
                            <input type="text" id="pointTitle" class="form-control" />
                        </div>
                    </div>

                    <div class="row mb-3">
                        <label for="inputEmail3" class="col-3 col-form-label">{{
                            __('app/administration/academic_settings.score') }}</label>
                        <div class="col-9">
                            <input type="text" id="pointScore" class="form-control" />
                        </div>
                    </div>


                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal"> {{ __('general.close') }}
                    </button>
                    <button type="button" class="btn btn-primary" id="btnSavePoint"> {{ __('general.save_changes') }}
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" tabindex="-1" id="modalEditType">
        <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">{{ __('app/administration/academic_settings.edit_activity_type') }}</h5>
                    <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal"
                        aria-label="Close">
                        <i class="fa fa-times"></i>
                    </div>
                </div>

                <div class="modal-body">
                    <div class="row mb-3">
                        <label for="inputEmail3" class="col-3 col-form-label">{{
                            __('app/administration/academic_settings.title') }}</label>
                        <div class="col-9">
                            <input type="text" id="typeTitle" class="form-control" />
                        </div>
                    </div>

                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal"> {{ __('general.close') }}
                    </button>
                    <button type="button" class="btn btn-primary" id="btnSaveType"> {{ __('general.save_changes') }}
                    </button>
                </div>
            </div>
        </div>
    </div>
    --}}

    <div class="modal fade" tabindex="-1" id="modalAddSS">
        <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">{{ __('app/administration/academic_settings.add_skills_strands') }}</h5>
                    <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal"
                        aria-label="Close">
                        <i class="fa fa-times"></i>
                    </div>
                </div>

                <div class="modal-body">
                    <div class="row mb-3">
                        <label for="inputEmail3"
                            class="col-3 col-form-label">{{ __('app/administration/academic_settings.type') }}</label>
                        <div class="col-9">
                            <select class="form-control required chosen-select"
                                data-placeholder="{{ __('app/administration/academic_settings.select_type') }}" id="ssType">
                                <option value="strand">{{ __('app/administration/academic_settings.strand') }}</option>
                                <option value="skill">{{ __('app/administration/academic_settings.skill') }}</option>
                            </select>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <label for="inputEmail3"
                            class="col-3 col-form-label">{{ __('app/administration/academic_settings.title') }}</label>
                        <div class="col-9">
                            <input type="text" id="ssTitle" class="form-control required" />
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal"> {{ __('general.close') }}
                    </button>
                    <button type="button" class="btn btn-primary" id="btnSubmitSS">
                        {{ __('general.save') }} </button>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" tabindex="-1" id="modalAssignSS">
        <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">{{ __('app/administration/academic_settings.assign_ss') }}</h5>
                    <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal"
                        aria-label="Close">
                        <i class="fa fa-times"></i>
                    </div>
                </div>

                <div class="modal-body">
                    <div class="row mb-3">
                        <label for="inputEmail3" class="col-3 col-form-label">Term</label>
                        <div class="col-9">
                            <select class="form-control required chosen-select" data-dropdown-parent="#modalAssignSS"
                                id="termSS">
                                <option value="1">Term 1</option>
                                <option value="2">Term 2</option>
                            </select>
                            </select>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <label for="inputEmail3"
                            class="col-3 col-form-label">{{ __('app/administration/academic_settings.grade') }}</label>
                        <div class="col-6">
                            <select class="form-control" data-control="select2" multiple
                                data-dropdown-parent="#modalAssignSS"
                                data-placeholder="{{ __('app/administration/academic_settings.select_grade') }}"
                                id="modalGradeList">
                                @foreach ($elective_grades as $key => $classroom)
                                    <option value="{{ $classroom->grade_id }}">{{ $classroom->grade_name }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-3 ">
                            <button class="btn btn-danger btn-sm" id="btnSelectAllGrade">Select All</button>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <label for="inputEmail3" class="col-3 col-form-label">Strand /Skill /Setting</label>
                        <div class="col-9">
                            <select class="form-control" data-control="select2" multiple
                                data-dropdown-parent="#modalAssignSS"
                                data-placeholder="{{ __('app/administration/academic_settings.select_type') }}"
                                id="modalSSlist">
                                {{-- <option> --}}
                                    @foreach ($skills_strands as $key => $element)
                                        <option value="{{ $element->skill_strand_id }}">
                                            @if($element->is_final_mid_exam == 1)
                                            setting -
                                            {{ $element->value }}
                                            @else
                                            {{ $element->type }} -
                                            {{ $element->value }}
                                            @endif
                                        </option>
                                    @endforeach
                                {{-- </option> --}}
                            </select>
                        </div>

                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal"> {{ __('general.close') }}
                    </button>
                    <button type="button" class="btn btn-primary" id="btnModalAssignSS">
                        Assign </button>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" tabindex="-1" id="modalAcademicSemester">
        <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">{{ __('app/administration/academic_settings.academic_semester') }}</h5>
                    <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal"
                        aria-label="Close">
                        <i class="fa fa-times"></i>
                    </div>
                </div>
                <div class="modal-body">

                    <div class="row mb-3">
                        <label for="inputEmail3" class="col-3 col-form-label">Term</label>
                        <div class="col-9">
                            <select class="form-control " data-control="select2"
                                data-dropdown-parent="#modalAcademicSemester" id="termAcademicYearSemester">
                                <option value="1">Term 1</option>
                                <option value="2">Term 2</option>
                            </select>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <label for="inputEmail3"
                            class="col-3 col-form-label">{{ __('app/administration/academic_settings.start_date') }}</label>
                        <div class="col-9">
                            <input type="date" class="form-control required" name="start_date" id="aSStartDate"
                                placeholder="Choose Start Date">
                        </div>
                    </div>
                    <div class="row mb-3">
                        <label for="inputEmail3"
                            class="col-3 col-form-label">{{ __('app/administration/academic_settings.end_date') }}</label>
                        <div class="col-9">
                            <input type="date" class="form-control required" name="end_date" id="aSEndDate"
                                placeholder="Choose End Date">
                        </div>
                    </div>
                    <div class="row mb-3">
                        <label for="inputEmail3" class="col-3 col-form-label">Status</label>
                        <div class="col-9">
                            <select class="form-control " data-control="select2"
                                data-dropdown-parent="#modalAcademicSemester" id="aSStatus">
                                <option value="0">Inactive</option>
                                <option value="1">Active</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal"> {{ __('general.close') }}
                    </button>
                    <button type="button" class="btn btn-primary" id="btnCreateAcademicSemester">
                        Create </button>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" tabindex="-1" id="modalAcademicSemesterEdit">
        <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">{{ __('app/administration/academic_settings.academic_semester') }}</h5>
                    <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal"
                        aria-label="Close">
                        <i class="fa fa-times"></i>
                    </div>
                </div>
                <div class="modal-body" id="academicSemesterEditArea">

                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal"> {{ __('general.close') }}
                    </button>
                    <button type="button" class="btn btn-primary" id="btnEditAcademicSemester">
                        Edit </button>
                </div>
            </div>
        </div>
    </div>

    <!-- learning areas modals -->
    <div class="modal fade" tabindex="-1" id="modalEditLearningArea">
        <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">{{ __('Learning Area') }}</h5>
                    <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal"
                        aria-label="Close">
                        <i class="fa fa-times"></i>
                    </div>
                </div>

                <div class="modal-body" id="updateLearningArea">

                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal"> {{ __('general.close') }}</button>
                    <button type="button" class="btn btn-primary" id="btnUpdateLearningArea">
                        {{ __('general.save') }}</button>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" tabindex="-1" id="modalAddLearningArea">
        <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">{{ __('Learning Area') }}</h5>
                    <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal"
                        aria-label="Close">
                        <i class="fa fa-times"></i>
                    </div>
                </div>

                <div class="modal-body" id="modalLearningArea">

                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal"> {{ __('general.close') }}</button>
                    <button type="button" class="btn btn-primary" id="btnSaveLearningArea">
                        {{ __('general.save') }}</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal for selecting previous year learning areas -->
    <div class="modal fade" id="importPreviousYearModal" tabindex="-1" role="dialog" aria-labelledby="importModalLabel">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="importModalLabel">Import Learning Areas from Previous Years</h5>
                    <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal"
                        aria-label="Close">
                        <i class="fa fa-times"></i>
                    </div>
                </div>
                <div class="modal-body" id="importPreviousYearModalBody">

                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal"> {{ __('general.close') }}</button>
                    <button type="button" class="btn btn-primary" id="import_selected_btn" disabled>Import Selected</button>
                </div>
            </div>
        </div>
    </div>

@endsection