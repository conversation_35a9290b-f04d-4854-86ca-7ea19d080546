@extends('layouts.app')
@section('content')
@php
use App\Library\Helper\AcademicHelper;
use App\Models\UserRole;
use App\Models\DetentionTypeSetting;
// $userId = \Auth::user()->id;
// $user_role = UserRole::where('user_id',$userId)->first();

$ah = new AcademicHelper();
$branchId = $ah->currentBranch();
$classes = $ah->branchClassrooms();
$students = $ah->branchStudents($branchId);
// $semester = $ah->currentSemester();
$detention_types = DetentionTypeSetting::all();
$isVicePrincipal = 'false';
$vicePrincipalIds = [52,69,70,71,72];
$userRoles = UserRole::where('user_id',Auth::user()->id)->get();
$userRoleIds = collect($userRoles)->pluck('role_id')->toArray();
$matchingRoles = collect($vicePrincipalIds)->intersect($userRoleIds);
if(count($matchingRoles) > 0){
$isVicePrincipal = 'true';
}
@endphp
<!--start:::Main-->
<div class="app-main flex-column flex-row-fluid" id="kt_app_main" data-role="{{$isVicePrincipal}}">
    <div class="d-flex flex-column flex-column-fluid">
        <div id="kt_app_toolbar" class="app-toolbar py-3 py-lg-6">
            <div id="kt_app_toolbar_container" class="app-container container-xxl d-flex flex-stack">
                <div class="page-title d-flex flex-column justify-content-center flex-wrap me-3">
                    <h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">
                        {{ __('app/discipline/bps_report.bps_reports') }}
                    </h1>
                    <ul class="breadcrumb breadcrumb-separatorless fw-semibold fs-7 my-0 pt-1">
                        <li class="breadcrumb-item text-muted">
                            <a href="/app/home" class="text-muted text-hover-primary">{{ __('general.home') }}</a>
                        </li>
                        <li class="breadcrumb-item">
                            <span class="bullet bg-gray-400 w-5px h-2px"></span>
                        <li class="breadcrumb-item text-muted">{{ __('general.behaviour') }}</li>
                        <li class="breadcrumb-item">
                            <span class="bullet bg-gray-400 w-5px h-2px"></span>
                        <li class="breadcrumb-item text-muted">{{ __('app/discipline/bps_report.bps_reports') }}</li>
                    </ul>
                </div>
                <div class="d-flex align-items-center gap-2 gap-lg-3">
                </div>
            </div>
        </div>

        <div id="kt_app_content" class="app-content flex-column-fluid">
            <div id="kt_app_content_container" class="app-container container-fluid">
                <div class="card card-flush h-md-100"
                    style="border-top-right-radius: 0px; border-bottom-right-radius: 0px;">
                    <div class="card-body" style="padding: 15px;">
                        <div class="row">
                            {{-- Filter Classes --}}
                            <div class="row my-4">
                                <div class="col-6 col-md-6 col-lg-3 col-xl-2 mt-2">
                                    <select class="form-control" data-control="select2" multiple
                                        data-placeholder="Select Classroom" id="classList">
                                        <option>
                                            @foreach ($classes as $key => $class)
                                        <option value="{{ $class->classroom_id }}">
                                            [{{ $class->classroom_name }}]</option>
                                        @endforeach
                                        </option>
                                    </select>
                                </div>
                                <div class="col-6 col-md-6 col-lg-3 col-xl-2 mt-2">
                                    <select class="form-control" data-control="select2" id="student">
                                        <option value="" disabled selected>Select Student</option>
                                        <option value="">All Student</option>
                                        @foreach ($students as $student)
                                        <option value="{{$student->id}}">{{ $student->id }} {{ $student->name }}
                                            [{{ $student->classroom_name }}]</option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="col-12 col-md-3 col-lg-2 col-xl-2 mt-2">
                                    <select class="form-control" data-control="select2" id="dateType" required>
                                        <option selected disabled> Select Date Type</option>
                                        <option value="over-all"> Over All</option>
                                        <option value="week"> This Week</option>
                                        <option value="month">This Month</option>
                                        <option value="term"> This Term</option>
                                        <option value="custom"> Custom</option>
                                    </select>
                                </div>
                                <div class="col-12 col-md-3 col-lg-2 col-xl-2 mt-2" id="divDateRange"
                                    style="display:none">
                                    <input type="date"
                                        placeholder="{{ __('app/reports/student_attendance.pick_date') }}"
                                        class="form-control dateRange" id="dateRange" />
                                </div>
                                <div class="col-12 col-md-2 col-xl-2 mt-2">
                                    <select class="form-control" data-control="select2" id="detentionTypeSearch">
                                        <option value="" disabled selected>Select Detention Type</option>
                                        <option value="">All Detention Type</option>
                                        @foreach ($detention_types as $detention_type)
                                        <option value="{{$detention_type->detention_type_setting_id}}">{{$detention_type->detention_type_setting_label}}</option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="col  mt-2">
                                    <button type="button" class="btn btn-primary ml-10" id="btnSearchBps">
                                        {{ __('general.search') }}
                                    </button>
                                </div>
                            </div>
                            <div class="col-lg-12 col-md-12 col-sm-12">
                                @include('partial_view.app.discipline.detention_list')
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!--end:::Main-->
<!--start:::Modal-->
<div class="modal fade" tabindex="-1" id="modalBPS">
    <div class="modal-dialog modal-xl modal-dialog-centered modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"> {{ __('app/discipline/bps_records.bps_info') }} </h5>
                <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal"
                    aria-label="Close">
                    <i class="fa fa-times"></i>
                </div>
            </div>

            <div class="modal-body" id="bpsArea">

            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-light" data-bs-dismiss="modal"> {{ __('general.close') }} </button>
                <button type="button" class="btn btn-primary" id="btnSaveDetention"> {{ __('general.save') }} </button>
            </div>
        </div>
    </div>
</div>
<!--end:::Modal-->


<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.1/jquery.min.js"
    integrity="sha512-aVKKRRi/Q/YV+4mjoKBsE4x3H+BkegoM/em46NNlCqNTmUYADjBbeNefNxYV7giUp0VxICtqdrbqU7iVaeZNXA=="
    crossorigin="anonymous" referrerpolicy="no-referrer"></script>
<script src="https://cdn.sheetjs.com/xlsx-latest/package/dist/xlsx.full.min.js"></script>
<script src="https://unpkg.com/file-saver@2.0.0-rc.2/dist/FileSaver.min.js"
    integrity="sha512-csNcFYJniKjJxRWRV1R7fvnXrycHP6qDR21mgz1ZP55xY5d+aHLfo9/FcGDQLfn2IfngbAHd8LdfsagcCqgTcQ=="
    crossorigin="anonymous" referrerpolicy="no-referrer"></script>
<script src="https://unpkg.com/tableexport@5.2.0/dist/js/tableexport.min.js"></script>
<script src="/library/js/app/discipline/detention_report.js"></script>


@endsection
