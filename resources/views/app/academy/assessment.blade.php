@extends('layouts.app')
@section('content')
    <?php
    use App\Library\Helper\AcademicHelper;
    use App\Models\Classroom;
    use App\Models\ElectiveGrade;
    use App\Models\FormativeAssessment;
    use App\Models\SummativeAssessment;
    use App\Models\AcademicSemester;
    
    $ah = new AcademicHelper();
    $branchId = $ah->currentBranch();
    $classes = $ah->branchClassrooms();
    $students = $ah->branchStudents($branchId);
    $currentSemester = AcademicSemester::where('academic_semester', $ah->currentSemester())->where('branch_id', $branchId)->where('academic_year_id', $ah->academicYear())->first();
    // $summativeAssessments = SummativeAssessment::where('academic_year_id',$ah->academicYear())
    // ->where('branch_id',$branchId)
    // ->where('date','<',$currentSemester->start_date)
    // ->update(['is_locked' => 1]);
    // $formativeAssessments = FormativeAssessment::where('academic_year_id',$ah->academicYear())
    // ->where('branch_id',$branchId)
    // ->where('date','<',$currentSemester->start_date)
    // ->update(['is_locked' => 1]);
    
    // echo $currentSemester;
    $teacherClassrooms = $ah->teacherTCLClassrooms(Auth::user()->id);
    $classes = Classroom::whereIn('classroom_id', $teacherClassrooms->pluck('class_id'))->get();
    $timetable = $ah->userTimetable(\Auth::user()->id);
    
    $array = $ah->userTimetable(\Auth::user()->id);
    
    ?>
    {{-- <div>{{$array}}</div> --}}
    <div class="app-main flex-column flex-row-fluid" id="kt_app_main">
        <div class="d-flex flex-column flex-column-fluid">
            <div id="kt_app_toolbar" class="app-toolbar py-3 py-lg-6">
                <div id="kt_app_toolbar_container" class="app-container container-xxl d-flex flex-stack">
                    <div class="page-title d-flex flex-column justify-content-center flex-wrap me-3">
                        <h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">
                            {{ __('app/academy/assessment.assessments') }} </h1>
                        <ul class="breadcrumb breadcrumb-separatorless fw-semibold fs-7 my-0 pt-1">
                            <li class="breadcrumb-item text-muted">
                                <a href="/app/home" class="text-muted text-hover-primary"> {{ __('general.home') }} </a>
                            </li>

                            <li class="breadcrumb-item">
                                <span class="bullet bg-gray-400 w-5px h-2px"></span>
                            <li class="breadcrumb-item text-muted">{{ __('general.academy') }}</li>
                            <li class="breadcrumb-item">
                                <span class="bullet bg-gray-400 w-5px h-2px"></span>
                            <li class="breadcrumb-item text-muted"> {{ __('app/academy/assessment.assessments') }} </li>
                        </ul>
                    </div>
                    <div class="d-flex align-items-center gap-2 gap-lg-3">
                        <a href="#" class="btn btn-sm fw-bold btn-dark" id="btnNewAssessment"><i
                                class="fas fa-plus"></i> {{ __('app/academy/assessment.assessment') }}</a>
                    </div>
                </div>
            </div>


            <div id="kt_app_content" class="app-content flex-column-fluid">
                <div id="kt_app_content_container" class="app-container container-fluid">
                    <div class="card card-flush h-md-100"
                        style="border-top-right-radius: 0px; border-bottom-right-radius: 0px;">
                        <div class="card-body" style="padding: 15px;">
                            {{-- --}}
                            <div class="row">
                                {{-- @foreach ($classes as $classroom) --}}
                                @foreach ($array as $session)
                                    <div class="col-lg-3 col-xl-3 col-sm-6 col-xs-12 mb-4">
                                        <a class="nav-link w-100 btn btn-flex btn-secondary  subjectRow"
                                            data-id="{{ $session->grade_id }}"
                                            href="#tab-classroom-{{ $session->grade_id }}">
                                            <span class="svg-icon svg-icon-2hx">
                                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                                                    xmlns="http://www.w3.org/2000/svg">
                                                    <path
                                                        d="M16.0173 9H15.3945C14.2833 9 13.263 9.61425 12.7431 10.5963L12.154 11.7091C12.0645 11.8781 12.1072 12.0868 12.2559 12.2071L12.6402 12.5183C13.2631 13.0225 13.7556 13.6691 14.0764 14.4035L14.2321 14.7601C14.2957 14.9058 14.4396 15 14.5987 15H18.6747C19.7297 15 20.4057 13.8774 19.912 12.945L18.6686 10.5963C18.1487 9.61425 17.1285 9 16.0173 9Z"
                                                        fill="currentColor" />
                                                    <rect opacity="0.3" x="14" y="4" width="4" height="4"
                                                        rx="2" fill="currentColor" />
                                                    <path
                                                        d="M4.65486 14.8559C5.40389 13.1224 7.11161 12 9 12C10.8884 12 12.5961 13.1224 13.3451 14.8559L14.793 18.2067C15.3636 19.5271 14.3955 21 12.9571 21H5.04292C3.60453 21 2.63644 19.5271 3.20698 18.2067L4.65486 14.8559Z"
                                                        fill="currentColor" />
                                                    <rect opacity="0.3" x="6" y="5" width="6" height="6"
                                                        rx="3" fill="currentColor" />
                                                </svg>
                                            </span>
                                            <span class="d-flex flex-column align-items-start">
                                                <span
                                                    class="fs-4 fw-bold">{{ $session->elective_grade->grade_name ?? '[Deleted]' }}</span>
                                                <span class="fs-7">{{ $session->subject->subject_name }}</span>
                                            </span>
                                        </a>
                                    </div>
                                @endforeach

                                {{-- <div class="col-lg-3 col-xl-3 col-sm-6 col-xs-12">
                                <a href="#" class="btn btn-secondary btnHomeroom w-100" data-view="report_card"
                                    id="btnReportCard">
                                    <div class="symbol symbol-55px mb-5 buttonSymbol">
                                        <i style="font-size: 25px; padding-top: 10px;"
                                            class="fa-solid fa-file-invoice"></i>
                                    </div>
                                    <div class="fs-5 fw-bold mb-2"><i class="fa-solid fa-file-invoice buttonSymbolSmall"
                                            style="display: none;"></i> {{ __('app/academy/homeroom.report_card') }}
                                    </div>
                                </a>
                            </div> --}}
                            </div>


                            {{-- --}}
                            <ul class="nav nav-tabs nav-line-tabs mb-5 fs-6">
                                <li class="nav-item">
                                    <a class="nav-link active" id="menu_tab_summative" data-bs-toggle="tab"
                                        href="#tab_summative">{{ __('app/academy/assessment.summative') }}</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" id="menu_tab_formative" data-bs-toggle="tab"
                                        href="#tab_formative">{{ __('app/academy/assessment.formative') }}</a>
                                </li>
                            </ul>

                            <div class="tab-content" id="myTabContent">
                                <div class="tab-pane fade show active" id="tab_summative" role="tabpanel">
                                    <span id="welcomeBox" style="width: 100%;">
                                        <br />
                                        <p class="text-center">Please choose Subject from the list!</p>
                                    </span>
                                    {{-- @include('partial_view.app.academy.assessment.summative') --}}
                                </div>
                                <div class="tab-pane fade" id="tab_formative" role="tabpanel">
                                    <span id="welcomeBox" style="width: 100%;">
                                        <br />
                                        <p class="text-center">Please choose Subject from the list!</p>
                                    </span>
                                    {{-- @include('partial_view.app.academy.assessment.formative') --}}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!--end:::Main-->


    <div class="modal fade" tabindex="-1" id="modalNewAssessment">
        <div class="modal-dialog modal-xl modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">{{ __('app/academy/assessment.new_assessment') }}</h5>
                    <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal"
                        aria-label="Close">
                        <span class="svg-icon svg-icon-2x"><i class="fa-solid fa-xmark"></i></span>
                    </div>
                </div>

                <div class="modal-body">
                    @include('partial_view.app.academy.assessment.add')
                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal"> {{ __('general.close') }}
                    </button>
                    <button type="button" class="btn btn-primary" id="btnSubmitNewAssessment">
                        {{ __('app/academy/assessment.create_assessment') }} </button>
                </div>
            </div>
        </div>
    </div>

    {{-- Summative Assessment Edit --}}
    <div class="modal fade" tabindex="-1" id="modalSummativeEdit">
        <div class="modal-dialog modal-xl modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">{{ __('app/academy/assessment.edit_assessment') }}</h5>
                    <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal"
                        aria-label="Close">
                        <span class="svg-icon svg-icon-2x"><i class="fa-solid fa-xmark"></i></span>
                    </div>
                </div>

                <div class="modal-body" id="summativeEditArea">
                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal"> {{ __('general.close') }}
                    </button>
                    <button type="button" class="btn btn-primary" id="btnSaveSummativeName">
                        {{ __('app/academy/assessment.edit_assessment') }} </button>
                </div>
            </div>
        </div>
    </div>
    {{-- Formative Assessment Edit --}}
    <div class="modal fade" tabindex="-1" id="modalFormativeEdit">
        <div class="modal-dialog modal-xl modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">{{ __('app/academy/assessment.edit_assessment') }}</h5>
                    <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal"
                        aria-label="Close">
                        <span class="svg-icon svg-icon-2x"><i class="fa-solid fa-xmark"></i></span>
                    </div>
                </div>

                <div class="modal-body" id="formativeEditArea">
                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal"> {{ __('general.close') }}
                    </button>
                    <button type="button" class="btn btn-primary" id="btnSaveFormativeName">
                        {{ __('app/academy/assessment.edit_assessment') }} </button>
                </div>
            </div>
        </div>
    </div>
    {{-- Assessment Detail --}}
    <div class="modal fade" tabindex="-1" id="modalAssessmentDetail">
        <div class="modal-dialog modal-xl modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">{{ __('app/academy/assessment.assessment') }}</h5>
                    <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal"
                        aria-label="Close">
                        <span class="svg-icon svg-icon-2x"><i class="fa-solid fa-xmark"></i></span>
                    </div>
                </div>

                <div class="modal-body" id="assessmentDetailArea">
                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal"> {{ __('general.close') }}
                    </button>
                </div>
            </div>
        </div>
    </div>


    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.1/jquery.min.js"
        integrity="sha512-aVKKRRi/Q/YV+4mjoKBsE4x3H+BkegoM/em46NNlCqNTmUYADjBbeNefNxYV7giUp0VxICtqdrbqU7iVaeZNXA=="
        crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script src="/library/js/app/academy/assessment.js?t={{ time() }}"></script>
@endsection
