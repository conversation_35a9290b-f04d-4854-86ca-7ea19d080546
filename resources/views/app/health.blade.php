@extends('layouts.app')
@section('content')

    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.1/jquery.min.js" integrity="sha512-aVKKRRi/Q/YV+4mjoKBsE4x3H+BkegoM/em46NNlCqNTmUYADjBbeNefNxYV7giUp0VxICtqdrbqU7iVaeZNXA==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script src="/library/js/app/health.js?v={{time()}}"></script>

    <div class="app-main flex-column flex-row-fluid" id="kt_app_main">
        <div class="d-flex flex-column flex-column-fluid">
            <div id="kt_app_toolbar" class="app-toolbar py-3 py-lg-6">
                <div id="kt_app_toolbar_container" class="app-container container-xxl d-flex flex-stack">
                    <div class="page-title d-flex flex-column justify-content-center flex-wrap me-3">
                        <h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">
                            {{ __('app/health.health_manager') }} </h1>
                        <ul class="breadcrumb breadcrumb-separatorless fw-semibold fs-7 my-0 pt-1">
                            <li class="breadcrumb-item text-muted">
                                <a href="/app/home" class="text-muted text-hover-primary"> {{ __('general.home') }} </a>
                            </li>
                            <li class="breadcrumb-item">
                                <span class="bullet bg-gray-400 w-5px h-2px"></span>
                            <li class="breadcrumb-item text-muted"> {{ __('app/health.health_manager') }} </li>
                        </ul>
                    </div>
                    <div class="d-flex align-items-center gap-2 gap-lg-3">
                    </div>
                </div>
            </div>

            <div id="kt_app_content" class="app-content flex-column-fluid">
                <div id="kt_app_content_container" class="app-container container-fluid">

                    <div class="row">
                        <div class="col-12">
                            <div class="card card-flush ">
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-12">
                                            <div class="row">
                                                <div class="col-lg-3 col-sm-6 col-xs-12">
                                                    <a href="#" class="btn btn-secondary w-100" id="btnStudentInfo" data-bs-toggle="modal" data-bs-target="#modalStudents">
                                                        <div class="symbol symbol-55px mb-5">
                                                            <i style="font-size: 25px; padding-top: 10px;" class="fa-solid fa-user-graduate"></i>
                                                        </div>
                                                        <div class="fs-5 fw-bold mb-2">{{ __('app/health.students') }}</div>
                                                    </a>
                                                </div>

                                                <div class="col-lg-3 col-sm-6 col-xs-12">
                                                    <a href="#" class="btn btn-secondary w-100" id="btnIssueBook" data-bs-toggle="modal" data-bs-target="#modalStaff">
                                                        <div class="symbol symbol-55px mb-5">
                                                            <i style="font-size: 25px; padding-top: 10px;" class="fa-solid fa-users"></i>
                                                        </div>
                                                        <div class="fs-5 fw-bold mb-2">{{ __('app/health.staff') }}</div>
                                                    </a>
                                                </div>

                                                <div class="col-lg-3 col-sm-6 col-xs-12">
                                                    <a href="#" id="btnSearch"  class="btn btn-secondary w-100" data-bs-toggle="modal" data-bs-target="#modalGuest">
                                                        <div class="symbol symbol-55px mb-5">
                                                            <i style="font-size: 25px; padding-top: 10px;" class="fa-regular fa-user"></i>
                                                        </div>
                                                        <div class="fs-5 fw-bold mb-2">{{ __('app/health.guests') }}</div>
                                                    </a>
                                                </div>

                                                <div class="col-lg-3 col-sm-6 col-xs-12">
                                                    <a href="#" id="btnSearch"  class="btn btn-secondary w-100" data-bs-toggle="modal" data-bs-target="#modalReports">
                                                        <div class="symbol symbol-55px mb-5">
                                                            <i style="font-size: 25px; padding-top: 10px;" class="fa-regular fa-user"></i>
                                                        </div>
                                                        <div class="fs-5 fw-bold mb-2">{{ __('app/health.reports') }}</div>
                                                    </a>
                                                </div>

                                                <!-- <div class="col-lg-3 col-sm-6 col-xs-12">
                                                <a href="#" id="btnReports" style="opacity: 0.3; pointer-events: none;" class="btn btn-secondary w-100">
                                                    <div class="symbol symbol-55px mb-5">
                                                        <i style="font-size: 25px; padding-top: 10px;" class="fa-solid  fa-chart-bar"></i>
                                                    </div>
                                                    <div class="fs-5 fw-bold mb-2">{{ __('app/health.reports') }}</div>
                                                </a>
                                            </div> -->
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-12">
                            <div class="card card-flush ">
                                <div class="card-header">
                                    <h3 class="card-title">{{ __('app/health.health_records') }}</h3>
                                </div>
                                <div class="card-body">
                                    <ul class="nav nav-tabs nav-line-tabs mb-5 fs-6">
                                        <li class="nav-item">
                                            <a class="nav-link active" data-bs-toggle="tab" href="#tabHealthStudent">{{ __('app/health.students') }}</a>
                                        </li>
                                        <li class="nav-item">
                                            <a class="nav-link" data-bs-toggle="tab" href="#tabHealthStaff">{{ __('app/health.staff') }}</a>
                                        </li>
                                        <li class="nav-item">
                                            <a class="nav-link" data-bs-toggle="tab" href="#tabHealthGuest">{{ __('app/health.guests') }}</a>
                                        </li>
                                    </ul>

                                    <div class="tab-content" id="myTabContent">
                                        <!-- tabHealthStudent÷ -->
                                        <div class="tab-pane fade show active" id="tabHealthStudent" role="tabpanel">
                                            <table class="table table-striped border gs-5" id="tableStudents">
                                                <thead>
                                                <th> {{ __('app/health.id') }} </th>
                                                <th> {{ __('app/health.student') }} </th>
                                                <th> {{ __('app/health.reason') }} </th>
                                                <th> {{ __('app/health.action') }} </th>
                                                <th> {{ __('app/health.date') }} </th>
                                                </thead>
                                                <tbody>
                                                @foreach ($studentRecords as $record)
                                                    <tr style="cursor: pointer;"  class="healthRow" data-type="student" data-id="{{$record->health_record_id}}">
                                                        <td>{{$record->student_id}}</td>
                                                        <td>{{$record->student->name ?? ''}}</td>
                                                        <td>{{$record->reason}}</td>
                                                        <td>{{$record->action}}</td>
                                                        <td>{{$record->date}}</td>
                                                    </tr>
                                                @endforeach
                                                </tbody>
                                            </table>
                                        </div>
                                        <!-- END tabHealthStudent÷ -->

                                        <!-- tabHealthStaff -->
                                        <div class="tab-pane fade" id="tabHealthStaff" role="tabpanel">
                                            <table class="table table-striped border gs-5" id="tableStaff">
                                                <thead>
                                                <th> {{ __('app/health.id') }} </th>
                                                <th> {{ __('app/health.staff') }} </th>
                                                <th> {{ __('app/health.reason') }} </th>
                                                <th> {{ __('app/health.action') }} </th>
                                                <th> {{ __('app/health.date') }} </th>
                                                </thead>
                                                <tbody>
                                                @foreach ($staffRecords as $record)
                                                    <tr style="cursor: pointer;" class="healthRow" data-type="staff" data-id="{{$record->record_id}}">
                                                        <td>{{$record->user_id}}</td>
                                                        <td>{{$record->user->name ?? ''}}</td>
                                                        <td>{{$record->reason}}</td>
                                                        <td>{{$record->action}}</td>
                                                        <td>{{$record->date}}</td>
                                                    </tr>
                                                @endforeach
                                                </tbody>
                                            </table>
                                        </div>
                                        <!-- END tabHealthStaff -->

                                        <!-- tabHealthGuest -->
                                        <div class="tab-pane fade" id="tabHealthGuest" role="tabpanel">
                                            <table class="table table-striped border gs-5" id="tableGuest">
                                                <thead>
                                                <th> {{ __('app/health.guest') }} </th>
                                                <th> {{ __('app/health.reason') }} </th>
                                                <th> {{ __('app/health.action') }} </th>
                                                <th> {{ __('app/health.date') }} </th>
                                                </thead>
                                                <tbody>
                                                @foreach ($guestRecords as $record)
                                                    <tr style="cursor: pointer;" class="healthRow" data-type="guest" data-id="{{$record->record_id}}">
                                                        <td>{{$record->full_name}}</td>
                                                        <td>{{$record->reason}}</td>
                                                        <td>{{$record->action}}</td>
                                                        <td>{{$record->date}}</td>
                                                    </tr>
                                                @endforeach
                                                </tbody>
                                            </table>
                                        </div>
                                        <!-- END tabHealthGuest -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!--end:::Main-->

    <!-- modalStudents -->
    <div class="modal bg-white fade" tabindex="-1" id="modalStudents">
        <div class="modal-dialog modal-fullscreen modal-dialog-scrollable">
            <div class="modal-content  shadow-none">
                <div class="modal-header">
                    <h5 class="modal-title">{{ __('app/health.students_health_info') }}</h5>
                    <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal" aria-label="Close">
                        <i class="fa fa-times"></i>
                    </div>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-lg-4 col-xl-4 col-md-4 col-sm-12">
                            <table class="table table-striped border gs-5" id="tableInfoStudents">
                                <thead>
                                <th> {{ __('app/health.id') }} </th>
                                <th> {{ __('app/health.student') }} </th>
                                <th> {{ __('app/health.grade') }} </th>
                                </thead>
                                <tbody>
                                @foreach ($students as $student)
                                    <tr style="cursor: pointer;" class="studentInfoRow" data-id="{{$student->id}}">
                                        <td>{{$student->id}}</td>
                                        <td>{{$student->name}}</td>
                                        <td>{{$student->classroom_name}}</td>
                                    </tr>
                                @endforeach
                                </tbody>
                            </table>
                        </div>

                        <div class="col-lg-8 col-xl-8 col-md-8 col-sm-12" id="studentDetailArea">
                            <p class="text-center">{{ __('app/health.select_student') }}</p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">{{ __('general.close') }}</button>
                    <button type="button" class="btn btn-primary" style="display: none;" id="btnSubmitHealthInfo"><i class="fas fa-save"></i> {{ __('general.save') }}</button>
                    <button type="button" class="btn btn-primary" style="display: none;" id="btnAßddRecord"><i class="fas fa-plus"></i> {{ __('app/health.add_record') }}</button>
                </div>
            </div>
        </div>
    </div>
    <!-- end modalStudents -->

    <!-- modalStaff -->
    <div class="modal bg-white fade" tabindex="-1" id="modalStaff">
        <div class="modal-dialog modal-fullscreen modal-dialog-scrollable">
            <div class="modal-content  shadow-none">
                <div class="modal-header">
                    <h5 class="modal-title">{{ __('app/health.staff_health_info') }}</h5>
                    <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal" aria-label="Close">
                        <i class="fa fa-times"></i>
                    </div>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-lg-4 col-xl-4 col-md-4 col-sm-12">
                            <table class="table table-striped border gs-5" id="tableInfoStaff">
                                <thead>
                                <th> {{ __('app/health.id') }} </th>
                                <th> {{ __('app/health.staff') }} </th>
                                <th> {{ __('app/health.position') }} </th>
                                <!-- <th> {{ __('app/health.username') }} </th> -->
                                </thead>
                                <tbody>
                                @foreach ($staff as $stf)
                                    <tr style="cursor: pointer;" class="staffInfoRow" data-id="{{$stf->id}}">
                                        <td>{{$stf->id}}</td>
                                        <td>{{$stf->name}}</td>
                                        <td>{{$stf->details->profession_position}}</td>
                                        <!-- <td>{{$stf->username}}</td> -->
                                    </tr>
                                @endforeach
                                </tbody>
                            </table>
                        </div>

                        <div class="col-lg-8 col-xl-8 col-md-8 col-sm-12" id="staffDetailArea">
                            <p class="text-center">{{ __('app/health.select_staff') }}</p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-light"
                            data-bs-dismiss="modal">{{ __('general.close') }}</button>
                    <button type="button" class="btn btn-primary" style="display: none;" id="btnSubmitHealthInfo"><i
                                class="fas fa-save"></i> {{ __('general.save') }} </button>
                    <button type="button" class="btn btn-primary" style="display: none;" id="btnSubmitStaffHealthInfo"><i
                                class="fas fa-save"></i> {{ __('general.save') }} </button>
                    <button type="button" class="btn btn-primary" style="display: none;" id="btnAddRecord"><i
                                class="fas fa-plus"></i> {{ __('app/health.add_record') }} </button>
                </div>
            </div>
        </div>
    </div>
    <!-- end modalStaff -->

    <!-- modalGuest -->
    <div class="modal bg-white fade" tabindex="-1" id="modalGuest">
        <div class="modal-dialog modal-fullscreen modal-dialog-scrollable">
            <div class="modal-content  shadow-none">
                <div class="modal-header">
                    <h5 class="modal-title">{{ __('app/health.guests_health_info') }}</h5>
                    <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal" aria-label="Close">
                        <i class="fa fa-times"></i>
                    </div>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-lg-4 col-xl-4 col-md-4 col-sm-12">
                            <h3>{{ __('app/health.add_record') }}</h3>
                            <hr/><br/>
                            <form id="formNewGuestRecord">
                                <div class="row mb-3">
                                    <label for="exampleInputuname_3" class="col-sm-3 control-label">{{ __('app/health.name') }}</label>
                                    <div class="col-9">
                                        <input type="text" class="form-control" name="name" />
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <label for="exampleInputuname_3" class="col-sm-3 control-label">{{ __('app/health.admission_date') }}</label>
                                    <div class="col-9">
                                        <input type="date" placeholder="{{ __('app/health.admission_date') }}" class="form-control" name="date" />
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <label for="admission_time" class="col-sm-3 control-label">{{ __('app/health.admission_time') }}</label>
                                    <div class="col-9">
                                        <input type="time" id="admission_time" placeholder="{{ __('app/health.admission_time') }}" class="form-control" name="time" autocomplete="off" />
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <label for="discharge_time" class="col-sm-3 control-label">{{ __('app/health.discharge_time') }}</label>
                                    <div class="col-9">
                                        <input type="time" id="discharge_time" placeholder="{{ __('app/health.discharge_time') }}" class="form-control" name="discharge_time" autocomplete="off"  />
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <label for="exampleInputuname_3" class="col-sm-3 control-label">{{ __('app/health.reason') }}</label>
                                    <div class="col-9">
                                        <select class="form-control" name="reason[]" multiple data-control="select2" data-placeholder="{{ __('app/health.pick_reason') }}" data-dropdown-parent="#modalGuest">
                                            @foreach ($injuries as $injury)
                                                <option value="{{$injury->item}}">{{trans($injury->item)}}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <label for="exampleInputuname_3" class="col-sm-3 control-label">{{ __('app/health.action') }}</label>
                                    <div class="col-9">
                                        <select class="form-control" name="action[]" multiple data-control="select2" data-placeholder="{{ __('app/health.actions_taken') }}" data-dropdown-parent="#modalGuest">
                                            @foreach ($actions as $action)
                                                <option value="{{$action->item}}">{{trans($action->item)}}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <label for="exampleInputuname_3" class="col-sm-3 control-label">{{ __('app/health.temperature') }}</label>
                                    <div class="col-9">
                                        <input type="text" placeholder="{{ __('app/health.temperature') }}" class="form-control" name="temperature" />
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <label for="exampleInputuname_3" class="col-sm-3 control-label">{{ __('app/health.blood_pressure') }}</label>
                                    <div class="col-9">
                                        <input type="text" placeholder="{{ __('app/health.blood_pressure') }}" class="form-control" name="blood_pressure" />
                                    </div>
                                </div>


                                <div class="row mb-3">
                                    <label for="exampleInputuname_3" class="col-sm-3 control-label">{{ __('app/health.saturation') }}</label>
                                    <div class="col-9">
                                        <input type="text" placeholder="{{ __('app/health.saturation') }}" class="form-control" name="saturation" />
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <label for="exampleInputuname_3" class="col-sm-3 control-label">{{ __('app/health.pulse') }}</label>
                                    <div class="col-9">
                                        <input type="text" placeholder="{{ __('app/health.pulse') }}" class="form-control" name="pulse" />
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <label for="exampleInputuname_3" class="col-sm-3 control-label">{{ __('app/health.medication') }}</label>
                                    <div class="col-9">
                                        <input type="text" placeholder="{{ __('app/health.medication') }}" class="form-control" name="medication" />
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <label for="exampleInputuname_3" class="col-sm-3 control-label">{{ __('app/health.comments') }}</label>
                                    <div class="col-9">
                                        <input type="text" placeholder="{{ __('app/health.comments') }}" class="form-control" name="comments" />
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-6"></div>
                                    <div class="col-6">
                                        <a class="btn btn-dark w-100" id="btnSubmitGuestRecord"><i class="fas fa-save"></i> {{ __('app/health.add_record') }}</a>
                                    </div>
                                </div>




                            </form>
                        </div>

                        <div class="col-lg-8 col-xl-8 col-md-8 col-sm-12">
                            <table class="table table-striped border gs-5" id="tableInfoGuest">
                                <thead>
                                <th> {{ __('app/health.name') }} </th>
                                <th> {{ __('app/health.date') }} </th>
                                <th> {{ __('app/health.time') }} </th>
                                <th> {{ __('app/health.reason') }} </th>
                                <th> {{ __('app/health.action') }} </th>
                                <th> {{ __('app/health.medication') }} </th>
                                <th> {{ __('app/health.vitals') }} </th>
                                <th> {{ __('app/health.comments') }} </th>
                                <th></th>
                                </thead>
                                <tbody>
                                @foreach ($guestRecords as $record)
                                    <tr>
                                        <td>{{$record->full_name}}</td>
                                        <td>{{$record->date}}</td>
                                        <td>
                                            <span class="badge badge-pill badge-success">{{ $record->time ? $record->time : '' }}</span>
                                            <span class="badge badge-pill badge-primary">{{ $record->discharge_time ? $record->discharge_time : '' }}</span>
                                        </td>
                                        <td>{{$record->reason}}</td>
                                        <td>{{$record->action}}</td>
                                        <td>{{$record->medication}}</td>
                                        <td>
                                            <span class="badge badge-pill badge-light">{{ $record->temperature ? $record->temperature . ' °C' : ''}}</span>
                                            <span class="badge badge-pill badge-light">{{ $record->blood_pressure ?? 'N/A'}}</span>
                                            <span class="badge badge-pill badge-light">{{ $record->saturation ? $record->saturation . ' %' : ''}}</span>
                                            <span class="badge badge-pill badge-light">{{ $record->pulse ? $record->pulse . ' bpm' : ''}}</span>
                                        </td>
                                        <td>{{$record->comments}}</td>
                                        <td style="text-align: right;">
                                            <a href="#" class="btnDeleteGuestRecord" data-id="{{$record->health_record_id}}"><i class="fas fa-trash"></i></a>
                                        </td>
                                    </tr>
                                @endforeach
                                </tbody>
                            </table>
                        </div>


                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">{{ __('general.close') }}</button>
                </div>
            </div>
        </div>
    </div>
    <!-- modalGuest -->

    <!-- modalReports -->
    <div class="modal bg-white fade" tabindex="-1" id="modalReports">
        <div class="modal-dialog modal-fullscreen modal-dialog-scrollable">
            <div class="modal-content  shadow-none">
                <div class="modal-header">
                    <h5 class="modal-title">{{ __('app/health.reports') }}</h5>
                    <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal" aria-label="Close">
                        <i class="fa fa-times"></i>
                    </div>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-lg-2 col-xl-2 col-md-2 col-sm-12">

                            <form id="formReport">
                                <div class="row mb-3">
                                    <div class="col-9">
                                        <select name="reportType" id="reportType" class="form-control">
                                            <option value=""> Select Report</option>
                                            <option value="healthStudentStatus"> Student Health Info </option>
                                            <option value="healthStaffStatus"> Staff Health Info </option>
                                        </select>
                                    </div>
                                </div>
                            </form>
                        </div>

                        <div class="col-lg-10 col-xl-10 col-md-10 col-sm-12">
                            <table class="table table-striped border gs-5" id="tableReport">

                            </table>
                        </div>


                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">{{ __('general.close') }}</button>
                </div>
            </div>
        </div>
    </div>
    <!-- modalReports -->

@endsection