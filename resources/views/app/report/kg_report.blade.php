@extends('layouts.app')
@section('content')

<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.1/jquery.min.js"
    integrity="sha512-aVKKRRi/Q/YV+4mjoKBsE4x3H+BkegoM/em46NNlCqNTmUYADjBbeNefNxYV7giUp0VxICtqdrbqU7iVaeZNXA=="
    crossorigin="anonymous" referrerpolicy="no-referrer"></script>
<script src="/library/js/app/report/kg_report.js?v={{ time() }}"></script>

<!--start:::Main-->
<div class="app-main flex-column flex-row-fluid" id="kt_app_main">
    <div class="d-flex flex-column flex-column-fluid">
        <div id="kt_app_toolbar" class="app-toolbar py-3 py-lg-6">
            <div id="kt_app_toolbar_container" class="app-container container-xxl d-flex flex-stack">
                <div class="page-title d-flex flex-column justify-content-center flex-wrap me-3">
                    <h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">
                        {{ __('app/reports/report_card.report_card') }}
                    </h1>
                    <ul class="breadcrumb breadcrumb-separatorless fw-semibold fs-7 my-0 pt-1">
                        <li class="breadcrumb-item text-muted">
                            <a href="/app/home" class="text-muted text-hover-primary"> {{ __('general.home') }} </a>
                        </li>
                        <li class="breadcrumb-item">
                            <span class="bullet bg-gray-400 w-5px h-2px"></span>
                        <li class="breadcrumb-item text-muted"> {{ __('general.reports') }} </li>
                        <li class="breadcrumb-item">
                            <span class="bullet bg-gray-400 w-5px h-2px"></span>
                        <li class="breadcrumb-item text-muted"> {{ __('app/reports/report_card.report_card') }} </li>
                    </ul>
                </div>
                <div class="d-flex align-items-center gap-2 gap-lg-3">
                </div>
            </div>
        </div>

        <div id="kt_app_content" class="app-content flex-column-fluid">
            <div id="kt_app_content_container" class="app-container container-fluid">
                <div class="card card-flush h-md-100"
                    style="border-top-right-radius: 0px; border-bottom-right-radius: 0px;">
                    <div class="card-body" style="padding: 15px;">
                        <div class="row">
                            <div class="col-9">
                                <form id="formKGReportCard">

                                    <!-- terms -->
                                    <div class="row mb-3" id="divTermDates">
                                        <label for="inputEmail3"
                                            class="col-sm-2 col-form-label">{{ __('Term / Dates') }}</label>
                                        <div class="col-sm-10">
                                            <select class="form-control" data-control="select2" name="term"
                                                id="term" data-placeholder="Pick" required>
                                                <option value=""> {{ __('-') }} </option>
                                                @foreach ($terms as $term)
                                                    <option value="{{$term->semester_id}}" data-start="{{$term->start_date}}"
                                                        data-end="{{$term->end_date}}">Term {{$term->academic_semester}}
                                                        [{{$term->start_date}} / {{$term->end_date}}]</option>
                                                @endforeach
                                            </select>
                                        </div>
                                    </div>

                                    <!-- classes -->
                                    <div class="row mb-3">
                                        <label for="inputEmail3"
                                            class="col-sm-2 col-form-label">{{ __('app/reports/report_card.classroom') }}</label>
                                        <div class="col-sm-10">
                                            <select class="form-control" data-control="select2" name="classroom"
                                                id="classroom" data-placeholder="Pick a classrom" required>
                                                <option selected disabled>
                                                    {{ __('app/reports/report_card.select_classroom') }} </option>
                                                @foreach ($classrooms as $classroom)
                                                    <option value="{{$classroom->classroom_id}}">
                                                        {{$classroom->classroom_name}}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                    </div>

                                    <!-- students -->
                                    <div class="row mb-3">
                                        <label for="inputEmail3"
                                            class="col-sm-2 col-form-label">{{ __('app/reports/report_card.student') }}</label>
                                        <div class="col-sm-8">
                                            <select class="form-control" data-control="select2" multiple
                                                name="students[]" id="students"
                                                data-placeholder="{{ __('app/reports/report_card.pick_student') }}"
                                                required>
                                            </select>
                                        </div>
                                        <div class="col-sm-2">
                                            <a class="btn btn-danger w-100" id="btnClearSelection"><i
                                                    class="fas fa-trash"></i> {{ __('general.clear') }}</a>
                                        </div>
                                    </div>

                                    <!-- title -->
                                    <div class="row mb-3">
                                        <label for="inputEmail3"
                                            class="col-sm-2 col-form-label">{{ __('app/reports/report_card.title') }}</label>
                                        <div class="col-sm-10">
                                            <input type="text" class="form-control"
                                                placeholder="{{ __('app/reports/report_card.title_ex') }}"
                                                name="title" />
                                        </div>
                                    </div>

                                    <!-- button -->
                                    <div class="row mb-3">
                                        <a class="btn btn-primary" id="btnGenerageReportCard"><i class="fas fa-print"
                                                id="btnGenerageReportCard"></i>
                                            {{ __('app/reports/report_card.generate_report') }}</a>
                                        <!-- <a id="btnSubmitReportCard" target="blank" style="display: none;"></a> -->
                                    </div>

                                </form>
                            </div>

                            <div class="col-3">
                                <p><strong>{{ __('app/reports/report_card.latest_requests') }}</strong></p>
                                <hr />
                                <table class="table" id="tableOldRequests">
                                    <thead>
                                        <th> {{ __('app/reports/report_card.date') }} </th>
                                        <th> {{ __('app/reports/report_card.classroom') }} </th>
                                        <th> {{ __('app/reports/report_card.title') }} </th>
                                    </thead>
                                    <tbody>
                                        @foreach ($requests as $req)
                                            <tr>
                                                <td>{{$req->created_at}}</td>
                                                <td>{{$req->classroom->classroom_name ?? 'N/A'}}</td>
                                                <td>{{$req->report_title}}</td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!--end:::Main-->

@endsection