@extends('layouts.app')
@section('content')
    <!--start:::Main-->
    <div class="app-main flex-column flex-row-fluid" id="kt_app_main">
        <div class="d-flex flex-column flex-column-fluid">
            <div id="kt_app_toolbar" class="app-toolbar py-3 py-lg-6">
                <div id="kt_app_toolbar_container" class="app-container container-xxl d-flex flex-stack">
                    <div class="page-title d-flex flex-column justify-content-center flex-wrap me-3">
                        <h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">
                            {{ __('app/reports/assessment_report.assessment_report') }} </h1>
                        <ul class="breadcrumb breadcrumb-separatorless fw-semibold fs-7 my-0 pt-1">
                            <li class="breadcrumb-item text-muted">
                                <a href="/app/home" class="text-muted text-hover-primary"> {{ __('general.home') }} </a>
                            </li>
                            <li class="breadcrumb-item">
                                <span class="bullet bg-gray-400 w-5px h-2px"></span>
                            <li class="breadcrumb-item text-muted"> {{ __('general.reports') }} </li>
                            <li class="breadcrumb-item">
                                <span class="bullet bg-gray-400 w-5px h-2px"></span>
                            <li class="breadcrumb-item text-muted">
                                {{ __('app/reports/assessment_report.assessment_report') }} </li>
                        </ul>
                    </div>
                    <div class="d-flex align-items-center gap-2 gap-lg-3">
                    </div>
                </div>
            </div>

            <div id="kt_app_content" class="app-content flex-column-fluid">
                <div id="kt_app_content_container" class="app-container container-fluid">
                    <div class="card card-flush h-md-100"
                        style="border-top-right-radius: 0px; border-bottom-right-radius: 0px;">
                        <div class="card-body" style="padding: 15px;">
                            <div class="row">
                                <div class="col-xs-12 col-md-2 col-lg-3">
                                    {{ __('app/reports/assessment_report.type') }}
                                    <select class="form-control" data-control="select2" id="type">
                                        <option value="summative">Assessment</option>
                                        <option value="formative">Life Skill</option>
                                    </select>
                                </div>

                                <div class="col-xs-12 col-md-2 col-lg-3">
                                    {{ __('app/reports/assessment_report.subject') }}
                                    <select class="form-control" data-control="select2" id="subject">
                                        @foreach ($subjects as $subject)
                                            <option value="{{ $subject->subject_id }}">{{ $subject->subject_name }}</option>
                                        @endforeach
                                    </select>
                                </div>

                                {{-- <div class="col-xs-12 col-md-2 col-lg-2">
                                Class
                                <select class="form-control" data-control="select2" id="classroom">
                                    @foreach ($classrooms as $classroom)
                                        <option value="{{$classroom->classroom_id}}">{{$classroom->classroom_name}}</option>
                                    @endforeach
                                </select>
                            </div> --}}

                                <div class="col-xs-12 col-md-4 col-lg-4">
                                    {{ __('app/reports/assessment_report.dates') }}
                                    <input type="text" class="form-control" id="date_range" />
                                </div>

                                <div class="col-xs-12 col-md-2 col-lg-2">
                                    &nbsp<br />
                                    <a class="btn btn-primary w-100" id="btnSearch"><i class="fas fa-search"></i>
                                        {{ __('general.search') }}</a>
                                </div>
                            </div>
                            <hr />
                            <div class="row">
                                <div class="col-12" id="searchArea">
                                    <p class="text-center">{{ __('app/reports/assessment_report.search_result') }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!--end:::Main-->





    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.1/jquery.min.js"
        integrity="sha512-aVKKRRi/Q/YV+4mjoKBsE4x3H+BkegoM/em46NNlCqNTmUYADjBbeNefNxYV7giUp0VxICtqdrbqU7iVaeZNXA=="
        crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script src="/library/js/app/report/assessment_report.js"></script>
@endsection
