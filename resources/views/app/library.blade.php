@extends('layouts.app')
@section('content')
<div class="app-main flex-column flex-row-fluid" id="kt_app_main">
    <div class="d-flex flex-column flex-column-fluid">
        <div id="kt_app_toolbar" class="app-toolbar py-3 py-lg-6">
            <div id="kt_app_toolbar_container" class="app-container container-xxl d-flex flex-stack">
                <div class="page-title d-flex flex-column justify-content-center flex-wrap me-3">
                    <h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">
                        {{ __('app/library_manager.library_manager') }} </h1>
                    <ul class="breadcrumb breadcrumb-separatorless fw-semibold fs-7 my-0 pt-1">
                        <li class="breadcrumb-item text-muted">
                            <a href="/app/home" class="text-muted text-hover-primary"> {{ __('general.home') }} </a>
                        </li>
                        <li class="breadcrumb-item">
                            <span class="bullet bg-gray-400 w-5px h-2px"></span>
                        <li class="breadcrumb-item text-muted"> {{ __('app/library_manager.library_manager') }} </li>
                    </ul>
                </div>
                <div class="d-flex align-items-center gap-2 gap-lg-3">
                </div>
            </div>
        </div>

        <div id="kt_app_content" class="app-content flex-column-fluid">
            <div id="kt_app_content_container" class="app-container container-fluid">

                <div class="row">
                    <div class="col-12">
                        <div class="card card-flush ">
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-lg-9 col-xl-9 col-md-9 col-xs-12">
                                        <div class="row">
                                            <div class="col-lg-3 col-xs-12">
                                                <a href="{{url('/app/library/book/show-all-book')}}"
                                                    class="btn btn-dark w-100" id="btnAllBook">
                                                    <div class="symbol symbol-55px mb-5">
                                                        <i style="font-size: 25px; padding-top: 10px;"
                                                            class="fa-solid fa-book-open"></i>
                                                    </div>
                                                    <div class="fs-5 fw-bold mb-2">{{ __('app/library_manager.all_book')
                                                        }}
                                                    </div>
                                                </a>
                                            </div>
                                            {{-- <div class="col-lg-3 col-xs-12">
                                                <a href="#" class="btn btn-dark w-100" id="btnSearch"
                                                    data-bs-target="#modalSendNotification">
                                                    <div class="symbol symbol-55px mb-5">
                                                        <i style="font-size: 25px; padding-top: 10px;"
                                                            class="fa-solid fa-search"></i>
                                                    </div>
                                                    <div class="fs-5 fw-bold mb-2">{{ __('general.search') }}</div>
                                                </a>
                                            </div> --}}

                                            <div class="col-lg-3 col-xs-12">
                                                <a href="#" class="btn btn-dark w-100" id="btnIssueBook"
                                                    data-bs-toggle="modal" data-bs-target="#modalIssueBook">
                                                    <div class="symbol symbol-55px mb-5">
                                                        <i style="font-size: 25px; padding-top: 10px;"
                                                            class="fa-solid fa-right-from-bracket"></i>
                                                    </div>
                                                    <div class="fs-5 fw-bold mb-2">{{
                                                        __('app/library_manager.issue_book') }}</div>
                                                </a>
                                            </div>

                                            <div class="col-lg-3 col-xs-12">
                                                <a href="#" id="btnBarcodeReturnBook" class="btn btn-secondary w-100"
                                                    data-bs-toggle="modal" data-bs-target="#modalBarcodeReturnBook">
                                                    <div class="symbol symbol-55px mb-5">
                                                        <i style="font-size: 25px; padding-top: 10px;"
                                                            class="fa-solid fa-download"></i>
                                                    </div>
                                                    <div class="fs-5 fw-bold mb-2">{{
                                                        __('app/library_manager.return_book') }}</div>
                                                </a>
                                            </div>
                                            {{-- <div class="col-lg-3 col-xs-12">
                                                <a href="#" id="btnSearch" style="opacity: 0.3; pointer-events: none;"
                                                    class="btn btn-secondary w-100">
                                                    <div class="symbol symbol-55px mb-5">
                                                        <i style="font-size: 25px; padding-top: 10px;"
                                                            class="fa-solid fa-chart-bar"></i>
                                                    </div>
                                                    <div class="fs-5 fw-bold mb-2">{{ __('general.reports') }}</div>
                                                </a>
                                            </div> --}}
                                            <div class="col-lg-3 col-xs-12">
                                                <a href="{{url('/app/library/book/overdue-student-book')}}"
                                                    class="btn btn-dark w-100" id="btnOverdueStudent"
                                                    data-bs-target="#modalSendNotification">
                                                    <div class="symbol symbol-55px mb-5">
                                                        <i style="font-size: 25px; padding-top: 10px;"
                                                            class="fa-solid fa-user-clock"></i>
                                                    </div>
                                                    <div class="fs-5 fw-bold mb-2">{{
                                                        __('app/library_manager.overdue_students') }}</div>
                                                </a>
                                            </div>
                                        </div>
                                        <br />
                                        <div class="row">
                                            <div class="col-lg-3 col-xs-12">
                                                <a href="#" id="btnAddBook" class="btn btn-secondary w-100">
                                                    <div class="symbol symbol-55px mb-5">
                                                        <i style="font-size: 25px; padding-top: 10px;"
                                                            class="fa-solid fa-book"></i>
                                                    </div>
                                                    <div class="fs-5 fw-bold mb-2">{{ __('app/library_manager.add_book')
                                                        }}</div>
                                                </a>
                                            </div>

                                            <div class="col-lg-3 col-xs-12">
                                                <a href="#" id="btnAddEbook" class="btn btn-secondary w-100"
                                                    style="opacity: 0.3; pointer-events: none;">
                                                    <div class="symbol symbol-55px mb-5">
                                                        <i style="font-size: 25px; padding-top: 10px;"
                                                            class="fa-solid fa-file-circle-plus"></i>
                                                    </div>
                                                    <div class="fs-5 fw-bold mb-2">{{
                                                        __('app/library_manager.add_ebook') }}</div>
                                                </a>
                                            </div>

                                            <div class="col-lg-3 col-xs-12">
                                                <a href="#" id="btnAuthor" class="btn btn-secondary w-100">
                                                    <div class="symbol symbol-55px mb-5">
                                                        <i style="font-size: 25px; padding-top: 10px;"
                                                            class="fa-solid fa-feather"></i>
                                                    </div>
                                                    <div class="fs-5 fw-bold mb-2">{{ __('app/library_manager.authors')
                                                        }}</div>
                                                </a>
                                            </div>
                                            <div class="col-lg-3 col-xs-12">
                                                <a href="#" id="btnUser" class="btn btn-secondary w-100">
                                                    <div class="symbol symbol-55px mb-5">
                                                        <i style="font-size: 25px; padding-top: 10px;"
                                                            class="fa-solid fa-users"></i>
                                                    </div>
                                                    <div class="fs-5 fw-bold mb-2">{{
                                                        __('app/library_manager.issue_users')
                                                        }}</div>
                                                </a>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-3 d-none d-sm-block">
                                        @include('partial_view.app.library.statistics')
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <br />
                {{-- <div class="card card-flush h-md-50">
                    <div class="card-header">
                        <h3 class="card-title">{{ __('app/library_manager.issued_books') }}</h3>
                    </div>
                    <div class="card-body" style="padding-top: 0px;">
                        <table class="table table-hover border" id="homeIssuesTable">
                            <thead>
                                <th style="padding-left: 10px;"> {{ __('app/library_manager.id') }} </th>
                                <th style="padding-left: 10px;"> {{ __('app/library_manager.user') }} </th>
                                <th style="padding-left: 10px;"> {{ __('app/library_manager.book') }} </th>
                                <th style="padding-left: 10px;"> {{ __('app/library_manager.barcode') }} </th>
                                <th style="padding-left: 10px;"> {{ __('app/library_manager.issued_date') }} </th>
                                <th style="padding-left: 10px;"> {{ __('app/library_manager.status') }} </th>
                                <th style="padding-left: 10px;"> {{ __('app/library_manager.type') }} </th>
                                <th style="padding-right: 20px; text-align:right;"> {{ __('app/library_manager.action')
                                    }}</th>
                            </thead>
                            <tbody>
                                @foreach ($issues as $issue)
                                <?php
                                        // $shouldReturnDate = $issue->should_return_date;
                                        // $status = "";
                                        // $rowStyle = "";

                                        // $dayCount = strtotime($shouldReturnDate) - strtotime(date("Y-m-d"));
                                        // $dayCount = $dayCount / 86400;

                                        // if (strtotime($shouldReturnDate) < strtotime(date("Y-m-d"))) {
                                        //     $dayCount = $dayCount * -1;
                                        //     $status = "DELAYED ".$dayCount." days";
                                        //     $rowStyle = " activeRow ";
                                        //     $delayStyle = " color: rgba(223, 82, 67); ";
                                        // } else {
                                        //     $status = $dayCount." days remain";
                                        //     $delayStyle = "";
                                        // }


                                    ?>
                                <tr style="cursor: pointer; " class="issueRow {{$rowStyle}}"
                                    data-id="{{$issue->issue_id}}">
                                    <td style="padding-left: 10px;">{{$issue->user->id ?? ''}}</td>
                                    <td style="padding-left: 10px;">{{$issue->user->name ?? ''}}</td>
                                    <td style="padding-left: 10px;">{{$issue->book->title}}</td>
                                    <td style="padding-left: 10px;">{{$issue->book->barcode}}</td>
                                    <td style="padding-left: 10px;">{{$issue->issue_date}}</td>
                                    <td style="padding-left: 10px; {{$delayStyle}}">{{$status}}</td>
                                    <td style="padding-left: 10px;">{{$issue->issue_type}}</td>
                                    <td style="padding-right: 20px; text-align:right;">
                                        @if ($issue->is_renewed == 0)
                                        <i class="fa-solid fa-rotate-right renewIssueBook"
                                            style="color: rgba(71, 169, 112);" title="Renew Issue Book"></i>
                                        @else
                                        <i class="fa-solid fa-ban" style="color: rgba(149, 149, 149);"
                                            title="Cannot Renew Book"></i>
                                        @endif
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div> --}}
                <br />
                <div class="card card-flush h-md-50">
                    <div class="card-header">
                        <h3 class="card-title">{{ __('app/library_manager.issued_books') }}</h3>
                    </div>
                    <div class="card-body" style="padding-top: 0px;">
                        <table class="table table-hover border" id="issueBooksTable">
                            <thead>
                                <th style="padding-left: 10px;"> {{ __('app/library_manager.id') }} </th>
                                <th style="padding-left: 10px;"> {{ __('app/library_manager.user') }} </th>
                                <th style="padding-left: 10px;"> {{ __('app/library_manager.year') }} </th>
                                <th style="padding-left: 10px;"> {{ __('app/library_manager.book') }} </th>
                                <th style="padding-left: 10px;"> {{ __('app/library_manager.barcode') }} </th>
                                <th style="padding-left: 10px;"> {{ __('app/library_manager.issued_date') }} </th>
                                <th style="padding-left: 10px;"> {{ __('app/library_manager.status') }} </th>
                                <th style="padding-left: 10px;"> {{ __('app/library_manager.type') }} </th>
                                <th style="padding-right: 20px; text-align:right;"> {{ __('app/library_manager.action')
                                    }}</th>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!--end:::Main-->

<div class="modal fade" tabindex="-1" id="modalIssueDetail">
    <div class="modal-dialog modal-xl modal-dialog-centered modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ __('app/library_manager.issue_details') }} </h5>
                <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal"
                    aria-label="Close">
                    <i class="fa fa-times"></i>
                </div>
            </div>
            <div class="modal-body" id="issueDetailArea">

            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-light" data-bs-dismiss="modal"> {{ __('general.close') }}
                </button>
                <button type="button" class="btn btn-danger" id="btnIssueReturnBook">
                    {{ __('app/library_manager.return_book') }} </button>
            </div>
        </div>
    </div>
</div>

<div class="modal bg-white fade" tabindex="-1" id="modalSearch">
    <div class="modal-dialog modal-fullscreen">
        <div class="modal-content shadow-none">
            <div class="modal-header">
                <h5 class="modal-title">{{ __('app/library_manager.books') }}</h5>
                <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal"
                    aria-label="Close">
                    <span class="svg-icon svg-icon-2x"></span>
                </div>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-12" id="divList">
                        <table class="table table-striped table-hover border gs-7" id="tableBookList">
                            <thead>
                                <th> {{ __('app/library_manager.title') }} </th>
                                <th> {{ __('app/library_manager.barcode') }} </th>
                                <th> {{ __('app/library_manager.language') }} </th>
                                <th> {{ __('app/library_manager.publication_year') }} </th>
                                <th> {{ __('app/library_manager.publisher') }} </th>
                                <th> {{ __('app/library_manager.isbn') }} </th>
                            </thead>
                            <tbody>

                            </tbody>
                        </table>
                    </div>

                    <div class="col-4" id="divDetail" style="display: none;">
                        <p class="text-center"><img src="/img/loader.gif" style="width: 30px;" /></p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-light" data-bs-dismiss="modal"> {{ __('general.close') }}
                </button>
                <button type="button" class="btn btn-danger" id="btnDeleteBook" style="display: none;">
                    {{ __('app/library_manager.delete_book') }} </button>
                <button type="button" class="btn btn-primary" id="btnSaveBookInfo" style="display: none;">
                    {{ __('general.save_changes') }} </button>
            </div>
        </div>
    </div>
</div>

{{-- @include('partial_view.app.library.add_book') --}}
<div class="modal fade" tabindex="-1" id="modalAddBook">
    <div class="modal-dialog modal-xl modal-dialog-centered modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"> {{ __('app/library_manager.upload_book_list') }} </h5>
                <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal"
                    aria-label="Close">
                    <i class="fa fa-times"></i>
                </div>
            </div>

            <div class="modal-body" id="addBookArea">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-light" data-bs-dismiss="modal"> {{ __('general.close') }}</button>
                <button type="button" class="btn btn-primary" id="btnSubmitNewBook">{{
                    __('app/library_manager.add_book') }} </button>
            </div>
        </div>
    </div>
</div>
{{-- --}}
{{-- <div id="asdasd">

</div> --}}
<div class="modal fade" tabindex="-1" id="modalAuthors">
    <div class="modal-dialog modal-xl modal-dialog-centered modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ __('app/library_manager.authors') }}</h5>
                <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal"
                    aria-label="Close">
                    <i class="fa fa-times"></i>
                </div>
            </div>
            <div class="modal-body">
                <div class="row mb-3">
                    <label class="col-2 col-form-label">{{ __('app/library_manager.author_name') }}</label>
                    <div class="col-5">
                        <input type="text" class="form-control" id="newAuthorName" />
                    </div>
                    <div class="col-3">
                        <a class="btn btn-dark " id="btnSubmitNewAuthor"><i class="fas fa-save"></i> {{
                            __('app/library_manager.add_author') }}</a>
                    </div>
                    {{-- Bulk Excel Input button --}}
                    <div class="col-2">
                        <a href="#" class="btn fw-bold btn-secondary " id="btnCreateBulkAuthor" data-bs-dismiss="modal"
                            data-bs-target="#modalBulkInventory"><i class="fas fa-file-excel"></i>
                            {{ __('app/library_manager.excel_upload')}} </a>
                    </div>
                </div>
                <hr />
                <div class="row">
                    <div class="col-12" id="authorDetailArea">
                        <p class="text-center"><img src="/img/loader.gif" style="width: 30px;" /></p>
                    </div>
                    <div class="col-6" id="authorBooksArea" style="display:none;">
                        <p class="text-center"><img src="/img/loader.gif" style="width: 30px;" /></p>
                    </div>
                </div>

            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-light" data-bs-dismiss="modal">{{ __('general.close')
                    }}</button>
            </div>
        </div>
    </div>
</div>
<div class="modal fade" tabindex="-1" id="modalUsers">
    <div class="modal-dialog modal-xl modal-dialog-centered modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ __('app/library_manager.total_users') }}</h5>
                <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal"
                    aria-label="Close">
                    <i class="fa fa-times"></i>
                </div>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-12" id="userDetailArea">
                        <p class="text-center"><img src="/img/loader.gif" style="width: 30px;" /></p>
                    </div>
                    <div class="col-7" id="userBooksArea" style="display:none;">
                        <p class="text-center"><img src="/img/loader.gif" style="width: 30px;" /></p>
                    </div>
                </div>

            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-light" data-bs-dismiss="modal">{{ __('general.close')
                    }}</button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" tabindex="-1" id="modalIssueBook">
    <div class="modal-dialog modal-xl modal-dialog-centered modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ __('app/library_manager.issue_book') }}</h5>
                <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal"
                    aria-label="Close">
                    <i class="fa fa-times"></i>
                </div>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-6">
                        <div class="row mb-3">
                            <label class="col-2 col-form-label">{{ __('app/library_manager.user') }}</label>
                            <div class="col-7">
                                <select class="form-control" id="issueUserId" data-control="select2"
                                    data-placeholder="{{ __('app/library_manager.select_user') }}"
                                    data-dropdown-parent="#modalIssueBook">
                                    <option value="0">{{ __('app/library_manager.select_user') }}</option>
                                    @foreach ($users as $user)
                                    <option value="{{$user->id}}">
                                        @if($user->student_detail)
                                        [{{$user->student_detail->student_number}}] {{$user->name}} /
                                        {{$user->user_type}}
                                        @else
                                        [{{$user->id}}] {{$user->name}} /
                                        {{$user->user_type}}
                                        @endif
                                    </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-3">
                                <a class="btn btn-dark w-100" id="btnSearchUser"><i class="fas fa-search"></i> {{
                                    __('general.search') }}</a>
                            </div>
                        </div>
                        <hr />
                        <div id="searchUserArea"></div>
                    </div>

                    <div class="col-6">
                        <div class="row mb-3">
                            <div class="col-9">
                                <input type="text" class="form-control" placeholder="Barcode or ISBN"
                                    id="issueBookSearch" />
                            </div>
                            <div class="col-3">
                                <a class="btn btn-dark w-100" id="btnSeachBook"><i class="fas fa-search"></i> {{
                                    __('general.search') }}</a>
                            </div>
                        </div>
                        <hr />
                        <div id="searchBookArea"></div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-light" data-bs-dismiss="modal"> {{ __('general.close')
                    }}</button>
                <button type="button" class="btn btn-primary" id="btnSubmitIssue"><i class="fas fa-save"></i>{{
                    __('app/library_manager.issue_book') }} </button>
            </div>
        </div>
    </div>
</div>
<div class="modal fade" tabindex="-1" id="modalUploadBulkBook">
    <div class="modal-dialog modal-xl modal-dialog-centered modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"> {{ __('app/library_manager.upload_book_list') }} </h5>
                <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal"
                    aria-label="Close">
                    <i class="fa fa-times"></i>
                </div>
            </div>

            <div class="modal-body" id="bulkBookResponseArea">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-light" data-bs-dismiss="modal"> {{ __('general.close')
                    }}
                </button>
                <button type="button" class="btn btn-primary" id="btnSubmitBulkBook"> {{
                    __('general.save_changes') }}
                </button>
            </div>
        </div>
    </div>
</div>
<div class="modal fade" tabindex="-1" id="modalUploadBulkAuthor">
    <div class="modal-dialog modal-xl modal-dialog-centered modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"> {{ __('app/library_manager.upload_author_list') }} </h5>
                <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal"
                    aria-label="Close">
                    <i class="fa fa-times"></i>
                </div>
            </div>

            <div class="modal-body" id="bulkAuthorResponseArea">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-light" data-bs-dismiss="modal"> {{ __('general.close')
                    }}
                </button>
                <button type="button" class="btn btn-primary" id="btnSubmitBulkAuthor"> {{
                    __('general.save_changes') }}
                </button>
            </div>
        </div>
    </div>
</div>
{{-- Return Book Model --}}
<div class="modal fade" tabindex="-1" id="modalBarcodeReturnBook">
    <div class="modal-dialog modal-xl modal-dialog-centered modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ __('app/library_manager.issue_details') }} </h5>
                <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal"
                    aria-label="Close">
                    <i class="fa fa-times"></i>
                </div>
            </div>


            <div class="modal-body">
                <div class="row mb-4">
                    <div class="col-9">
                        <input type="text" class="form-control" placeholder="Barcode or ISBN"
                            id="barcodeIssueBookSearch" />
                    </div>
                    <div class="col-3">
                        <a class="btn btn-dark w-100" id="btnSearchBarcodeIssueBook"><i class="fas fa-search"></i> {{
                            __('general.search') }}</a>
                    </div>
                </div>
                <hr />
                <div id="barcodeIssueDetailArea">

                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-light" data-bs-dismiss="modal"> {{ __('general.close') }}
                </button>
                <button type="button" class="btn btn-danger" id="btnBarcodeIssueReturnBook">
                    {{ __('app/library_manager.return_book') }} </button>
            </div>
        </div>
    </div>
</div>
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.1/jquery.min.js"
    integrity="sha512-aVKKRRi/Q/YV+4mjoKBsE4x3H+BkegoM/em46NNlCqNTmUYADjBbeNefNxYV7giUp0VxICtqdrbqU7iVaeZNXA=="
    crossorigin="anonymous" referrerpolicy="no-referrer"></script>
<script src="/library/js/app/library/library.js"></script>

@endsection