<?php
    use App\Models\MobileDevice;
    use App\Library\Helper\AcademicHelper;
    use App\Models\SummativeAssessmentData;
    use App\Models\FormativeAssessmentData;

    $ah = new AcademicHelper();
    $userId = \Auth::user()->id;

    $summativeList = SummativeAssessmentData::leftJoin('academic_summative_assessments', 'academic_summative_assessments.assessment_id', 'academic_summative_assessments_data.assessment_id')
                                            ->leftJoin('subjects', 'subjects.subject_id', 'academic_summative_assessments.subject_id')
                                            ->leftJoin('users', 'users.id', 'academic_summative_assessments.teacher_id')
                                            ->where('academic_summative_assessments.academic_year_id', $ah->academicYear())
                                            ->where('academic_summative_assessments_data.score', '>=', 0)
                                            ->where('academic_summative_assessments_data.student_id', $userId)
                                            ->orderBy('academic_summative_assessments.created_at', 'desc')
                                            ->get();

    $formativeList = FormativeAssessmentData::select(
                                                        'academic_formative_assessments_data.t1 as tt1',
                                                        'academic_formative_assessments_data.t2 as tt2',
                                                        'academic_formative_assessments_data.t3 as tt3',
                                                        'academic_formative_assessments_data.t4 as tt4',
                                                        'users.name',
                                                        'subjects.subject_name',
                                                        'academic_formative_assessments.date'
                                                    )
                                            ->leftJoin('academic_formative_assessments', 'academic_formative_assessments.formative_assessment_id', 'academic_formative_assessments_data.formative_assessment_id')
                                            ->leftJoin('subjects', 'subjects.subject_id', 'academic_formative_assessments.subject_id')
                                            ->leftJoin('users', 'users.id', 'academic_formative_assessments.teacher_id')
                                            ->where('academic_formative_assessments.academic_year_id', $ah->academicYear())
                                            ->where('academic_formative_assessments_data.student_id', $userId)
                                            ->orderBy('academic_formative_assessments.created_at', 'desc')
                                            ->get();

?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="refresh" content="7200">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, shrink-to-fit=no">
    <meta name="_token" content="{!! csrf_token() !!}"/>
    <link rel="icon" type="image/x-icon" href="/theme_v2/assets/img/favicon.ico" />
    <link href="https://fonts.googleapis.com/css?family=Nunito:400,600,700" rel="stylesheet">
    <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.15.4/css/all.css" integrity="sha384-DyZ88mC6Up2uqS4h/KRgHuoeGwBcD4Ng9SiP4dIRy0EXTlnuz47vAwmeGwVChigm" crossorigin="anonymous">
    <link href="/theme/dist/assets/plugins/global/plugins.bundle.css" rel="stylesheet" type="text/css" />
    <link href="/theme/dist/assets/css/style.bundle.css" rel="stylesheet" type="text/css" />
    <link href="/theme/dist/assets/plugins/global/plugins.bundle.css" rel="stylesheet" type="text/css"/>
    <link href="/theme/dist/assets/plugins/custom/datatables/datatables.bundle.css" rel="stylesheet" type="text/css"/>


    <div style="padding: 10px;">
        <ul class="nav nav-tabs nav-fill nav-line-tabs mb-5 fs-6">
            <li class="nav-item">
                <a class="nav-link active" data-bs-toggle="tab" href="#tabSummative"><strong>ASESSMENT</strong></a>
            </li>
            <li class="nav-item">
                <a class="nav-link" data-bs-toggle="tab" href="#tabFormative"><strong>LIFE SKILL</strong></a>
            </li>
        </ul>

        <div class="tab-content" id="myTabContent">
            <div class="tab-pane fade show active" id="tabSummative" role="tabpanel">
                <table id="summativeTable" class="table table-striped gs-7">
                    <thead>
                        <th>Date</th>
                        <th>Subject</th>
                        <th>Title</th>
                        <th>Type</th>
                        <th>Score</th>
                        <th>Percentage</th>
                        <th>Teacher</th>
                    </thead>
                    <tbody>
                        @foreach ($summativeList as $summative)
                            <tr>
                                <td>{{$summative->date}}</td>
                                <td>{{$summative->subject_name}}</td>
                                <td>{{$summative->assessment_name}}</td>
                                <td>{{$summative->type_title}}</td>
                                <td>{{$summative->score}}</td>
                                <td>{{$summative->score_percentage}}</td>
                                <td>{{$summative->name}}</td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
            <div class="tab-pane fade" id="tabFormative" role="tabpanel">
                <table id="formativeTable" class="table table-striped gs-7">
                    <thead>
                        <th>Date</th>
                        <th>Subject</th>
                        <th>EE</th>
                        <th>ME</th>
                        <th>AE</th>
                        <th>BE</th>
                        <th>Teacher</th>
                    </thead>
                    <tbody>
                        @foreach ($formativeList as $formative)
                            <tr>
                                <td>{{$formative->date}}</td>
                                <td>{{$formative->subject_name}}</td>
                                <td>{{$formative->tt1}}</td>
                                <td>{{$formative->tt2}}</td>
                                <td>{{$formative->tt3}}</td>
                                <td>{{$formative->tt4}}</td>
                                <td>{{$formative->name}}</td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>
