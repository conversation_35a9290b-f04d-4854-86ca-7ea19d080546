<?php

    use App\Models\StudentInformation;
    use App\Models\ElectiveGrade;
    use App\Models\SummativeAssessmentData;
    use App\Models\FormativeAssessmentData;
    use App\Library\Helper\ReportCardHelper;
    use App\Models\ScoreTemplate;
    use App\Models\DisciplineRecord;

    $reportHelper = new ReportCardHelper();

    $info = StudentInformation::find($studentId);

    $englishSubjects = null;
    $khmerSubjects = null;
    $totalEnglishSum = 0;
    $totalKhmerSum = 0;
    $totalEnglishCount = 0;
    $totalKhmerCount = 0;


    $subjects = ElectiveGrade::select(
                                    'subjects.subject_id',
                                    'subjects.subject_name',
                                    'academic_elective_grade.grade_id',
                                    'subjects.subject_type'
                                )
                                ->leftJoin('academic_elective_grade_students', 'academic_elective_grade.grade_id', 'academic_elective_grade_students.grade_id')
                                ->leftJoin('subjects', 'subjects.subject_id', 'academic_elective_grade.subject_id')
                                ->where('academic_elective_grade_students.student_id', $studentId)
                                ->where('academic_elective_grade.academic_year_id', $academicYearId)
                                ->groupBy(
                                            'academic_elective_grade.subject_id',
                                            'subjects.subject_id',
                                            'subjects.subject_name',
                                            'academic_elective_grade.grade_id'
                                         )
                                ->get();

    foreach ($subjects as $key => $subject) {
        foreach($excluded as $ex) {
            if($subject->subject_id == $ex->subject_id) {
              $subjects->forget($key);
            }
        }
    }
?>
<div style="height: 80px; padding: 10px; padding-bottom: 0px;">
    <div style="text-align: center;"><img src="{{$_SERVER['DOCUMENT_ROOT']}}/img/logo.png" style="width: 250px;" /></div>
</div>

<div style="text-align: center;"><h3>{{$reportTitle}}</h3></div>

{{-- <div style="border-bottom: 1px solid #0e284d;"></div> --}}

<table style="width: 100%;">
    <tr>
        <td style="width: 75%;">
            <div class="table100 ver5">
                <div class="table100-body js-pscroll ps ps--active-y">
                    <table style="width: 90%;">
                        <tbody>
                            <tr>
                                <td class="cell100 column1 col1" style="border-left: 1px solid #0e284d!important;">Date Range</td>
                                <td class="cell100 column2 col2">{{$startDate}}/{{$endDate}}</td>
                            </tr>
                            <tr>
                                <td class="cell100 column1 col1">Student Name/ID</td>
                                <td class="cell100 column2 col2">{{$info->name}} #{{$info->id}}</td>
                            </tr>
                            {{-- <tr>
                                <td class="cell100 column1 col1">School ID</td>
                                <td class="cell100 column2 col2"></td>
                            </tr> --}}
                            <tr>
                                <td class="cell100 column1 col1">Grade</td>
                                <td class="cell100 column2 col2">{{$classroomName}}</td>
                            </tr>
                            <tr>
                                <td class="cell100 column1 col1">Report ID</td>
                                <td class="cell100 column2 col2">{{strtoupper($uid)}}</td>
                            </tr>
                            <tr>
                                <td class="cell100 column1 col1">Created at</td>
                                <td class="cell100 column2 col2">{{$createdAt}}</td>
                            </tr>

                        </tbody>
                    </table>
                </div>
            </div>
        </td>
        <td style="width: 20%;">
            <img src="{{$_SERVER['DOCUMENT_ROOT'].$info->photo}}" style="width: 100%; border-radius: 10px;" />
        </td>
    </tr>
</table>

<div style="border-bottom: 1px solid #0e284d;"></div>


{{-- <div style="text-align: center;"><h3>Grading System</h3></div> --}}
{{-- <div style="text-align: center;"><h3>All school Grading system (International & Bilingual)</h3></div> --}}
<br/>
<table style="width: 100%; font-size: 12px; border-collapse: collapse;" border="1" id="assessmentListTable">
    <thead>
        <tr>
            <th colspan="4" style="text-align: center; font-size: 15px; ">Grading System</th>
        </tr>
        <tr>
            <th colspan="3" style="width: 70%; text-align: center;">ASSESSMENT SCALE</th>
            <th style="width: 30%; text-align: center;">LIFE SKILL SCALE</th>
        </tr>
        <tr>
            <td style="text-align: center;">GRADE</td>
            <td style="text-align: center;">Average Score%</td>
            <td style="text-align: center;">Descriptor</td>
            <td style="text-align: center;">Life Skill Descriptor</td>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td style="background-color: #e5a63b; text-align: center;">A</td>
            <td style="background-color: #e5a63b; text-align: center;">85-100</td>
            <td style="background-color: #e5a63b; text-align: center;">Student experienced excellent achievement</td>
            <td style="background-color: #e5a63b; text-align: center;">EE-Exceeds Expectation</td>
        </tr>
        <tr>
            <td style="background-color: #71a737; text-align: center;">B</td>
            <td style="background-color: #71a737; text-align: center;">70-84</td>
            <td style="background-color: #71a737; text-align: center;">Student experienced good achievement</td>
            <td style="background-color: #71a737; text-align: center;">ME - Meets Expectation</td>
        </tr>
        <tr>
            <td style="background-color: #71a737; text-align: center;">C</td>
            <td style="background-color: #71a737; text-align: center;">60-69</td>
            <td style="background-color: #71a737; text-align: center;">Student experienced satisfactory achievement</td>
            <td style="background-color: #71a737; text-align: center;">ME - Meets Expectation</td>
        </tr>
        <tr>
            <td style="background-color: #4c9fdc; text-align: center;">D</td>
            <td style="background-color: #4c9fdc; text-align: center;">50-59</td>
            <td style="background-color: #4c9fdc; text-align: center;">Student experienced limited achievement</td>
            <td style="background-color: #4c9fdc; text-align: center;">AE - Approaches Expectation</td>
        </tr>
        <tr>
            <td style="background-color: #ed7267; text-align: center;">F</td>
            <td style="background-color: #ed7267; text-align: center;">0-49</td>
            <td style="background-color: #ed7267; text-align: center;">Student experienced only minimal achievement</td>
            <td style="background-color: #ed7267; text-align: center;">BE - Below Expectation</td>
        </tr>
        <tr>
            <td style="background-color: #808080; text-align: center;">U</td>
            <td style="background-color: #808080; text-align: center; padding: 5px;" colspan="3">Applies when there is not enough evidence to allocate a grade</td>
        </tr>
    </tbody>
</table>

{{-- <br/><br/>
<div style="border-bottom: 1px solid #0e284d;"></div><br/>

<table style="width: 100%;">
    <tr>
        <td style="width: 50%; text-align: left;">
            www.paragonisc.edu.kh<br/>
            <EMAIL>
        </td>
        <td style="width: 50%; text-align: right;">
            RC-U-ID<br/>
            {{strtoupper($uid)}}
        </td>
    </tr>
</table> --}}

{{-- <div style="page-break-after: always"></div> --}}
<br/>
<div style="border: 1px solid black; text-align: center; padding: 5px; background-color: black; color: white;">
    Basic Report Card
</div>

@foreach ($subjects as $subject)
    <?php
        $summativeAssessments = null;
        $formativeAssessments = null;
        $allSubjectFinalScores = array();

        if($printSummative == 1) {
            $summativeAssessments = SummativeAssessmentData::leftJoin('academic_summative_assessments', 'academic_summative_assessments.assessment_id', 'academic_summative_assessments_data.assessment_id')
                                  ->whereBetween('academic_summative_assessments.date', [$startDate, $endDate])
                                  ->where('academic_summative_assessments_data.student_id', $student)
                                  ->where('academic_summative_assessments.subject_id', $subject->subject_id)
                                  ->whereNotNull('academic_summative_assessments_data.score')
                                  ->where('academic_summative_assessments.academic_year_id', $academicYearId)
                                  ->get();
        }

        if($printFormative == 1) {
            $formativeAssessments = FormativeAssessmentData::select(
                                            'academic_formative_assessments.assessment_name as assessment_name',
                                            'academic_formative_assessments.t1 as tt1',
                                            'academic_formative_assessments.t2 as tt2',
                                            'academic_formative_assessments.t3 as tt3',
                                            'academic_formative_assessments.t4 as tt4',
                                            'academic_formative_assessments_data.t1 as t1',
                                            'academic_formative_assessments_data.t2 as t2',
                                            'academic_formative_assessments_data.t3 as t3',
                                            'academic_formative_assessments_data.t4 as t4',
                                            'academic_formative_assessments_data.type_id as type_id',
                                            'academic_formative_assessments_data.type_percentage as type_percentage'
                                          )
                                  ->leftJoin('academic_formative_assessments', 'academic_formative_assessments.formative_assessment_id', 'academic_formative_assessments_data.formative_assessment_id')
                                  ->whereBetween('academic_formative_assessments.date', [$startDate, $endDate])
                                  ->where('academic_formative_assessments_data.student_id', $student)
                                  ->where('academic_formative_assessments.subject_id', $subject->subject_id)
                                  ->where('academic_formative_assessments.academic_year_id', $academicYearId)
                                  ->get();
        }

        $subjectAverage = $reportHelper->calculateSubjectAverage([
                                'student_id'    => $student,
                                'subject_id'    => $subject->subject_id,
                                'start_date'    => $startDate,
                                'end_date'      => $endDate,
                                'academic_year_id'  => $academicYearId,
                                'print_summative'   => $printSummative,
                                'print_formative'   => $printFormative,
                                'summative_assessments' => $summativeAssessments,
                                'formative_assessments' => $formativeAssessments,
                            ]);

        $subjectAvgDetail = explode("/*/", $subjectAverage);
        $nAveragePoint = $subjectAvgDetail[0];
        $nNA = $subjectAvgDetail[1];
        $nLetter = $subjectAvgDetail[2];
        $nTypeData = $subjectAvgDetail[3];
        $totalAveragePoint = $nAveragePoint;

        if($subject->subject_type == 'en') {
            $englishSubjects .= '<tr>'.
                                    '<td>'.$subject->subject_name.'</td>'.
                                    '<td>'.$nAveragePoint.'</td>'.
                                '</tr>';
            $totalEnglishSum += $nAveragePoint;
            $totalEnglishCount++;
        } else if($subject->subject_type == 'kh') {
            $khmerSubjects .= '<tr>'.
                                    '<td>'.$subject->subject_name.'</td>'.
                                    '<td>'.$nAveragePoint.'</td>'.
                                '</tr>';
            $totalKhmerSum += $nAveragePoint;
            $totalKhmerCount++;
        }

        $nTypeDataArr = explode("|*|", $nTypeData);
        $nDynamicTypeData = "";

        $allTypes = ScoreTemplate::leftJoin('academic_summative_score_template_grades', 'academic_summative_score_template_grades.summative_template_id', 'academic_summative_score_template.summative_template_id')
                                    ->leftJoin('academic_summative_score_template_data', 'academic_summative_score_template_data.summative_template_id', 'academic_summative_score_template.summative_template_id')
                                    ->where('academic_summative_score_template.subject_id', $subject->subject_id)
                                    ->where('academic_summative_score_template_grades.grade_id', $subject->grade_id)
                                    ->get();

        $totalNACounter = 0;

        foreach ($allTypes as $key => $typ) {
            $tempTitle = $typ->title;
            // $tempTitle = str_replace(' ',"\n",$tempTitle);
            $tempPoint = "N\A";
            foreach ($nTypeDataArr as $key => $item) {
                if($item != "") {
                    $itemArr = explode("|", $item);
                    if ($typ->title == $itemArr[0]) {
                        $tempPoint = $itemArr[1];
                        break;
                    }
                }
            }

            if(is_numeric($tempPoint) ) { $tempPoint = round($tempPoint, 1); }

            $tempPointSubjectArr = array(
                'subject_id'    => $subject->subject_id,
                'subject_type'  =>   $subject->subject_type,
                'subject_point' => $tempPoint
            );
            array_push($allSubjectFinalScores, $tempPointSubjectArr);
        }
    ?>
@endforeach

<?php
    $englishAverage = 0;
    $khmerAverage = 0;
    if($totalEnglishCount > 0) {
        $englishAverage = $totalEnglishSum / $totalEnglishCount;
    }
    if($totalKhmerCount > 0) {
        $khmerAverage = $totalKhmerSum / $totalKhmerCount;
    }


?>


<br/>
<table style="width: 100%;">
    <?php
        $tableWidth = 50;

        if($totalKhmerCount == 0) {
            $tableWidth = 100;
        }
    ?>
    <tr>
        <td style="widtH: {{$tableWidth}}%;">
            <table class="assessmentListTable" style="width: 100%; border-collapse: collapse;" border="1">
                <thead style="background-color: rgba(222, 209, 209, 0.232)">
                    <tr>
                        <th colspan="2">English Subjects</th>
                    </tr>
                    <tr>
                        <th>Subject</th>
                        <th>Average</th>
                    </tr>
                </thead>
                <tbody>
                    <?php echo $englishSubjects; ?>
                    <tr style="background-color: rgb(169, 143, 143);">
                        <td style="font-weight: bold;">Average</td>
                        <td style="font-weight: bold;">{{round($englishAverage, 2)}}</td>
                    </tr>
                </tbody>
            </table>
        </td>
        @if($totalKhmerCount > 0)
        <td style="widtH: 50%;">
            <table class="assessmentListTable" style="width: 100%; border-collapse: collapse;" border="1">
                <thead style="background-color: rgba(222, 209, 209, 0.232)">
                    <tr>
                        <th colspan="2">Khmer Subjects</th>
                    </tr>
                    <tr>
                        <th>Subject</th>
                        <th>Average</th>
                    </tr>
                </thead>
                <tbody>
                    <?php echo $khmerSubjects; ?>
                    <tr style="background-color: rgb(169, 143, 143);">
                        <td style="font-weight: bold;">Average</td>
                        <td style="font-weight: bold;">{{round($khmerAverage, 2)}}</td>
                    </tr>
                </tbody>
            </table>
        </td>
        @endif
    </tr>
{{-- </table>
<hr/>

<div style="border: 1px solid black; text-align: center; padding: 5px; background-color: black; color: white;">
    BPS RECORDS ({{$startDate}}/{{$endDate}})
</div>
<br/>
<?php
    // $records = DisciplineRecord::where('student_id', $studentId)
    //                            ->whereBetween('date', [$startDate, $endDate])
    //                            ->where('status', 1)
    //                            ->get();
?>
<table class="assessmentListTable" style="width: 100%; border-collapse: collapse;" border="1">
    <thead style="background-color: rgba(222, 209, 209, 0.232)">
        <tr>
            <th style="min-width: 70px;">Date</th>
            <th>Teacher</th>
            <th style="min-width: 40px;">Type</th>
            <th>Title</th>
            <th style="min-width: 20px;"></th>
            <th>Note</th>
        </tr>
    </thead>
    <tbody>
        @foreach ($records as $record)
            <tr>
                <td class="center-text">{{$record->date}}</td>
                <td>{{$record->user->name ?? 'N/A'}}</td>
                <td>{{strtoupper($record->item_type)}}</td>
                <td>{{$record->item_title}}</td>
                <td class="center-text">{{$record->item_point}}</td>
                <td>{{$record->note}}</td>
            </tr>
        @endforeach
    </tbody>
</table> --}}

<div style="page-break-after: always"></div>
