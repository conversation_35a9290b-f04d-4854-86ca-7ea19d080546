<?php

use App\Models\StudentInformation;
use App\Models\ElectiveGrade;
use App\Models\SummativeAssessmentData;
use App\Models\FormativeAssessmentData;
use App\Library\Helper\ReportCardHelper;
use App\Models\ScoreTemplate;
use App\Models\UserBranch;
use App\Models\Comment;
use App\Models\StudentClassAttendance;
use App\Library\Helper\AcademicHelper;

$reportHelper = new ReportCardHelper();
// $getBranchInfo = new AcademicHelper();
// $branchInfo = $getBranchInfo->getBranchInfo();
$activeBranch = $branchId;
$info = StudentInformation::find($studentId);
$englishSubjects = null;
$totalEnglishSum = 0;
$totalEnglishCount = 0;

$subjects = ElectiveGrade::select('subjects.subject_id', 'subjects.subject_name', 'academic_elective_grade.grade_id', 'academic_elective_grade.grade_name', 'subjects.subject_type')->leftJoin('academic_elective_grade_students', 'academic_elective_grade.grade_id', 'academic_elective_grade_students.grade_id')->leftJoin('subjects', 'subjects.subject_id', 'academic_elective_grade.subject_id')->where('academic_elective_grade_students.student_id', $studentId)->where('academic_elective_grade.academic_year_id', $academicYearId)->groupBy('academic_elective_grade.subject_id', 'subjects.subject_id', 'subjects.subject_name', 'academic_elective_grade.grade_id')->get();

foreach ($subjects as $key => $subject) {
    foreach ($excluded as $ex) {
        if ($subject->subject_id == $ex->subject_id) {
            $subjects->forget($key);
        }
    }
}
?>
<div style=" padding: 10px;">
    <div style="text-align: center;"><img src="{{ $_SERVER['DOCUMENT_ROOT'] }}/img/{{ $activeBranch }}_logo.png"
            style="width: 250px;" /></div>
</div>

<div style="text-align: center; ">
    <h2>Subject report</h2>
</div>

<div style="border-bottom: 1px solid #0e284d;"></div>

<br />
<table style="width: 100%;">
    <tr>
        <td style="width: 75%;">
            <div class="table100 ver5">
                <div class="table100-body js-pscroll ps ps--active-y">
                    <table style="width: 90%;">
                        <tbody>
                            <tr>
                                <td class="cell100 column1 col1" style="border-left: 1px solid #0e284d!important;">Date
                                    Range</td>
                                <td class="cell100 column2 col2">{{ $startDate }}/{{ $endDate }}</td>
                            </tr>
                            <tr>
                                <td class="cell100 column1 col1">Student Name</td>
                                <td class="cell100 column2 col2">{{ $info->name }}</td>
                            </tr>
                            <tr>
                                <td class="cell100 column1 col1">Student No</td>
                                <td class="cell100 column2 col2">{{ $info->student_number }}</td>
                            </tr>
                            <tr>
                                <td class="cell100 column1 col1">School ID</td>
                                <td class="cell100 column2 col2">{{ $info->id }}</td>
                            </tr>
                            <tr>
                                <td class="cell100 column1 col1">Grade</td>
                                <td class="cell100 column2 col2">{{ $classroomName }}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </td>
        <td style="width: 25%;">
            <img src="{{ $_SERVER['DOCUMENT_ROOT'] . $info->photo }}" style="width: 100%; border-radius: 10px;" />
        </td>
    </tr>
</table>

<div style="border-bottom: 1px solid #0e284d;"></div>


<div style="text-align: center; padding-top: 20px">
    <h2>Grading System</h2>
</div>
{{-- <div style="text-align: center;"><h3>All school Grading system (International & Bilingual)</h3></div> --}}
<table style="width: 100%;border-collapse: collapse;" id="gradingTable">
    <thead>
        <tr>
            <th colspan="3" style="width: 48%; text-align: center;">GRADE DESCRIPTORS</th>
        </tr>
        <tr>
            <td>GRADE</td>
            <td>Percentage Range(%)</td>
            <td>Grade Descriptor</td>
        </tr>
        <tr>
            <td style="background-color: #fe6300;">A*</td>
            <td style="background-color: #fe6300;">90-100</td>
            <td style="background-color: #fe6300;">Student experienced excellent achievement</td>
        </tr>
        <tr>
            <td style="background-color: #d8d9d9;">A</td>
            <td style="background-color: #d8d9d9;">80-89</td>
            <td style="background-color: #d8d9d9;">Student experienced excellent achievement</td>
        </tr>
        <tr>
            <td style="background-color: #01bcb4;">B</td>
            <td style="background-color: #01bcb4;">70-79</td>
            <td style="background-color: #01bcb4;">Student experienced good achievement</td>
        </tr>
        <tr>
            <td style="background-color: #bb7405;">C</td>
            <td style="background-color: #bb7405;">60-69</td>
            <td style="background-color: #bb7405;">Student experienced satisfactory achievement</td>
        </tr>
        <tr>
            <td style="background-color: #f8969f;">D</td>
            <td style="background-color: #f8969f;">50-59</td>
            <td style="background-color: #f8969f;">Student experienced limited achievement</td>
        </tr>
        <tr>
            <td style="background-color: #5a4546;">E</td>
            <td style="background-color: #5a4546;">40-49</td>
            <td style="background-color: #5a4546;">Student experienced only minimal achievement</td>
        </tr>
        <tr>
            <td style="background-color: #00c7dd;">U</td>
            <td style="background-color: #00c7dd;">Ungraded / Not Yet Assessed</td>
            <td style="background-color: #00c7dd;"></td>
        </tr>
    </thead>
</table>
</br>
<table style="width: 100%;border-collapse: collapse;" id="gradingTable">
    <thead>
        <tr>
            <th colspan="2" style="width: 48%; text-align: center;">ACHIEVEMENT INDICATORS</th>
        </tr>
        <tr>
            <td>Indicator</td>
            <td>Descriptor</td>
        </tr>
        <tr>
            <td style="background-color: #00a7c2;">EE</td>
            <td style="background-color: #00a7c2;">Exceeds Expectation</td>
        </tr>
        <tr>
            <td style="background-color: #fd8c00;">ME</td>
            <td style="background-color: #fd8c00;">Meets Expectation</td>
        </tr>
        <tr>
            <td style="background-color: #fe3900;">AE</td>
            <td style="background-color: #fe3900;">Approaches Expectation</td>
        </tr>
        <tr>
            <td style="background-color: #52c027;">BE</td>
            <td style="background-color: #52c027;">Below Expectation</td>
        </tr>
        <tr>
            <td style="background-color: #8d5dad;">N\A</td>
            <td style="background-color: #8d5dad;">Not applicable at this time</td>
        </tr>
    </thead>
</table>

<br /><br />
<div style="border-bottom: 1px solid #0e284d;"></div><br />

<table style="width: 100%;">
    <tr>
        <td style="width: 50%; text-align: left;">

        </td>
        <td style="width: 50%; text-align: right;">
            RC-U-ID<br />
            {{ strtoupper($uid) }}
        </td>
    </tr>
</table>

<div style="page-break-after: always"></div>

@foreach ($subjects as $subject)
    <?php
    $summativeAssessments = null;
    $formativeAssessments = null;
    $subjectComment = null;
    $allSubjectFinalScores = [];
    
    if ($printSummative == 1) {
        $summativeAssessments = SummativeAssessmentData::leftJoin('academic_summative_assessments', 'academic_summative_assessments.assessment_id', 'academic_summative_assessments_data.assessment_id')
            ->leftJoin('skills_strands', 'skills_strands.skill_strand_id', 'academic_summative_assessments.strand')
            ->whereBetween('academic_summative_assessments.date', [$startDate, $endDate])
            ->where('academic_summative_assessments_data.student_id', $student)
            ->whereNotNull('academic_summative_assessments_data.score')
            ->where('academic_summative_assessments.subject_id', $subject->subject_id)
            ->where('academic_summative_assessments.grade_id', $subject->grade_id)
            ->where('academic_summative_assessments.academic_year_id', $academicYearId)
            ->get();
    }
    
    if ($printFormative == 1) {
        $formativeAssessments = FormativeAssessmentData::select('academic_formative_assessments.assessment_name as assessment_name', 'academic_formative_assessments.t1 as tt1', 'academic_formative_assessments.t2 as tt2', 'academic_formative_assessments.t3 as tt3', 'academic_formative_assessments.t4 as tt4', 'academic_formative_assessments_data.t1 as t1', 'academic_formative_assessments_data.t2 as t2', 'academic_formative_assessments_data.t3 as t3', 'academic_formative_assessments_data.t4 as t4', 'academic_formative_assessments_data.type_id as type_id', 'academic_formative_assessments_data.type_percentage as type_percentage', 'skills_strands.value as skill', 'str.value as strand')
            ->leftJoin('academic_formative_assessments', 'academic_formative_assessments.formative_assessment_id', 'academic_formative_assessments_data.formative_assessment_id')
            ->leftJoin('skills_strands', 'skills_strands.skill_strand_id', 'academic_formative_assessments.skill')
            ->leftJoin('skills_strands as str', 'str.skill_strand_id', 'academic_formative_assessments.strand')
            ->whereBetween('academic_formative_assessments.date', [$startDate, $endDate])
            ->where('academic_formative_assessments_data.student_id', $student)
            ->where('academic_formative_assessments.subject_id', $subject->subject_id)
            ->where('academic_formative_assessments.academic_year_id', $academicYearId)
            ->get();
    }
    
    if ($printComments == 1) {
        $subjectComment = Comment::where('student_id', $student)
            ->where('subject_id', $subject->grade_id)
            ->where('academic_year_id', $academicYearId)
            ->whereBetween('comment_date', [$startDate, $endDate])
            ->orderBy('comment_date', 'DESC')
            ->get();
    }
    
    // if ($printSubjectAttendance == 1) {
    //     $classPresent = StudentClassAttendance::whereBetween('date', [$startDate, $endDate])
    //         ->where('student_id', $student)
    //         ->where('attendance_status', 'present')
    //         ->where('subject_id', $subject->subject_id)
    //         ->where('academic_year_id', $academicYearId)
    //         ->count();
    
    //     $classLate = StudentClassAttendance::whereBetween('date', [$startDate, $endDate])
    //         ->where('student_id', $student)
    //         ->where('attendance_status', 'late')
    //         ->where('subject_id', $subject->subject_id)
    //         ->where('academic_year_id', $academicYearId)
    //         ->count();
    
    //     $classAbsent = StudentClassAttendance::whereBetween('date', [$startDate, $endDate])
    //         ->where('student_id', $student)
    //         ->where('attendance_status', 'absent')
    //         ->where('subject_id', $subject->subject_id)
    //         ->where('academic_year_id', $academicYearId)
    //         ->count();
    // }
    if ($printSubjectAttendance == 1) {
        $attendanceCounts = StudentClassAttendance::whereBetween('date', [$startDate, $endDate])
            ->where('student_id', $student)
            ->where('subject_id', $subject->subject_id)
            ->where('academic_year_id', $academicYearId)
            ->selectRaw(
                "
                                SUM(CASE WHEN attendance_status = 'present' THEN 1 ELSE 0 END) as classPresent,
                                SUM(CASE WHEN attendance_status = 'late' THEN 1 ELSE 0 END) as classLate,
                                SUM(CASE WHEN attendance_status = 'absent' THEN 1 ELSE 0 END) as classAbsent
                            ",
            )
            ->first();
    
        $classPresent = $attendanceCounts->classPresent;
        $classLate = $attendanceCounts->classLate;
        $classAbsent = $attendanceCounts->classAbsent;
    }
    
    $subjectAverage = $reportHelper->calculateSubjectAverage([
        'student_id' => $student,
        'subject_id' => $subject->subject_id,
        'start_date' => $startDate,
        'end_date' => $endDate,
        'academic_year_id' => $academicYearId,
        'print_summative' => $printSummative,
        'print_formative' => $printFormative,
        'summative_assessments' => $summativeAssessments,
        'formative_assessments' => $formativeAssessments,
    ]);
    
    $subjectAvgDetail = explode('/*/', $subjectAverage);
    $nAveragePoint = $subjectAvgDetail[0];
    $nNA = $subjectAvgDetail[1];
    $nLetter = $subjectAvgDetail[2];
    $nTypeData = $subjectAvgDetail[3];
    $totalAveragePoint = $nAveragePoint;
    $englishSubjects .= '<tr>' . '<td>' . $subject->subject_name . ' - ' . $subject->grade_name . '</td>' . '<td>' . $nAveragePoint . '</td>' . '</tr>';
    $totalEnglishSum += $nAveragePoint;
    $totalEnglishCount++;
    
    $nTypeDataArr = explode('|*|', $nTypeData);
    $nDynamicTypeData = '';
    
    $allTypes = ScoreTemplate::leftJoin('academic_summative_score_template_grades', 'academic_summative_score_template_grades.summative_template_id', 'academic_summative_score_template.summative_template_id')->leftJoin('academic_summative_score_template_data', 'academic_summative_score_template_data.summative_template_id', 'academic_summative_score_template.summative_template_id')->where('academic_summative_score_template.subject_id', $subject->subject_id)->where('academic_summative_score_template_grades.grade_id', $subject->grade_id)->get();
    
    $totalNACounter = 0;
    ?>

    <div style="border: 1px solid black; text-align: center; padding: 5px; background-color: white; color: black;">
        {{ $subject->subject_name }} - {{ $subject->grade_name }}
    </div>

    <br />

    <!-- SUMMATIVE ASSESSMENT TABLE -->
    @if ($printSummative == 1)
        <table class="assessmentListTable" style="width: 100%; border-collapse: collapse;" border="1">
            <thead style="background-color: rgba(222, 209, 209, 0.232);">
                <tr>
                    <th colspan="8" style="padding: 5px;">ASSESSMENTS</th>
                </tr>
                <tr>
                    <th>Assessment</th>
                    <th>Strand</th>
                    <th>Type</th>
                    <th>Impact</th>
                    <th>Date</th>
                    <th>Score</th>
                    <th>Max. Score</th>
                    <th>Grade</th>
                </tr>
            </thead>
            <tbody>
                @foreach ($summativeAssessments as $sa)
                    <tr>
                        <td
                            style="{{ $subject->subject_type == 'kh' ? 'font-family: Khmer; font-size: 8px; color: black;' : ' color: black;' }}">
                            {{ $sa->assessment_name }}</td>
                        <td
                            style="{{ $subject->subject_type == 'kh' ? 'font-family: Khmer; font-size: 8px; color: black;' : ' color: black;' }}">
                            {{ $sa->value }}</td>
                        <td style="color: black;">{{ $sa->type_title }}</td>
                        <td>{{ $sa->type_percentage }}%</td>
                        <td>{{ $sa->date }}</td>
                        <td>{{ $sa->score }}</td>
                        <td>{{ $sa->max_score }}</td>
                        <td>{{ $reportHelper->getLetterGrade($sa->score_percentage, 100) }}</td>
                    </tr>
                @endforeach
            </tbody>
        </table>
    @endif

    <!--
                    FORMATIVE ASSESSMENT TABLE
                -->
    @if ($printFormative == 1)
        <br />
        <table class="assessmentListTable" style="width: 100%; border-collapse: collapse;" border="1">
            <thead style="background-color: rgba(222, 209, 209, 0.232)">
                <tr>
                    <th colspan="7" style="padding: 5px;">LIFE SKILLS</th>
                </tr>
                <tr>
                    <th>Name</th>
                    <th>Skill</th>
                    <th>Strand</th>
                    <th>EE</th>
                    <th>ME</th>
                    <th>AE</th>
                    <th>BE</th>
                </tr>
            </thead>
            <tbody>
                @foreach ($formativeAssessments as $fa)
                    <?php
                    $t1Text = null;
                    $t2Text = null;
                    $t3Text = null;
                    $t4Text = null;
                    
                    if ($fa->t1 != '') {
                        $t1Text = is_numeric($fa->t1) ? $fa->t1 : '+';
                    }
                    if ($fa->t2 != '') {
                        $t2Text = is_numeric($fa->t2) ? $fa->t2 : '+';
                    }
                    if ($fa->t3 != '') {
                        $t3Text = is_numeric($fa->t3) ? $fa->t3 : '+';
                    }
                    if ($fa->t4 != '') {
                        $t4Text = is_numeric($fa->t4) ? $fa->t4 : '+';
                    }
                    ?>
                    <tr>
                        <td
                            style="{{ $subject->subject_type == 'kh' ? 'font-family: Khmer; font-size: 8px; color: black;' : ' color: black;' }}">
                            {{ $fa->assessment_name }}</td>
                        <td>{{ $fa->skill }}</td>
                        <td>{{ $fa->strand }}</td>
                        <td style="font-weight: bold;">{{ $t1Text }}</td>
                        <td style="font-weight: bold;">{{ $t2Text }}</td>
                        <td style="font-weight: bold;">{{ $t3Text }}</td>
                        <td style="font-weight: bold;">{{ $t4Text }}</td>
                    </tr>
                @endforeach

            </tbody>
        </table>
    @endif

    @if ($printComments == 1)
        <br />
        <table class="commentTable" style="width: 100%; border-collapse: collapse;" border="1">
            <thead>
                <tr>
                    <th colspan="2" style="background-color: rgba(222, 209, 209, 0.232)">SUBJECT COMMENTS</th>
                </tr>
                <tr>
                    <th style="text-align: center;">Date</th>
                    <th style="text-align: center;">Comment</th>
                </tr>
            </thead>
            <tbody>
                @foreach ($subjectComment as $comment)
                    <tr>
                        <td style="padding-left: 10px;">{{ $comment->comment_date }}</td>
                        <td style="padding-left: 10px;">{{ $comment->comment }}</td>
                    </tr>
                @endforeach
            </tbody>
        </table>
    @endif

    @if ($printSubjectAttendance == 1)
        <br />
        <table class="assessmentListTable" style="width: 100%; border-collapse: collapse;" border="1">
            <thead style="background-color: rgba(222, 209, 209, 0.232)">
                <tr>
                    <th colspan="3" style="padding: 5px;">IN CLASS ATTENDANCE</th>
                </tr>
                <tr>
                    <th>Present</th>
                    <th>Late</th>
                    <th>Absent</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>{{ $classPresent }}</td>
                    <td>{{ $classLate }}</td>
                    <td>{{ $classAbsent }}</td>
                </tr>
            </tbody>
        </table>
    @endif

    {{-- <div style="page-break-after: always"></div> --}}
    <br />
    <br />
@endforeach

@if ($printLibrary == 1)
    @include('partial_view.generate_lists.report_card.detail_parts.page_library', [
        'studentId' => $studentId,
        'startDate' => $startDate,
        'endDate' => $endDate,
    ])
@endif

@if ($printBps == 1)
    @include('partial_view.generate_lists.report_card.detail_parts.page_bps', [
        'studentId' => $studentId,
        'startDate' => $startDate,
        'endDate' => $endDate,
    ])
@endif
