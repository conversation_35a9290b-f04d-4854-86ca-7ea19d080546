<?php

use App\Models\StudentInformation;
use App\Models\ElectiveGrade;
use App\Models\SummativeAssessmentData;
use App\Models\FormativeAssessmentData;
use App\Library\Helper\ReportCardHelper;
use App\Models\ScoreTemplate;
use App\Models\UserBranch;
use App\Models\StudentClassAttendance;
use App\Library\Helper\AcademicHelper;
use App\Models\AcademicSemester;
use App\Models\Comment;
use App\Models\SettingsAssessment;
use App\Models\AcademicYear;

$reportHelper = new ReportCardHelper();
$getAcademicInfo = new AcademicHelper();
$branchInfo = $getAcademicInfo->getBranchInfo();
// $acYearInfo = $getAcademicInfo->getAcademicYearInfo();
$acYearInfo = AcademicYear::find($academicYearId);
$activeBranch = $branchInfo->branch_id;
$resultSubjects = [];
//UserBranch::where('user_id',\Auth::user()->id)->where('is_active',1)->first();
$info = StudentInformation::find($studentId);

$semesterInfo = AcademicSemester::where('start_date', '<=', $endDate)->where('end_date', '>=', $endDate)->where('branch_id', $activeBranch)->first();

$englishSubjects = null;
$totalEnglishSum = 0;
$totalEnglishCount = 0;

$subjects = ElectiveGrade::select('subjects.subject_id', 'subjects.subject_name', 'academic_elective_grade.grade_id', 'subjects.subject_type')
->leftJoin('academic_elective_grade_students', 'academic_elective_grade.grade_id', 'academic_elective_grade_students.grade_id')
->leftJoin('subjects', 'subjects.subject_id', 'academic_elective_grade.subject_id')
->where('academic_elective_grade_students.student_id', $studentId)
->where('academic_elective_grade.academic_year_id', $academicYearId)
->groupBy('academic_elective_grade.subject_id', 'subjects.subject_id', 'subjects.subject_name')
->get();
//                                            'academic_elective_grade.grade_id'
$HR_gradeId = 0;

foreach ($subjects as $key => $subject) {
    $notExcluded = true;
    foreach ($excluded as $ex) {
        if ($subject->subject_id == 116) {
            $HR_gradeId = $subject->grade_id;
            // $notExcluded = false;
        }
        if ($subject->subject_id == $ex->subject_id) {
            $subjects->forget($key);
            $notExcluded = false;
        }
    }
    if ($notExcluded) {
        $resultSubjects[$subject->subject_id]['name'] = $subject->subject_name;
        $subjectGrades = '';
        $formativeAssessments = [];
        $allSubjectFinalScores = [];
        $allSubjectsLifeSkillGrades = [];
        if ($subject->subject_id != 253) {
            $lifeSkillTypes = SettingsAssessment::leftJoin('skills_strands', 'skills_strands.skill_strand_id', 'settings_assessment.skill_strand_id')->where('grade_id', $subject->grade_id)->where('type', 'skill')->where('term', $semesterInfo->academic_semester)->groupBy('skills_strands.skill_strand_id')->get();
        }
        $skills = [];
        $subjectLifeSkills = [];
        if ($printSummative == 1) {
            $subjectGrades = $reportHelper->calculateSubjectSummativeStrand([
                'student_id' => $student,
                'subject_id' => $subject->subject_id,
                'start_date' => $startDate,
                'end_date' => $endDate,
                'academic_year_id' => $academicYearId,
                'grade_id' => $subject->grade_id,
            ]);
            $subjectAvgDetail[$subject->subject_id] = explode('/*/', $subjectGrades);
            $strandLines[$subject->subject_id] = explode('/x/', $subjectAvgDetail[$subject->subject_id][0]);
        }
        if ($printFormative == 1) {
        }

        if ($printSubjectAttendance == 1) {
            $attendanceCounts = StudentClassAttendance::whereBetween('date', [$startDate, $endDate])
                ->where('student_id', $student)
                ->where('subject_id', $subject->subject_id)
                ->where('academic_year_id', $academicYearId)
                ->selectRaw(
                    "
                        SUM(CASE WHEN attendance_status = 'present' THEN 1 ELSE 0 END) as classPresent,
                        SUM(CASE WHEN attendance_status = 'late' THEN 1 ELSE 0 END) as classLate,
                        SUM(CASE WHEN attendance_status = 'absent' THEN 1 ELSE 0 END) as classAbsent
                    ",
                )
                ->first();

            $classPresent = $attendanceCounts->classPresent;
            $classLate = $attendanceCounts->classLate;
            $classAbsent = $attendanceCounts->classAbsent;
        }
    }
}

$HR_Comment = Comment::where('student_id', $student)->where('subject_id', $HR_gradeId)->where('academic_year_id', $academicYearId)->where('term', $semesterInfo->academic_semester)->orderBy('comment_date', 'DESC')->first();
$comment = '';
if ($HR_Comment) {
    $comment = $HR_Comment->comment;
}
?>

<page size="A4" style="width: 95%; height: 95%;">
    <div style="text-align: center; padding-top: 70px;"><img
            src="{{ $_SERVER['DOCUMENT_ROOT'] }}/img/{{ $activeBranch }}_logo.png" style="width: 250px;" /></div>
    <div style="text-align: center; padding-top: 5px;">
        @if ($info->photo != '')
            <img src="{{ $_SERVER['DOCUMENT_ROOT'] . $info->photo }}" style="width: 200px; border-radius: 15px;" />
        @endif
    </div>
    <div style="text-align: center;">
        <h3>School Year: {{ $acYearInfo->academic_year }}</h3>
    </div>
    <div style="text-align: center;">
        <h3>Term-{{ $semesterInfo->academic_semester }}</h3>
    </div>
    <div style="text-align: center;">
        <h1 style="font-weight: bold;">{{ $info->name }}</h1>
    </div>
    <div style="text-align: center;">
        <h3 style="font-weight: bold;">{{ $classroomName }}</h3>
    </div>
    <div style="text-align: center;">
        <h3 style="font-weight: bold;">{{ $info->student_number }} / {{ $info->id }}</h3>
    </div>
    <div style="text-align: center;">
        <h3>Date Range: {{ $startDate }}/{{ $endDate }} </h3>
    </div>
    <div style="text-align: center;">{{ $branchInfo->branch_website }}</div>
</page>

<page size="A4">
    <div style="text-align: center; padding-top: 50px;">
        <h1>Grading System</h1>
    </div>

    <table style="width: 90%;border-collapse: collapse;" class="gradingTable">
        <thead>
            <tr>
                <th colspan="3" style="width: 48%; text-align: center;">GRADE DESCRIPTORS</th>
            </tr>
            <tr>
                <td>GRADE</td>
                <td>Percentage Range(%)</td>
                <td>Grade Descriptor</td>
            </tr>
            <tr>
                <td style="background-color: #fe6300;">A*</td>
                <td style="background-color: #fe6300;">90-100</td>
                <td style="background-color: #fe6300;">Student experienced excellent achievement</td>
            </tr>
            <tr>
                <td style="background-color: #d8d9d9;">A</td>
                <td style="background-color: #d8d9d9;">80-89</td>
                <td style="background-color: #d8d9d9;">Student experienced excellent achievement</td>
            </tr>
            <tr>
                <td style="background-color: #01bcb4;">B</td>
                <td style="background-color: #01bcb4;">70-79</td>
                <td style="background-color: #01bcb4;">Student experienced good achievement</td>
            </tr>
            <tr>
                <td style="background-color: #bb7405;">C</td>
                <td style="background-color: #bb7405;">60-69</td>
                <td style="background-color: #bb7405;">Student experienced satisfactory achievement</td>
            </tr>
            <tr>
                <td style="background-color: #f8969f;">D</td>
                <td style="background-color: #f8969f;">50-59</td>
                <td style="background-color: #f8969f;">Student experienced limited achievement</td>
            </tr>
            <tr>
                <td style="background-color: #5a4546;">E</td>
                <td style="background-color: #5a4546;">40-49</td>
                <td style="background-color: #5a4546;">Student experienced only minimal achievement</td>
            </tr>
            <tr>
                <td style="background-color: #00c7dd;">U</td>
                <td style="background-color: #00c7dd;">Ungraded / Not Yet Assessed</td>
                <td style="background-color: #00c7dd;"></td>
            </tr>
        </thead>
    </table>

    <table style="width: 90%;border-collapse: collapse;" class="gradingTable">
        <thead>
            <tr>
                <th colspan="2" style="width: 48%; text-align: center;">ACHIEVEMENT INDICATORS</th>
            </tr>
            <tr>
                <td>Indicator</td>
                <td>Descriptor</td>
            </tr>
            <tr>
                <td style="background-color: #00a7c2;">EE</td>
                <td style="background-color: #00a7c2;">Exceeds Expectation</td>
            </tr>
            <tr>
                <td style="background-color: #fd8c00;">ME</td>
                <td style="background-color: #fd8c00;">Meets Expectation</td>
            </tr>
            <tr>
                <td style="background-color: #fe3900;">AE</td>
                <td style="background-color: #fe3900;">Approaches Expectation</td>
            </tr>
            <tr>
                <td style="background-color: #52c027;">BE</td>
                <td style="background-color: #52c027;">Below Expectation</td>
            </tr>
            <tr>
                <td style="background-color: #8d5dad;">N\A</td>
                <td style="background-color: #8d5dad;">Not applicable at this time</td>
            </tr>
        </thead>
    </table>
</page>

<page size="A4">
    <table class="listTable" style="padding-top: 70px;">
        <thead>
            <tr>
                <th colspan="2" style="text-align: center; background-color: #f0f0f0;">
                    <p>SUBJECT FINAL GRADE</p>
                </th>
            </tr>
            <tr>
                <th width="80%" style="text-align: left;"> Subjects</th>
                <th width="20%" style="text-align: center;"> Grade</th>
            </tr>
        </thead>
        <tbody>
            @foreach ($resultSubjects as $key => $subject)
                <tr>
                    <td>{{ $subject['name'] }}</td>
                    <td width="20%" style="text-align: center; font-weight: bold;">
                        {{-- {{$reportHelper->getLetterGrade($subjectAvgDetail[$key][1])}}</td> --}}
                        {{ $subjectAvgDetail[$key][1] }}
                    </td>
                </tr>
            @endforeach
        </tbody>
    </table>
    </br>
    <table style="width: 90%;" class="listTable">
        <thead>
            <tr>
                <th style="text-align: center; background-color: #f0f0f0;">
                    <p>Homeroom Teacher`s Comment</p>
                </th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td style="font-size:1.1em">{{ $comment }} </td>
            </tr>
        </tbody>
    </table>
    </br>

    <table style="width: 90%;">
        <tr style="height:200px"></tr>
        <tr colspan="4">
            <td width="30%" style="text-align: left; padding-left:20px;">
                <img src="{{ $_SERVER['DOCUMENT_ROOT'] }}/img/teacherSignatures/{{ $hrTeacherId }}.png"
                    style="width: 150px;" />
            </td>
            </td>
            <td></td>
            <td></td>
            <td width="30%" style="text-align: right; padding-right:20px;">
                <img src="{{ $_SERVER['DOCUMENT_ROOT'] }}/img/principlesignatures/{{ $branchInfo->branch_principal }}.jpg"
                    style="width: 150px;" />
            </td>
        </tr>
        <tr colspan="2">
            <td width="30%" style="text-align: center; font-size: 15px;">Homeroom Teacher</td>
            <td></td>
            <td></td>
            <td width="30%" style="text-align: center; font-size: 15px;">Head of School</td>
        </tr>
    </table>
</page>

<page size="A4">
    <table class="listTable" style="padding-top: 70px;">
        <thead>
            @if (isset($lifeSkillTypes))
                <tr>
                    <th colspan="{{ 1 + count($lifeSkillTypes) }}"
                        style="text-align: center; background-color: #f0f0f0;">
                        <p>Overview Life Skills</p>
                    </th>
                </tr>
                <tr>
                    <th style="text-align: center; width: 30%">Subjects</th>

                    @foreach ($lifeSkillTypes as $lifeSkill)
                        <th style="text-align: center; width: {{ 70 / count($lifeSkillTypes) }}%">
                            {{ $lifeSkill->value }}</th>
                        <?php
                        $skills[] = $lifeSkill->skill_strand_id;
                        foreach ($resultSubjects as $key => $subject) {
                            $subjectLifeSkills[$key][$lifeSkill->skill_strand_id] = '';
                            $lifeSkillAssessments = FormativeAssessmentData::selectRaw(
                                'skill,
                                                                    academic_formative_assessments.assessment_name as assessment_name,
                                                                    academic_formative_assessments_data.t1 as t1,
                                                                    academic_formative_assessments_data.t2 as t2,
                                                                    academic_formative_assessments_data.t3 as t3,
                                                                    academic_formative_assessments_data.t4 as t4,
                                                                    academic_formative_assessments_data.type_id as type_id,
                                                                    academic_formative_assessments_data.type_percentage as type_percentage',
                            )
                                ->leftJoin('academic_formative_assessments', 'academic_formative_assessments.formative_assessment_id', 'academic_formative_assessments_data.formative_assessment_id')
                                ->whereBetween('academic_formative_assessments.date', [$startDate, $endDate])
                                ->where('academic_formative_assessments.skill', $lifeSkill->skill_strand_id)
                                ->where('academic_formative_assessments_data.student_id', $student)
                                ->where('academic_formative_assessments.subject_id', $key)
                                ->where('academic_formative_assessments.academic_year_id', $academicYearId)
                                ->first();
                            //                                CONCAT(academic_formative_assessments_data.t1,academic_formative_assessments_data.t2,academic_formative_assessments_data.t3,academic_formative_assessments_data.t4) as skill_grade'
                            $subjectLifeSkills[$key][$lifeSkill->skill_strand_id] = null;
                            if ($lifeSkillAssessments) {
                                if ($lifeSkillAssessments->t1 != '') {
                                    $subjectLifeSkills[$key][$lifeSkill->skill_strand_id] = 'EE';
                                }
                                if ($lifeSkillAssessments->t2 != '') {
                                    $subjectLifeSkills[$key][$lifeSkill->skill_strand_id] = 'ME';
                                }
                                if ($lifeSkillAssessments->t3 != '') {
                                    $subjectLifeSkills[$key][$lifeSkill->skill_strand_id] = 'AE';
                                }
                                if ($lifeSkillAssessments->t4 != '') {
                                    $subjectLifeSkills[$key][$lifeSkill->skill_strand_id] = 'BE';
                                }
                            }
                        }
                        ?>
                    @endforeach
                </tr>
            @endif
        </thead>
        <tbody>
            @foreach ($resultSubjects as $key => $subject)
                <tr>

                    <td>{{ $subject['name'] }}</td>
                    @foreach ($skills as $skill_id)
                        @if ($subjectLifeSkills[$key][$skill_id])
                            <td width="20%" style="text-align: center; font-weight: bold;">
                                {{ $subjectLifeSkills[$key][$skill_id] }}

                            </td>
                        @else
                            <td width="20%" style="text-align: center; font-weight: bold;"></td>
                        @endif
                    @endforeach
                </tr>
            @endforeach
        </tbody>

    </table>
    </br>
</page>
@foreach ($resultSubjects as $key => $subject)
    <page size="A4">
        <div style="font-size: 10px; right: 0; position: absolute; "></div>
        <div class="subject_title" style="padding-top: 70px;">
            <table class="listTable" style="width: 90%;">
                <thead>
                    <tr>
                        <th style="text-align: left; background-color: #ffffff;"><small>Student:
                                {{ $info->student_number }} /
                                {{ $info->id }} </small></th>
                        <th style="text-align: left; background-color: #ffffff;"><small>Subject:
                                {{ $subject['name'] }}
                            </small></th>
                    </tr>
                </thead>
            </table>
            <br />

            <!-- SUMMATIVE ASSESSMENT TABLE -->
            @if ($printSummative == 1)
                <div style="text-align: center;">
                    <table class="listTable">
                        <thead>
                            <tr>
                                <th colspan="2" style="text-align: center; background-color: #f0f0f0;">
                                    <p>STRAND</p>
                                </th>
                            </tr>
                            <tr>
                                <th width="80%" style="text-align: left;"> Strand</th>
                                <th width="20%" style="text-align: center;">Grade</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                    foreach ($strandLines[$key] as $strRow){
                        $straDetail = explode("/_/", $strRow);
                        if ($straDetail[0] != ""){
                            ?>
                            <tr>
                                <td>{{ $straDetail[0] }}</td>
                                <td width="20%" style="text-align: center; font-weight: bold;">
                                    {{ $straDetail[1] }}</td>
                            </tr>
                            <?php
                        }
                    }
                ?>

                        </tbody>
                        <tfoot>
                            <tr>
                                <td style="font-weight:bold ">Final Grade</td>
                                <td width="20%" style="text-align: center; font-weight: bold;">
                                    {{-- {{$reportHelper->getLetterGrade($subjectAvgDetail[$key][1])}} --}}
                                    {{ $subjectAvgDetail[$key][1] }}
                                </td>
                            </tr>
                        </tfoot>
                    </table>
                </div>
                <br />
            @endif

            <!--
        FORMATIVE ASSESSMENT TABLE
    -->
            @if ($printFormative == 1)
                <table class="listTable" style="width: 90%; border-collapse: collapse;" border="1">
                    <thead style="background-color: rgba(222, 209, 209, 0.232)">
                        <tr>
                            <th colspan="6" style="text-align: center;">LIFE SKILLS</th>
                        </tr>
                        <tr>
                            <th>Assessment</th>
                            <th>Skill</th>
                            <th style="text-align: center">EE</th>
                            <th style="text-align: center">ME</th>
                            <th style="text-align: center">AE</th>
                            <th style="text-align: center">BE</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        $formativeAssessments = FormativeAssessmentData::select('academic_formative_assessments.assessment_name as assessment_name', 'academic_formative_assessments.t1 as tt1', 'academic_formative_assessments.t2 as tt2', 'academic_formative_assessments.t3 as tt3', 'academic_formative_assessments.t4 as tt4', 'academic_formative_assessments_data.t1 as t1', 'academic_formative_assessments_data.t2 as t2', 'academic_formative_assessments_data.t3 as t3', 'academic_formative_assessments_data.t4 as t4', 'academic_formative_assessments_data.type_id as type_id', 'academic_formative_assessments_data.type_percentage as type_percentage', 'skills_strands.value as skill', 'str.value as strand')
                            ->leftJoin('academic_formative_assessments', 'academic_formative_assessments.formative_assessment_id', 'academic_formative_assessments_data.formative_assessment_id')
                            ->leftJoin('skills_strands', 'skills_strands.skill_strand_id', 'academic_formative_assessments.skill')
                            ->leftJoin('skills_strands as str', 'str.skill_strand_id', 'academic_formative_assessments.strand')
                            ->whereBetween('academic_formative_assessments.date', [$startDate, $endDate])
                            ->where('academic_formative_assessments_data.student_id', $student)
                            ->where('academic_formative_assessments.subject_id', $key)
                            ->where('academic_formative_assessments.academic_year_id', $academicYearId)
                            ->get();
                        
                        ?>
                        @foreach ($formativeAssessments as $fa)
                            <?php
                            $t1Text = null;
                            $t2Text = null;
                            $t3Text = null;
                            $t4Text = null;
                            
                            if ($fa->t1 != '') {
                                $t1Text = is_numeric($fa->t1) ? $fa->t1 : '+';
                            }
                            if ($fa->t2 != '') {
                                $t2Text = is_numeric($fa->t2) ? $fa->t2 : '+';
                            }
                            if ($fa->t3 != '') {
                                $t3Text = is_numeric($fa->t3) ? $fa->t3 : '+';
                            }
                            if ($fa->t4 != '') {
                                $t4Text = is_numeric($fa->t4) ? $fa->t4 : '+';
                            }
                            ?>
                            <tr>
                                <td style="">{{ $fa->assessment_name }}</td>
                                <td>{{ $fa->skill }}</td>
                                <td style="text-align: center; font-weight: bold;">{{ $t1Text }}</td>
                                <td style="text-align: center; font-weight: bold;">{{ $t2Text }}</td>
                                <td style="text-align: center; font-weight: bold;">{{ $t3Text }}</td>
                                <td style="text-align: center; font-weight: bold;">{{ $t4Text }}</td>
                            </tr>
                        @endforeach

                    </tbody>
                </table>
            @endif
            @if ($printSubjectAttendance == 1)
                <br />
                <table class="listTable" style="width: 90%; border-collapse: collapse;" border="1">
                    <thead style="background-color: rgba(222, 209, 209, 0.232)">
                        <tr>
                            <th colspan="3" style="text-align: center">IN CLASS ATTENDANCE</th>
                        </tr>
                        <tr>
                            <th style="text-align: center">Present</th>
                            <th style="text-align: center">Late</th>
                            <th style="text-align: center">Absent</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>{{ $classPresent }}</td>
                            <td>{{ $classLate }}</td>
                            <td>{{ $classAbsent }}</td>
                        </tr>
                    </tbody>
                </table>
            @endif
    </page>
@endforeach

@if ($printLibrary == 1)
    @include('partial_view.generate_lists.report_card.detail_parts.page_library', [
        'studentId' => $studentId,
        'startDate' => $startDate,
        'endDate' => $endDate,
    ])
@endif

@if ($printBps == 1)
    @include('partial_view.generate_lists.report_card.detail_parts.page_bps', [
        'studentId' => $studentId,
        'startDate' => $startDate,
        'endDate' => $endDate,
    ])
@endif
