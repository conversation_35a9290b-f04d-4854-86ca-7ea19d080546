<style>
    table {
        width: 100%;
        border-collapse: collapse;
        margin: 20px 0;
    }

    th,
    td {
        padding: 10px;
        solid #000;
        text-align: left;
    }

    th {
        background-color: #f2f2f2;
        font-weight: bold;
    }

    .header-logo-wrapper {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-bottom: 20px;
    }

    .header-logo-wrapper img {
        width: 180px;
    }

    .header-title {
        text-align: center;
        font-size: 18px;
        font-weight: bold;
        margin-top: 10px;
    }


    /* Container for logos */
    .logo-container {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .logo {
        width: 800px;
    }

    /* Page header */
    .header {
        text-align: center;
        padding: 20px 0;
    }

    .page-break {
        page-break-after: always;
    }

    @media print {
            #printButton {
                display: none;
            }
    }
</style>

@php
use App\Models\StudentInformation;
use App\Models\StudentMeasurements;
$info = StudentInformation::find($studentId);
$birthDate = new DateTime($info->birth_date);
$age = (new DateTime())->diff($birthDate)->y;

$measurements = StudentMeasurements::where('student_id', $studentId)
    ->where('branch_id', $branchId)
    ->where('academic_year_id', $academicYearId)
    ->orderBy('date')  // Ensure records are ordered by date.
    ->get();

    @endphp

<div class="header-logo-wrapper">
    <img src="/img/logo.png" />
    <div class="header-title">KINDERGARTEN CAMPUS</div>
    <div class="header-title">KINDERGARTEN REPORT CARD</div>
</div>


<div class="print">
        <button type="button" id="printButton">Print Report</button>
        <script>
            document.getElementById('printButton').addEventListener('click', function () {
                window.print();
            });        
        </script>
    
    </div>
    {{-- Student Information --}}
    <table style="width: 100%;">
        <tr>
            <td style="width: 75%;">
                <div class="table100 ver5">
                    <div class="table100-body js-pscroll ps ps--active-y">
                    <table style="width: 90%;">
                            <tbody>
                                <tr>
                                    <td class="cell100 column1 col1">{{ trans('Student Name') }}</td>
                                    <td class="cell100 column2 col2">
                                        {{$info->name}} </td>
                                </tr>
                                <tr>
                                    <td class="cell100 column1 col1">{{ trans('Student ID') }}</td>
                                    <td class="cell100 column2 col2">
                                        {{$info->id}} </td>
                                </tr>
                                <tr>
                                    <td class="cell100 column1 col1">{{ trans('Student Age') }}</td>
                                    <td class="cell100 column2 col2">
                                        {{ $age }} {{ $age === 1 ? 'year' : 'years' }}
                                    </td>
                                </tr>
                                <tr>
                                    <td class="cell100 column1 col1">{{ trans('Grade') }}</td>
                                    <td class="cell100 column2 col2">
                                        {{$classroomName}}</td>
                                </tr>
                                <tr>
                                    <td class="cell100 column1 col1">{{ trans('Teacher') }}</td>
                                    <td class="cell100 column2 col2">
                                        {{$teacher}}</td>
                                </tr>
                                <tr>
                                    <td class="cell100 column1 col1">
                                        {{ trans('Academic Year') }}</td>
                                    <td class="cell100 column2 col2">
                                        {{$academicYear}}</td>
                                </tr>
                                <tr>
                                    <td class="cell100 column1 col1">
                                        {{ trans('Date range') }}</td>
                                    <td class="cell100 column2 col2">
                                        {{$startDate}}/{{$endDate}}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </td>
            <td style="width: 20%;">
                <img src="{{$info->photo}}" style="height: 150px; border-radius: 10px;" />
            </td>
        </tr>
    </table>
   
    {{-- Student Measurements --}}
    <table style="width: 100%; border-collapse: collapse; font-size: 14px" border="1">
        <tr>
            <th colspan="3" style="padding: 5px; text-align: center;"> Height & Weight </th>
        </tr>
        <tr>
            <th style="padding: 5px; text-align: center; font-weight: bold;">Month</th>
            <th style="padding: 5px; text-align: center; font-weight: bold;">H</th>
            <th style="padding: 5px; text-align: center; font-weight: bold;">W</th>
        </tr>

        @foreach ($measurements as $measurement)
            @php
    $monthName = \Carbon\Carbon::parse($measurement->date)->format('F'); // Get month name dynamically
    $height = number_format($measurement->height, 0) . ' cm';
    $weight = number_format($measurement->weight, 0) . ' kg';
            @endphp
            <tr>
                <td style="padding: 5px; text-align: center;">{{ $monthName }}</td>
                <td style="padding: 5px; text-align: center;">{{ $height }}</td>
                <td style="padding: 5px; text-align: center;">{{ $weight }}</td>
            </tr>
        @endforeach
    </table>

    {{-- Grading System --}}
    @include('partial_view.generate_lists.kg_report.term_parts.grading_scale', [$age])

    <!-- Logos at the bottom of the first page -->
    <div class="logo-container page-break">
        <!-- <div style="text-align: center;"> -->
{{--            <img src="/img/footer.png" class="logo" />--}}
        <!-- </div> -->
    </div>

    {{-- Learning Areas --}}
    @include('partial_view.generate_lists.kg_report.term_parts.page_subjects', [$studentId, $academicYearId, $term, $startDate, $endDate])

    {{-- Signatures --}}
    <table border="1" style="border-collapse: collapse; text-align: center; font-size: 14px">
        <tr>
            <th rowspan="2">Term {{ $term?->academic_semester }}</th>
            <th>Teacher’s Signature</th>
            <th>Principal’s Signature</th>
            <th>Parent’s Signature</th>
        </tr>
        <tr>
            <td style="padding: 20px;"></td>
            <td></td>
            <td></td>
        </tr>
    </table>
