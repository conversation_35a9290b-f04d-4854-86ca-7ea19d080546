@php
    use App\Models\StudentInformation;
    use App\Models\ElectiveGrade;
    use App\Library\Helper\ReportCardHelper;

    $reportHelper = new ReportCardHelper();

    $info = StudentInformation::find($studentId);
    $excludedIds = $excluded->pluck('subject_id')->toArray();

    $subjects = ElectiveGrade::select(
        'subjects.subject_id',
        'subjects.subject_name',
        'subjects.subject_type',
        'academic_elective_grade.grade_id',
        'subjects.subject_type'
    )
        ->leftJoin('academic_elective_grade_students', 'academic_elective_grade.grade_id', 'academic_elective_grade_students.grade_id')
        ->leftJoin('subjects', 'subjects.subject_id', 'academic_elective_grade.subject_id')
        ->where('academic_elective_grade_students.student_id', $studentId)
        ->where('academic_elective_grade.academic_year_id', $academicYearId)
        ->whereNotIn('subjects.subject_id', $excludedIds) // Exclude IDs here
        ->groupBy(
            'academic_elective_grade.subject_id',
            'subjects.subject_id',
            'subjects.subject_name',
            'academic_elective_grade.grade_id'
        )
        ->orderBy('subjects.subject_type')
        ->orderBy('subjects.subject_name')
        ->get();
@endphp

@php
    $nationalSubjects = $subjects->filter(fn($subject) => $subject->subject_type !== 'en');
    $englishSubjects = $subjects->filter(fn($subject) => $subject->subject_type === 'en');
@endphp

@foreach ($englishSubjects as $subject)
    <table border="1" style="border-collapse: collapse; text-align: center; font-size: 13px">
        @php
            $assessmentsByType = $reportHelper->getStudentSubjectFormativeAssessments($subject->subject_id, $studentId, $academicYearId, $startDate, $endDate);
            
        @endphp
        <tr>
            <td style="text-align: center; font-weight: bold;" colspan="2">Learning Areas</td>
        </tr>

        <tr>
            <td style="width:80%; font-weight: bold;">{{ $subject->subject_name }}</td>
            <td style="width:20%; font-weight: bold; text-align:center">Term {{ $term?->academic_semester }}</td>
        </tr>

        @foreach ($assessmentsByType as $typeTitle => $assessments)
            @if ($typeTitle != '-')
                <tr>
                    <td colspan="2"><strong>{{ $typeTitle }}</strong></td>
                </tr>
            @endif
            @foreach ($assessments as $assessment)
                <tr>
                    <td>{{ $assessment['assessment_name'] }}</td>
                    <td style="text-align:center">{{ $assessment['grade'] }}</td>
                </tr>
            @endforeach
        @endforeach

    </table>
@endforeach

@if ($nationalSubjects->isNotEmpty())
    <table border="1" style="border-collapse: collapse; text-align: center; font-size: 13px">
        <tr>
            <td style="text-align: center; font-weight: bold;" colspan="2">Learning Areas</td>
        </tr>
        <tr>
            <td style="text-align: center; font-weight: bold;">National Curriculum</td>
            <td style="width:20%; font-weight: bold; text-align:center">{{ $term?->term_title }}</td>
        </tr>

        @foreach ($nationalSubjects as $subject)
            @php
                $assessmentsByType = $reportHelper->getStudentSubjectFormativeAssessments($subject->subject_id, $studentId, $academicYearId, $startDate, $endDate);
            @endphp

            <tr>
                <td colspan="2"><strong>{{ $subject->subject_name}}</strong></td>
            </tr>

            @foreach ($assessmentsByType as $typeTitle => $assessments)

                @foreach ($assessments as $assessment)
                    <tr>
                        <td>{{ $assessment['assessment_name'] }}</td>
                        <td style="text-align:center">{{ $assessment['grade'] }}</td>
                    </tr>
                @endforeach
            @endforeach
        @endforeach
    </table>
@endif