

<head>
    <title>Report Card</title>
<style>
    @import url('https://fonts.googleapis.com/css2?family=Roboto&display=swap');        

    html {
        margin: 0px;
        font-family: 'Roboto', sans-serif;
    }

    .table100.ver5 .table100-body table {
        border-collapse: separate;
        border-spacing:1 5px;
    }

    .table100.ver5 .table100-body td {
        padding-top: 2px;
        padding-bottom:2px;
        padding-left: 10px;
        font-size: 13px;
        line-height: 1;
    }

    .table100.ver5 .table100-body td:last-child {
        border-bottom-right-radius: 10px;
        border-top-right-radius:10px
    }

    .table100.ver5 .table100-body {
        padding-right:30px
    }

    .col1 {
        border-top: 1px solid #0e284d!important; 
        border-bottom:  1px solid #0e284d!important; 
        border-left:  1px solid #0e284d!important;

        font-weight: bold;
    }

    .col2 {
        border-top: 1px solid #0e284d!important; 
        border-right:  1px solid #0e284d!important; 
        border-bottom:  1px solid #0e284d!important;
        font-weight: bold;
    }

    .assessmentListTable {
        text-align:center;
        font-size: 10px;
    }

    .center-text {
        text-align: center;
    }

    .assessmentListTable {
        text-align:center;
        font-size: 12px;
    }

    .center-text {
        text-align: center;
    }

    .assessmentListTable {
        width: 100%;
        border-collapse: collapse;
        /* border: 1px solid black; */
        /* margin-top: 5px; */
    }

    .subjectSection {
        background-color: bisque;
        font-size: 15px;
        width: 150px;
    }
</style>

</head>

@php
    use App\Models\AcademicSemester;
    use App\Models\AcademicYear;
    use App\Models\ReportCardData;
    use App\Library\Helper\AcademicHelper;

    $ah = new AcademicHelper();
    $academicYearId = $ah->academicYear();
    $branchId = $ah->currentBranch();

    $yearInfo = AcademicYear::find($academicYearId);

    $reportCardData = ReportCardData::where('uid', $data->uid)->first();
    $students = explode(",",$reportCardData->students);

    $term = AcademicSemester::where('academic_year_id', $academicYearId)
                         ->where('branch_id', $branchId)
                         ->where('start_date', $reportCardData->start_date)
                         ->where('end_date', $reportCardData->end_date)
                         ->first();

    $uid = $reportCardData->uid;
    $reportTitle = $reportCardData->report_title;
    $startDate = $reportCardData->start_date;
    $endDate = $reportCardData->end_date;
    $classroomName = $reportCardData->classroom->classroom_name;
    $teacher = $reportCardData->classroom->homeroom->name;
    $createdAt = $reportCardData->created_at;
   
    $excluded = \DB::Table('settings_excluded_subjects')
                    ->where('academic_year_id', $academicYearId)
                    ->where('branch_id', $branchId)
                    ->where('grade_id', $reportCardData->classroom_id)
                    ->get();

@endphp


@foreach ($students as $student)
    <div style="padding-left: 10px; padding-right: 10px;">
    @include('partial_view.generate_lists.kg_report.term_parts.page_intro', 
                        [
                            'studentId'                 => $student, 
                            'uid'                       => $uid,
                            'reportTitle'               => $reportTitle,
                            'academicYear'              => $yearInfo->academic_year,
                            'academicYearId'            => $academicYearId,
                            'branchId'                  => $branchId,
                            'startDate'                 => $startDate,
                            'endDate'                   => $endDate,
                            'classroomName'             => $classroomName,
                            'excluded'                  => $excluded,
                            'term'                      => $term,
                            'teacher'                   => $teacher,
                        ])
    </div>
@endforeach
