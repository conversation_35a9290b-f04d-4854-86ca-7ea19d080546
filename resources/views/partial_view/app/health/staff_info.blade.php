<div class="card ">
    <div class="card-header card-header-stretch">

        <h3 class="card-title">
            <img src="{{$user->photo}}" class="user-photo user-photo-zoom" />
            <span style="padding-left: 50px;">{{$user->name}}</span>
        </h3>
        <div class="card-toolbar">
            <ul class="nav nav-tabs nav-line-tabs nav-stretch fs-6 border-0">
                <li class="nav-item">
                    <a class="nav-link active" id="menuTabStaffRecord" data-bs-toggle="tab"
                       href="#tabStaffRecords">{{ __('app/health.health_records') }}</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" id="menuTabStaffInformation" data-bs-toggle="tab"
                       href="#tabStaffHealthInfo">{{ __('app/health.staff_health_info') }}</a>
                </li>
            </ul>
        </div>
    </div>
    <div class="card-body">

        <div class="tab-content" id="myTabContent">
            <!-- tabRecords -->
            <div class="tab-pane fade show active" id="tabStaffRecords" role="tabpanel">
                <form id="formNewStaffRecord">
                    <div class="row">
                        <div class="col-4">
                            <input type="date" class="form-control" placeholder="{{ __('app/health.admission_date') }}"
                                   name="date" required />
                        </div>

                        <div class="col-4">
                            <input type="time" class="form-control" placeholder="{{ __('app/health.admission_time') }}"
                                   name="time" autocomplete="off" required />
                        </div>
                        <div class="col-4">
                            <input type="time" class="form-control" placeholder="{{ __('app/health.discharge_time') }}"
                                   name="discharge_time" autocomplete="off" />
                        </div>
                    </div>

                    <br />
                    <div class="row">
                        <div class="col-4">
                            <select class="form-control" name="reason[]" multiple data-control="select2"
                                    data-placeholder="{{ __('app/health.reason') }}" data-dropdown-parent="#modalStaff">
                                @foreach ($injuries as $injury)
                                    <option value="{{$injury->item}}">{{trans($injury->item)}}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-4">
                            <select class="form-control" name="action[]" multiple data-control="select2"
                                    data-placeholder="{{ __('app/health.actions_taken') }}" data-dropdown-parent="#modalStaff">
                                @foreach ($actions as $action)
                                    <option value="{{$action->item}}">{{trans($action->item)}}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-4">
                            <input type="text" class="form-control" placeholder="{{ __('app/health.temperature') }}"
                                   name="temperature" />
                        </div>
                    </div>

                    <br>
                    <div class="row">
                        <div class="col-4">
                            <input type="text" class="form-control" placeholder="{{ 'Blood Pressure (BP)'}}"
                                   name="blood_pressure" />
                        </div>
                        <div class="col-4">
                            <input type="text" class="form-control" placeholder="{{ __('Saturation (SpO2)') }}"
                                   name="saturation" />
                        </div>
                        <div class="col-4">
                            <input type="text" class="form-control" placeholder="{{ __('Pulse') }}" name="pulse" />
                        </div>
                    </div>


                    <br />
                    <div class="row">
                        <div class="col-4">
                            <input type="text" class="form-control" placeholder="{{ __('app/health.medication') }}"
                                   name="medication" />
                        </div>
                        <div class="col-4">
                            <input type="text" class="form-control" placeholder="{{ __('app/health.comments') }}"
                                   name="comments" />
                        </div>
                        <div class="col-4">
                            <a href="#" class="btn btn-dark w-100" id="btnSubmitStaffRecord"><i
                                        class="fas fa-save"></i>{{ __('app/health.add_record') }}</a>
                        </div>
                    </div>
                    <br />

                    <input type="hidden" name="user_id" value="{{$user->id}}" required />

                </form>
                <table class="table border table-hover gs-5" id="tableStaffRecords">
                    <thead>
                    <th>{{ __('app/health.date') }}</th>
                    <th>{{ __('app/health.time') }}</th>
                    <th>{{ __('app/health.reason') }}</th>
                    <th>{{ __('app/health.action') }}</th>
                    {{-- <th>Contact Time</th> --}}
                    <th>{{ __('app/health.medication') }}</th>
                    <th style="text-align:center">{{ __('app/health.vitals') }}</th>
                    <th style="text-align:center">{{ __('app/health.comments') }}</th>
                    <th></th>
                    </thead>
                    <tbody>
                    @foreach ($records as $record)
                        <tr>
                            <td>{{$record->date}}</td>
                            {{-- <td>{{$record->parent_contact_time}}</td> --}}
                            <td>
                                <span class="badge badge-pill badge-success">{{ $record->time ? $record->time : '' }}</span>
                                <span class="badge badge-pill badge-primary">{{ $record->discharge_time ? $record->discharge_time : '' }}</span>
                            </td>
                            <td>{{ $record->reason }}</td>
                            <td>{{ $record->action }}</td>
                            <td>{{$record->medication}}</td>
                            <td>
                                <span class="badge badge-pill badge-light">{{ $record->temperature ? $record->temperature . ' °C' : ''}}</span>
                                <span class="badge badge-pill badge-light">{{ $record->blood_pressure ?? 'N/A'}}</span>
                                <span class="badge badge-pill badge-light">{{ $record->saturation ? $record->saturation . ' %' : ''}}</span>
                                <span class="badge badge-pill badge-light">{{ $record->pulse ? $record->pulse . ' bpm' : ''}}</span>
                            </td>
                            <td>{{$record->comments}}</td>
                            <td class="text-right"><a href="#" class="btnDeleteStaffRecord" data-id="{{$record->record_id}}"><i
                                            class="fas fa-trash"></i></a></td>
                        </tr>
                    @endforeach
                    </tbody>
                </table>
            </div>
            <!-- END: tabRecords -->

            <!-- tabHealthInfo -->
            <div class="tab-pane fade" id="tabStaffHealthInfo" role="tabpanel">
                <form id="formStaffHealthInfo">
                    <input type="hidden" name="user_id" value="{{ $user->id }}" required />

                    <div class="row mb-3">
                        <label for="inputEmail3" class="col-3 col-form-label">
                            {{ __('app/head_office/staff.allergies') }} </label>
                        <div class="col-9">
                            <input type="text" class="form-control" name="allergies"
                                   value="{{ $user->details?->health_allergies }}"
                                   placeholder=" {{ __('app/head_office/staff.allergies') }} " />
                        </div>
                    </div>

                    <div class="row mb-3">
                        <label for="inputEmail3" class="col-3 col-form-label">
                            {{ __('app/head_office/staff.chronic_diseases') }} </label>
                        <div class="col-9">
                            <input type="text" class="form-control" name="chronic_diseases"
                                   value="{{ $user->details?->health_chronic_diseases }}"
                                   placeholder=" {{ __('app/head_office/staff.chronic_diseases') }} " />
                        </div>
                    </div>

                    <div class="row mb-3">
                        <label for="inputEmail3" class="col-3 col-form-label">
                            {{ __('app/head_office/staff.chronic_diseases_leave') }} </label>
                        <div class="col-9">
                            <input type="text" class="form-control" name="chronic_diseases_requires_leave"
                                   value="{{ $user->details?->health_chronic_diseases_require_leave }}"
                                   placeholder=" {{ __('app/head_office/staff.chronic_diseases_leave') }} " />
                        </div>
                    </div>

                    <div class="row mb-3">
                        <label for="inputEmail3" class="col-3 col-form-label">
                            {{ __('app/head_office/staff.reg_medication') }} </label>
                        <div class="col-9">
                            <input type="text" class="form-control" name="regularly_used_medication"
                                   value="{{ $user->details?->health_regularly_used_medication }}"
                                   placeholder=" {{ __('app/head_office/staff.reg_medication') }} " />
                        </div>
                    </div>

                    <div class="row mb-3">
                        <label for="inputEmail3" class="col-3 col-form-label"> {{ __('app/head_office/staff.pregnant') }}
                        </label>
                        <div class="col-9">
                            <script>
                                $('#pregnant').val('{{ $user->details?->health_is_pregnant }}');
                            </script>
                            <select name="pregnant" id="pregnant" class="form-control"  data-control="select2" data-dropdown-parent="#modalUserInfo">
                                <option></option>
                                <option value="Yes"> {{ __('general.yes') }} </option>
                                <option value="No"> {{ __('general.no') }} </option>
                            </select>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <label for="inputEmail3" class="col-3 col-form-label">
                            {{ __('app/head_office/staff.height_weight') }} </label>
                        <div class="col-5">
                            <input type="text" class="form-control" value="{{ $user->details?->health_height }}"
                                   name="height" placeholder=" {{ __('app/head_office/staff.height') }} " />
                        </div>
                        <div class="col-4">
                            <input type="text" class="form-control" value="{{ $user->details?->health_weight }}"
                                   name="weight" placeholder=" {{ __('app/head_office/staff.weight') }} " />
                        </div>
                    </div>

                    <div class="row mb-3">
                        <label for="inputEmail3" class="col-3 col-form-label">
                            {{ __('app/head_office/staff.blood_type') }} </label>
                        <div class="col-9">
                            <script>
                                $('#blood_type').val('{{ $user->details?->health_blood_type }}');
                            </script>
                            <select name="blood_type" id="blood_type" class="form-control"  data-control="select2" data-dropdown-parent="#modalUserInfo">
                                <option></option>
                                @foreach ($bloodType as $key => $bt)
                                    <option value='{{ $bt->item }}'>{{ $bt->item }}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <label for="inputEmail3" class="col-3 col-form-label">
                            {{ __('app/head_office/staff.other_health_issues') }} </label>
                        <div class="col-9">
                            <input type="text" class="form-control" name="other_health_issues"
                                   value="{{ $user->details?->health_other_health_issues }}"
                                   placeholder=" {{ __('app/head_office/staff.other_health_issues') }} " />
                        </div>
                    </div>

                </form>

            </div>
            <!-- END: tabHealthInfo -->



        </div>
    </div>