
    <thead>
        <tr>
            <th>#</th>
            <th>ID</th>
            <th>Name</th>
            <th>{{ __('app/head_office/staff.allergies') }}</th>
            <th>{{ __('app/head_office/staff.chronic_diseases') }}</th>
            <th>{{ __('app/head_office/staff.chronic_diseases_leave') }}</th>
            <th>{{ __('app/head_office/staff.reg_medication') }}</th>
            <th>{{ __('app/head_office/staff.pregnant') }}</th>
            <th>{{ __('app/head_office/staff.height_weight') }}</th>
            <th>{{ __('app/head_office/staff.weight') }}</th>
            <th>{{ __('app/head_office/staff.blood_type') }}</th>
            <th>{{ __('app/head_office/staff.other_health_issues') }}</th>
        </tr>
    </thead>

    <tbody>
        @foreach ($reportData as $record)
            <tr>
                <td>{{ $loop->iteration }}</td>
                <td>{{ $record->user_id }}</td>
                <td>{{ $record->user->name }}</td>
                <td>{{ $record->health_allergies }}</td>
                <td>{{ $record->health_chronic_diseases }}</td>
                <td>{{ $record->health_chronic_diseases_require_leave }}</td>
                <td>{{ $record->health_regularly_used_medication }}</td>
                <td>{{ $record->health_is_pregnant }}</td>
                <td>{{ $record->health_height }}</td>
                <td>{{ $record->health_weight }}</td>
                <td>{{ $record->health_blood_type }}</td>
                <td>{{ $record->health_other_health_issues }}</td>
            </tr>
        @endforeach
    </tbody>

