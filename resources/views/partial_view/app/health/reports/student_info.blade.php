
<thead>
<tr>
    <th>#</th>
    <th>Student ID</th>
    <th>Name</th>
    <th>Health Status</th>
    <th>{{ __('app/health.regularly_used_medication') }}</th>
    <th>{{ __('app/health.vision_problem') }}</th>
    <th>{{ __('app/health.vision_check_date') }}</th>
    <th>{{ __('app/health.any_hearing_issue') }}</th>
    <th>{{ __('app/health.special_food_consideration') }}</th>
    <th>{{ __('app/health.allergies') }}</th>
    <th>{{ __('app/health.allergy_symptoms') }}</th>
    <th>{{ __('app/health.allergy_first_aid') }}</th>
    <th>{{ __('app/health.allowed_medications') }}</th>
</tr>
</thead>

<tbody>
@forelse($reportData as $record)
    <tr>
        <td>{{ $loop->iteration }}</td>
        <td>{{ $record->id }}</td>
        <td>{{ $record->name }}</td>
        <td>{{ $record->health?->medical_conditions }}</td>
        <td>{{ $record->health?->regularly_used_medication }}</td>
        <td>{{ $record->health?->has_vision_problem }}</td>
        <td>{{ $record->health?->vision_check_date }}</td>
        <td>{{ $record->health?->hearing_issue }}</td>
        <td>{{ $record->health?->special_food_consideration }}</td>
        <td>{{ $record->health?->allergies }}</td>
        <td>{{ $record->health?->allergy_symptoms }}</td>
        <td>{{ $record->health?->allergy_first_aid }}</td>
        <td>
            @php
                $allowedDrugsCodes = explode(',', $record->allowed_drugs);
            @endphp
            @foreach($allowedDrugsCodes as $code)
                @php
                    // Find the medicine by its item_code using firstWhere (returns the first match)
                    $medicine = $medicineDictionary->firstWhere('item_code', trim($code));
                @endphp
                @if($medicine)
                    <span class="badge badge-pill badge-secondary">{{ $medicine->item_slug }}</span>
                @else
                    <span class="badge badge-pill badge-secondary">{{ $code }}</span>
                @endif
            @endforeach
        </td>
    </tr>
@empty
    <tr>
        <td colspan="5" class="text-center">No health records found for selected students.</td>
    </tr>
@endforelse
</tbody>

