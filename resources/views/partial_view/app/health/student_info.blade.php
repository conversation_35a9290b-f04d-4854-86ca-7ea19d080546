
<style>
    .indicator {
        font-size: 18px;
        margin-left: 10px;
    }

    .up {
        color: green;
    }

    .down {
        color: red;
    }
</style>
<div class="card">
    <div class="card-header card-header-stretch">
        <h3 class="card-title">
            <img src="{{ $student->photo }}" class="user-photo user-photo-zoom"/>
            <span style="padding-left: 50px;">{{ $student->name }}</span>
        </h3>
        <div class="card-toolbar">
            <ul class="nav nav-tabs nav-line-tabs nav-stretch fs-6 border-0">
                <li class="nav-item">
                    <a class="nav-link active" id="menuTabRecord" data-bs-toggle="tab"
                       href="#tabRecords">{{ __('app/health.health_records') }}</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" id="menuTabInformation" data-bs-toggle="tab"
                       href="#tabHealthInfo">{{ __('app/health.students_health_info') }}</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" id="menuTabMeasurements" data-bs-toggle="tab"
                       href="#tabHealthMeasurements">{{ __('app/health.height_weight') }}</a>
                </li>
            </ul>
        </div>
    </div>

    <!-- TABS -->
    <div class="card-body">
        <div class="tab-content" id="myTabContent">
            <!-- tabRecords -->
            <div class="tab-pane fade show active" id="tabRecords" role="tabpanel">
                <form id="formNewStudentRecord">
                    <div class="row">
                        <div class="col-3">
                            <input type="date" class="form-control" placeholder="{{ __('app/health.admission_date') }}"
                                   name="date" required/>
                        </div>

                        <div class="col-3">
                            <input type="time" class="form-control" placeholder="{{ __('app/health.admission_time') }}"
                                   name="time" autocomplete="off" required/>
                        </div>
                        <div class="col-3">
                            <input type="time" class="form-control" placeholder="{{ __('app/health.discharge_time') }}"
                                   name="discharge_time" autocomplete="off" required/>
                        </div>

                        <div class="col-3">
                            <input type="time" class="form-control"
                                   placeholder="{{ __('app/health.parent_contact_time') }}" autocomplete="off"
                                   name="parent_contact_time"/>
                        </div>
                    </div>
                    <br/>
                    <div class="row">
                        <div class="col-4">
                            <select class="form-control" name="reason[]" multiple data-control="select2"
                                    data-placeholder="{{ __('app/health.reason') }}"
                                    data-dropdown-parent="#modalStudents">
                                @foreach ($injuries as $injury)
                                    <option value="{{ $injury->item }}">{{ trans($injury->item) }}</option>
                                @endforeach
                            </select>
                        </div>

                        <div class="col-4">
                            <select class="form-control" name="action[]" multiple data-control="select2"
                                    data-placeholder="{{ __('app/health.actions_taken') }}"
                                    data-dropdown-parent="#modalStudents">
                                @foreach ($actions as $action)
                                    <option value="{{ $action->item }}">{{ trans($action->item) }}</option>
                                @endforeach
                            </select>
                        </div>

                        <div class="col-4">
                            <input type="text" class="form-control" placeholder="{{ __('app/health.temperature') }}"
                                   name="temperature"/>
                        </div>
                    </div>

                    <br>
                    <div class="row">
                        <div class="col-4">
                            <input type="text" class="form-control" placeholder="{{ __('Blood Pressure (BP)') }}"
                                   name="blood_pressure"/>
                        </div>
                        <div class="col-4">
                            <input type="text" class="form-control" placeholder="{{ __('Saturation (SpO2)') }}"
                                   name="saturation"/>
                        </div>
                        <div class="col-4">
                            <input type="text" class="form-control" placeholder="{{ __('Pulse') }}" name="pulse"/>
                        </div>
                    </div>

                    <br/>
                    <div class="row">
                        <div class="col-4">
                            <input type="text" class="form-control" placeholder="{{ __('app/health.medication') }}"
                                   name="medication"/>
                        </div>

                        <div class="col-4">
                            <input type="text" class="form-control" placeholder="{{ __('app/health.comments') }}"
                                   name="comments"/>
                        </div>

                        <div class="col-4">
                            <a href="#" class="btn btn-dark w-100" id="btnSubmitStudentRecord"><i
                                        class="fas fa-save"></i> {{ __('app/health.add_record') }}</a>
                        </div>
                    </div>
                    <br/>
                    <input type="hidden" name="student_id" value="{{ $student->id }}" required/>
                </form>

                <!-- Added Information Table -->
                <table class="table border table-hover gs-5" id="tableStudentRecords">
                    <thead>
                    <tr>
                        <th>{{ __('app/health.date') }}</th>
                        <th>{{ __('app/health.time') }}</th>
                        <th>{{ __('app/health.reason') }}</th>
                        <th>{{ __('app/health.action') }}</th>
                        <th>{{ __('app/health.medication') }}</th>
                        <th style="text-align:center">{{ __('app/health.vitals') }}</th>
                        <!-- Updated header for clarity -->
                        <th style="text-align:center">{{ __('app/health.comments') }}</th>
                        <th></th>
                    </tr>
                    </thead>
                    <tbody>
                    @foreach ($records as $record)
                        <tr>
                            <td>{{ $record->date }}</td>
                            <td>
                                    <span
                                            class="badge badge-pill badge-success">{{ $record->time ? $record->time : '' }}</span>
                                <span
                                        class="badge badge-pill badge-primary">{{ $record->discharge_time ? $record->discharge_time : '' }}</span>
                                <span
                                        class="badge badge-pill badge-warning">{{ $record->parent_contact_time ? $record->parent_contact_time : '' }}</span>
                            </td>
                            <td>{{ $record->reason }}</td>
                            <td>{{ $record->action }}</td>
                            <td>{{ $record->medication }}</td>
                            <td>
                                    <span
                                            class="badge badge-pill badge-light">{{ $record->temperature ? $record->temperature . ' °C' : '' }}</span>
                                <span class="badge badge-pill badge-light">{{ $record->blood_pressure ?? 'N/A' }}</span>
                                <span
                                        class="badge badge-pill badge-light">{{ $record->saturation ? $record->saturation . ' %' : '' }}</span>
                                <span
                                        class="badge badge-pill badge-light">{{ $record->pulse ? $record->pulse . ' bpm' : '' }}</span>
                            </td>
                            <td>{{ $record->comments }}</td>
                            <td class="text-right">
                                <a href="#" class="btnDeleteStudentRecord" data-id="{{ $record->health_record_id }}">
                                    <i class="fas fa-trash"></i>
                                </a>
                            </td>
                        </tr>
                    @endforeach
                    </tbody>
                </table>
                <!-- END: Added Information Table -->

            </div>
            <!-- END: tabRecords -->

            <!-- tabHealthInfo -->
            <div class="tab-pane fade" id="tabHealthInfo" role="tabpanel">
                <form id="formStudentHealthInfo">
                    <table class="table table-striped border gs-5">
                        <tr>
                            <td>{{ __('app/health.medical_condition') }}</td>
                            <td>
                                <textarea class="form-control"
                                          name="medical_condition">{{ $healthInfo->medical_conditions ?? '' }}</textarea>
                            </td>
                        </tr>
                        <tr>
                            <td>{{ __('app/health.regularly_used_medication') }}</td>
                            <td>
                                <textarea class="form-control"
                                          name="regularly_used_medication">{{ $healthInfo->regularly_used_medication ?? '' }}</textarea>
                            </td>
                        </tr>
                        <tr>
                            <td>{{ __('app/health.vision_problem') }}</td>
                            <td>
                                <input type="text" class="form-control"
                                       value="{{ $healthInfo->has_vision_problem ?? '' }}" name="vision_problem"/>
                            </td>
                        </tr>
                        <tr>
                            <td>{{ __('app/health.vision_check_date') }}</td>
                            <td>
                                <input type="date" placeholder="Pick date"
                                       value="{{ $healthInfo->vision_check_date ?? '' }}" class="form-control"
                                       name="vision_check_date"/>
                            </td>
                        </tr>
                        <tr>
                            <td>{{ __('app/health.any_hearing_issue') }}</td>
                            <td>
                                <input type="text" class="form-control" value="{{ $healthInfo->hearing_issue ?? '' }}"
                                       name="hearing_issue"/>
                            </td>
                        </tr>
                        <tr>
                            <td>{{ __('app/health.special_food_consideration') }}</td>
                            <td>
                                <input type="text" class="form-control"
                                       value="{{ $healthInfo->special_food_consideration ?? '' }}"
                                       name="food_consideration"/>
                            </td>
                        </tr>
                        <tr>
                            <td>{{ __('app/health.allergies') }}</td>
                            <td>
                                <input type="text" class="form-control" value="{{ $healthInfo->allergies ?? '' }}"
                                       name="allergy"/>
                            </td>
                        </tr>
                        <tr>
                            <td>{{ __('app/health.allergy_symptoms') }}</td>
                            <td>
                                <input type="text" class="form-control"
                                       value="{{ $healthInfo->allergy_symptoms ?? '' }}" name="allergy_symptoms"/>
                            </td>
                        </tr>
                        <tr>
                            <td>{{ __('app/health.allergy_first_aid') }}</td>
                            <td>
                                <input type="text" class="form-control"
                                       value="{{ $healthInfo->allergy_first_aid ?? '' }}" name="allergy_first_aid"/>
                            </td>
                        </tr>
                        <tr>
                            <td>{{ __('app/health.allowed_medications') }}</td>
                            <td>
                                @php
                                    // Convert the allowed_drugs into an array of selected medication codes
                                    $allowedDrugsCodes = explode(',', $healthInfo->allowed_drugs ?? '');
                                @endphp

                                <select class="form-control select2-hidden-accessible" id="allowedMedications"
                                        name="allowed_medication[]"
                                        data-control="select2" multiple data-placeholder="-"
                                        data-dropdown-parent="#modalStudents">

                                    @foreach ($medications as $medication)
                                        <option value="{{ $medication->item_code }}"
                                                {{ in_array(trim($medication->item_code), $allowedDrugsCodes) ? 'selected' : '' }}>
                                            {{ $medication->item }}
                                        </option>
                                    @endforeach
                                </select>

                                <script>
                                    document.addEventListener('DOMContentLoaded', function () {
                                        // Initialize Select2 on the #allowedMedications select box
                                        $('#allowedMedications').select2({
                                            dropdownParent: $('#modalStudents')
                                        });
                                    });
                                </script>

                            </td>
                        </tr>

                        <tr>
                            <td>{{ __('app/health.emergency_contact_1') }}</td>
                            <td>
                                <div class="row">
                                    <div class="col-6"><input type="text" class="form-control"
                                                              value="{{ $healthInfo->emergency_name_1 ?? '' }}"
                                                              name="emergency_name_1"/>
                                    </div>
                                    <div class="col-6"><input type="text" class="form-control"
                                                              value="{{ $healthInfo->emergency_phone_1 ?? '' }}"
                                                              name="emergency_phone_1"/></div>
                                </div>
                            </td>
                        </tr>

                        <tr>
                            <td>{{ __('app/health.emergency_contact_2') }}</td>
                            <td>
                                <div class="row">
                                    <div class="col-6"><input type="text" class="form-control"
                                                              value="{{ $healthInfo->emergency_name_2 ?? '' }}"
                                                              name="emergency_name_2"/>
                                    </div>
                                    <div class="col-6"><input type="text" class="form-control"
                                                              value="{{ $healthInfo->emergency_phone_2 ?? '' }}"
                                                              name="emergency_phone_2"/></div>
                                </div>
                            </td>
                        </tr>
                    </table>

                    <input type="hidden" name="student_id" value="{{ $student->id }}" required/>
                </form>
            </div>
            <!-- END: tabHealthInfo -->

            <!-- tabHealthMeasurements -->
            <div class="tab-pane fade" id="tabHealthMeasurements" role="tabpanel">
                <form id="formStudentHealthMeasurements">
                    <div class="row">
                        <div class="col-3">
                            <input type="date" class="form-control" placeholder="{{ __('app/health.date') }}"
                                   name="date" required/>
                        </div>

                        <div class="col-3">
                            <input type="number" class="form-control" placeholder="{{ __('app/health.height') }}"
                                   name="height" autocomplete="off" required/>
                        </div>
                        <div class="col-3">
                            <input type="number" class="form-control" placeholder="{{ __('app/health.weight') }}"
                                   name="weight" autocomplete="off" required/>
                        </div>
                        <div class="col-3">
                            <a href="#" class="btn btn-dark w-100" id="btnSubmitStudentMeasurementRecord"><i
                                        class="fas fa-save"></i> {{ __('app/health.add_record') }}</a>
                        </div>
                    </div>
                    <br/>
                    <!-- information table -->
                    <table class="table table-striped border gs-5" id="tableStudentMeasurements">
                        <thead>
                        <tr>
                            <th>{{ __('app/health.date') }}</th>
                            <th>{{ __('app/health.height') }}</th>
                            <th>Height Change</th>
                            <th>{{ __('app/health.weight') }}</th>
                            <th>Weight Change</th>
                            <th></th>
                        </tr>
                        </thead>
                        <tbody>
                        @php
                            $previous = null;
                        @endphp
                        @foreach ($measurements as $measurement)
                            @php
                                $heightChange = $weightChange = null;
                                $heightIndicator = $weightIndicator = '';


                                if ($previous) {

                                    $heightChange = $measurement->height - $previous->height;
                                    $weightChange = $measurement->weight - $previous->weight;

                                    $heightIndicator = $heightChange > 0 ? 'up' : ($heightChange < 0 ? 'down' : '');
                                    $weightIndicator = $weightChange > 0 ? 'up' : ($weightChange < 0 ? 'down' : '');
                                    $heightInd = $heightChange > 0 ? 'success' : ($heightChange < 0 ? 'danger' : '');
                                    $weightInd = $weightChange > 0 ? 'success' : ($weightChange < 0 ? 'danger' : '');
                                }

                                $previous = $measurement;
                            @endphp
                            <tr>
                                <td>{{ $measurement->date }}
                                <td>{{ $measurement->height }}</td>
                                <td>
                                    @if ($heightChange !== null)
                                        <span
                                                class="badge badge-light-{{ $heightInd }} fs-base indicator {{ $heightIndicator }}">
                                                                        {{ $heightChange > 0 ? '↑' : ($heightChange < 0 ? '↓' : '-') }}
                                                                    </span>
                                    @endif


                                </td>
                                <td>{{ $measurement->weight }}</td>
                                <td>
                                    @if ($weightChange !== null)
                                        <span
                                                class="badge badge-light-{{ $weightInd }} fs-base indicator {{ $weightIndicator }}">
                                                                        {{ $weightChange > 0 ? '↑' : ($weightChange < 0 ? '↓' : '-') }}
                                                                    </span>
                                    @endif
                                </td>
                                <td>
                                    <a href="#" id="btnDeleteMeasurement" data-id="{{$measurement->id}}"><i
                                                class="fas fa-trash"></i></a>
                                </td>
                            </tr>
                        @endforeach
                        </tbody>
                    </table>
                    <input type="hidden" name="student_id" value="{{ $student->id }}" required/>
                </form>
            </div>
            <!-- END: tabHealthMeasurements -->
        </div>
    </div>
</div>