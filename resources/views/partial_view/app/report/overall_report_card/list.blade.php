<?php
use App\Library\Helper\AcademicHelper;
use App\Models\StudentClassroom;
use App\Models\ElectiveGrade;
use App\Models\FormativeAssessmentData;
use App\Models\SummativeAssessmentData;
use App\Library\Helper\ReportCardHelper;

$reportHelper = new ReportCardHelper();

$ah = new AcademicHelper();
$academicYearId = $ah->academicYear();

$excluded = \DB::table('settings_excluded_subjects')->where('academic_year_id', $academicYearId)->where('branch_id', $ah->currentBranch())->where('grade_id', $classroomId)->get();

$allSubjects = [];
$studentsArray = [];

$dateArr = explode('/', $dateRange);

$students = StudentClassroom::where('classroom_id', $classroomId)->get();

foreach ($students as $key => $student) {
    array_push($studentsArray, $student->student_id);
}

$subjects = ElectiveGrade::select('subjects.subject_id', 'subjects.subject_name', 'academic_elective_grade.grade_id', 'subjects.subject_type')->leftJoin('academic_elective_grade_students', 'academic_elective_grade.grade_id', 'academic_elective_grade_students.grade_id')->leftJoin('subjects', 'subjects.subject_id', 'academic_elective_grade.subject_id')->whereIn('academic_elective_grade_students.student_id', $studentsArray)->where('academic_elective_grade.academic_year_id', $academicYearId)->groupBy('academic_elective_grade.subject_id', 'subjects.subject_id', 'academic_elective_grade.grade_id')->get();

foreach ($subjects as $key => $subject) {
    foreach ($excluded as $ex) {
        if ($subject->subject_id == $ex->subject_id) {
            $subjects->forget($key);
        }
    }
}
?>

<div class="btn-group float-end" role="group" aria-label="Basic example">
    <a class="btn btn-secondary btnShow" style="background-color: #ff9900; color: #fff;" data-type="EE"
        data-color="ff9900">Show EE</a>
    <a class="btn btn-secondary btnShow" style="background-color: #4caf50; color:#fff;" data-type="ME"
        data-color="4caf50">Show ME</a>
    <a class="btn btn-secondary btnShow" style="background-color: #00b0f0; color:#fff;" data-type="AE"
        data-color="00b0f0">Show AE</a>
    <a class="btn btn-secondary btnShow" style="background-color: #ff6961; color:#fff;" data-type="BE"
        data-color="ff6961">Show BE</a>
</div>

<br /><br /><br />


<table id="resultTable" class="table border gs-7">
    <thead>
        <th>#</th>
        <th style="min-width: 100px;">Student</th>
        @foreach ($subjects as $subject)
            <th>{{ $subject->subject_name }}</th>
        @endforeach
    </thead>
    <tbody>
        @foreach ($students as $student)
            <tr>
                <td>{{ $student->student_id }}</td>
                <th>{{ $student->student->name ?? 'N/A' }}</th>
                @foreach ($subjects as $subject)
                    <?php
                    if ($summative == 1) {
                        $summativeAssessments = SummativeAssessmentData::leftJoin('academic_summative_assessments', 'academic_summative_assessments.assessment_id', 'academic_summative_assessments_data.assessment_id')
                            ->leftJoin('skills_strands', 'skills_strands.skill_strand_id', 'academic_summative_assessments.strand')
                            ->whereBetween('academic_summative_assessments.date', [$dateArr[0], $dateArr[1]])
                            ->where('academic_summative_assessments_data.student_id', $student->student_id)
                            ->whereNotNull('academic_summative_assessments_data.score')
                            ->where('academic_summative_assessments.subject_id', $subject->subject_id)
                            ->where('academic_summative_assessments.grade_id', $subject->grade_id)
                            ->where('academic_summative_assessments.academic_year_id', $academicYearId)
                            ->get();
                    
                        /* $summativeAssessments = SummativeAssessmentData::leftJoin('academic_summative_assessments', 'academic_summative_assessments.assessment_id', 'academic_summative_assessments_data.assessment_id')
                                    ->whereBetween('academic_summative_assessments.date', [$dateArr[0], $dateArr[1]])
                                    ->where('academic_summative_assessments_data.student_id', $student->student_id)
                                    ->where('academic_summative_assessments.subject_id', $subject->subject_id)
                                    ->where('academic_summative_assessments.academic_year_id', $academicYearId)
                                    ->get(); */
                    }
                    
                    if ($formative == 1) {
                        $formativeAssessments = FormativeAssessmentData::select('academic_formative_assessments.assessment_name as assessment_name', 'academic_formative_assessments.t1 as tt1', 'academic_formative_assessments.t2 as tt2', 'academic_formative_assessments.t3 as tt3', 'academic_formative_assessments.t4 as tt4', 'academic_formative_assessments_data.t1 as t1', 'academic_formative_assessments_data.t2 as t2', 'academic_formative_assessments_data.t3 as t3', 'academic_formative_assessments_data.t4 as t4', 'academic_formative_assessments_data.type_id as type_id', 'academic_formative_assessments_data.type_percentage as type_percentage')
                            ->leftJoin('academic_formative_assessments', 'academic_formative_assessments.formative_assessment_id', 'academic_formative_assessments_data.formative_assessment_id')
                            ->whereBetween('academic_formative_assessments.date', [$dateArr[0], $dateArr[1]])
                            ->where('academic_formative_assessments_data.student_id', $student->student_id)
                            ->where('academic_formative_assessments.subject_id', $subject->subject_id)
                            ->where('academic_formative_assessments.academic_year_id', $academicYearId)
                            ->get();
                    }
                    
                    $subjectAverage = $reportHelper->calculateSubjectFinal([
                        'student_id' => $student->student_id,
                        'subject_id' => $subject->subject_id,
                        'start_date' => $dateArr[0],
                        'end_date' => $dateArr[1],
                        'academic_year_id' => $academicYearId,
                        'grade_id' => $subject->grade_id,
                    ]);
                    
                    $avgArr = explode('/*/', $subjectAverage);
                    
                    $cellClass = null;
                    
                    if ($avgArr[0] >= 90 && $avgArr[0] <= 100) {
                        $cellClass = " class='color_EE resultBox' ";
                    }
                    if ($avgArr[0] >= 80 && $avgArr[0] <= 89) {
                        $cellClass = " class='color_ME resultBox' ";
                    }
                    if ($avgArr[0] >= 70 && $avgArr[0] <= 79) {
                        $cellClass = " class='color_AE resultBox' ";
                    }
                    if ($avgArr[0] <= 69) {
                        $cellClass = " class='color_BE resultBox' ";
                    }
                    ?>
                    <td <?php echo $cellClass; ?>>{{ $avgArr[1] }}</td>
                @endforeach
            </tr>
        @endforeach
    </tbody>
</table>
