<?php
    use App\Models\LessonPlan;

    $plan = LessonPlan::find($lessonPlanId);


    // $ah = new AcademicHelper();
    // $weeks = AcademicWeek::where('academic_year_id', $ah->academicYear())
    //                       ->where('branch_id', $ah->currentBranch())
    //                       ->get();

    // $subjects = $ah->teacherSubjects(\Auth::user()->id);
    // $classrooms = $ah->teacherTCLClassrooms(\Auth::user()->id);
?>

<form id="formLessonPlan">
    {{-- <div class="row mb-3">
        <label for="exampleInputuname_3" class="col-3 col-form-label">Week</label>
        <div class="col-9">
            <select class="form-control" name="week" required data-control="select2" data-placeholder="Pick week"
                data-dropdown-parent="#modalLessonPlan">
                @foreach ($weeks as $week)
                <option value="{{$week->week}}">Week {{$week->week}} [{{$week->range}}]</option>
                @endforeach
            </select>
        </div>
    </div> --}}

    {{-- <div class="row mb-3">
        <label for="exampleInputuname_3" class="col-3 col-form-label">Subject</label>
        <div class="col-9">
            <select class="form-control" name="subject" required data-control="select2" data-placeholder="Pick subject"
                data-dropdown-parent="#modalLessonPlan">
                @foreach ($subjects as $subject)
                <option value="{{$subject->subject_id}}">{{$subject->subject->subject_name}}</option>
                @endforeach
            </select>
        </div>
    </div> --}}

    {{-- <div class="row mb-3">
        <label class="col-3 col-form-label">Classroom(s)</label>
        <div class="col-9">
            <select class="form-control" multiple name="classroom[]" required data-control="select2"
                data-placeholder="Pick classroom" data-dropdown-parent="#modalLessonPlan">
                @foreach ($classrooms as $key => $classroom)
                <option value="{{$classroom->class_id}}">{{$classroom->classroom->classroom_name}}</option>
                @endforeach
            </select>
        </div>
    </div> --}}
    <div class="row mb-3">
        <label class="col-lg-3 control-label">Date</label>
        <div class="col-9">
            <input data-parent="#modalLessonPlan" type="date" value="{{$plan->lesson_plan_date_detail}}"
                class="form-control" name="date" />
        </div>
    </div>
    <div class="row mb-3">
        <label for="exampleInputuname_3" class="col-3 col-form-label">Unit</label>
        <div class="col-9">
            <input type="text" value="{{$plan->lesson_plan_unit}}" class="form-control" name="unit" />
        </div>
    </div>
    <div class="row mb-3">
        <label for="exampleInputuname_3" class="col-3 col-form-label">Upload Link</label>
        <div class="col-9">
            <input type="text" value="{{$plan->lesson_plan_link}}" class="form-control" name="link" />
        </div>
    </div>
    <hr />
    <ul class="nav nav-tabs nav-line-tabs mb-5 fs-6">
        <li class="nav-item">
            <a class="nav-link active" data-bs-toggle="tab" href="#tab01">Day and Period</a>
        </li>
        <li class="nav-item">
            <a class="nav-link" data-bs-toggle="tab" href="#tab02">Unit, Topic, Sub-topic</a>
        </li>
        <li class="nav-item">
            <a class="nav-link" data-bs-toggle="tab" href="#tab03">Learning Objectives (Students will be able to:)</a>
        </li>
        <li class="nav-item">
            <a class="nav-link" data-bs-toggle="tab" href="#tab04">Activities & Differentiation</a>
        </li>
        <li class="nav-item">
            <a class="nav-link" data-bs-toggle="tab" href="#tab05">Independent, Paired Group, Whole Class</a>
        </li>
        <li class="nav-item">
            <a class="nav-link" data-bs-toggle="tab" href="#tab06">Evidence of Achievement</a>
        </li>
        <li class="nav-item">
            <a class="nav-link" data-bs-toggle="tab" href="#tab07">Materials & Resources</a>
        </li>
    </ul>

    <div class="tab-content" id="myTabContent">
        <div class="tab-pane fade show active" id="tab01" role="tabpanel">
            <textarea name="day_period" class="form-control textArea">{{$plan->day_period}}</textarea>
        </div>
        <div class="tab-pane fade" id="tab02" role="tabpanel">
            <textarea name="unit_topic_subtopic" class="form-control textArea">{{$plan->unit_topic_subtopic}}</textarea>
        </div>
        <div class="tab-pane fade" id="tab03" role="tabpanel">
            <textarea name="learning_objectives" class="form-control textArea">{{$plan->learning_objectives}}</textarea>
        </div>
        <div class="tab-pane fade" id="tab04" role="tabpanel">
            <textarea name="activities_differentiation"
                class="form-control textArea">{{$plan->activities_differentiation}}</textarea>
        </div>
        <div class="tab-pane fade" id="tab05" role="tabpanel">
            <textarea name="independent_pair" class="form-control textArea">{{$plan->independent_pair}}</textarea>
        </div>
        <div class="tab-pane fade" id="tab06" role="tabpanel">
            <textarea name="evidence_assessment" class="form-control textArea">{{$plan->evidence_assessment}}</textarea>
        </div>
        <div class="tab-pane fade" id="tab07" role="tabpanel">
            <textarea name="materials" class="form-control textArea">{{$plan->materials}}</textarea>
        </div>
    </div>
    <hr />
    <div class="row">
        <div class="col-2">
            <div class="n-chk">
                <label class="new-control new-checkbox checkbox-primary">
                    <input type="checkbox" id="check_confident" class="new-control-input optCheck" name="opt_confident"
                        data-id="01" {{$plan->opt_confident == "1" ?"checked":""}} >
                    <span class="new-control-indicator"></span>{{__('Confident')}}
                </label>
            </div>
        </div>

        <div class="col-2">
            <div class="n-chk">
                <label class="new-control new-checkbox checkbox-primary">
                    <input type="checkbox" id="check_reflective" class="new-control-input optCheck"
                        name="opt_reflective" data-id="04" {{$plan->opt_reflective == "1" ?"checked":""}}>
                    <span class="new-control-indicator"></span>{{__('Reflective')}}
                </label>
            </div>
        </div>

        <div class="col-2">
            <div class="n-chk">
                <label class="new-control new-checkbox checkbox-primary">
                    <input type="checkbox" id="check_innovative" class="new-control-input optCheck"
                        name="opt_innovative" data-id="05" {{$plan->opt_innovative == "1" ?"checked":""}}>
                    <span class="new-control-indicator"></span>{{__('Innovative')}}
                </label>
            </div>
        </div>

        <div class="col-2">
            <div class="n-chk">
                <label class="new-control new-checkbox checkbox-primary">
                    <input type="checkbox" id="check_engaged" class="new-control-input optCheck" name="opt_engaged"
                        data-id="02" {{$plan->opt_engaged == "1" ?"checked":""}}>
                    <span class="new-control-indicator"></span>{{__('Engaged')}}
                </label>
            </div>
        </div>
        <div class="col-2">
            <div class="n-chk">
                <label class="new-control new-checkbox checkbox-primary">
                    <input type="checkbox" id="check_responsible" class="new-control-input optCheck"
                        name="opt_responsible" data-id="03" {{$plan->opt_responsible == "1" ?"checked":""}}>
                    <span class="new-control-indicator"></span>{{__('Responsible')}}
                </label>
            </div>
        </div>
    </div>
    <input type="hidden" name="plan_id" value="{{$plan->lesson_plan_id}}" />

</form>