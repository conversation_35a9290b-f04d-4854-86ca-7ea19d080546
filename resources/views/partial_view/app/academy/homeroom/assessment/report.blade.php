<?php
use App\Models\StudentInformation;
use App\Models\ElectiveGrade;
use App\Models\SummativeAssessmentData;
use App\Models\FormativeAssessmentData;
use App\Library\Helper\ReportCardHelper;
use App\Models\ScoreTemplate;
use App\Models\UserBranch;
use App\Models\StudentClassAttendance;
use App\Library\Helper\AcademicHelper;
use App\Models\AcademicSemester;
use App\Models\Comment;
use App\Models\SettingsAssessment;

$ah = new AcademicHelper();
$academicYearId = $ah->academicYear();
$reportHelper = new ReportCardHelper();
$branchInfo = $ah->getBranchInfo();
$activeBranch = $branchInfo->branch_id;
$resultSubjects = [];
// $semesterInfo = AcademicSemester::where('start_date', '<=', date('Y-m-d'))->where('end_date', '>=', date('Y-m-d'))->where('branch_id', $activeBranch)->first();
$semesterInfo = AcademicSemester::where('is_default', 1)->where('branch_id', $activeBranch)->first();
$startDate = $semesterInfo->start_date;
$endDate = $semesterInfo->end_date;
$subjects = ElectiveGrade::select('subjects.subject_id', 'subjects.subject_name', 'academic_elective_grade.grade_id', 'subjects.subject_type')->leftJoin('academic_elective_grade_students', 'academic_elective_grade.grade_id', 'academic_elective_grade_students.grade_id')->leftJoin('subjects', 'subjects.subject_id', 'academic_elective_grade.subject_id')->where('academic_elective_grade_students.student_id', $studentId)->where('academic_elective_grade.academic_year_id', $academicYearId)->groupBy('academic_elective_grade.subject_id', 'subjects.subject_id', 'subjects.subject_name', 'academic_elective_grade.grade_id')->get();
$excluded = \DB::Table('settings_excluded_subjects')->where('academic_year_id', $academicYearId)->where('branch_id', $activeBranch)->where('grade_id', $class)->get();
$gradeIDs = [];

foreach ($subjects as $key => $subject) {
    $notExcluded = true;
    foreach ($excluded as $ex) {
        if ($subject->subject_id == 116) {
            $HR_gradeId = $subject->grade_id;
            $notExcluded = false;
        }
        if ($subject->subject_id == $ex->subject_id) {
            $subjects->forget($key);
            $notExcluded = false;
        }
    }
    if ($notExcluded) {
        $resultSubjects[$subject->subject_id]['name'] = $subject->subject_name;
        $gradeIDs[$subject->subject_id] = $subject->grade_id;
        $subjectGrades = '';
        $formativeAssessments = [];
        $allSubjectFinalScores = [];
        $allSubjectsLifeSkillGrades = [];

        $skills = [];
        $subjectLifeSkills = [];
        $subjectGrades = $reportHelper->calculateSubjectSummativeStrand([
            'student_id' => $studentId,
            'subject_id' => $subject->subject_id,
            'start_date' => $startDate,
            'end_date' => $endDate,
            'academic_year_id' => $academicYearId,
            'grade_id' => $subject->grade_id,
        ]);
        $subjectAvgDetail[$subject->subject_id] = explode('/*/', $subjectGrades);
        $strandLines[$subject->subject_id] = explode('/x/', $subjectAvgDetail[$subject->subject_id][0]);
    }
}
?>

<style>
    @import url('https://fonts.googleapis.com/css2?family=Open+Sans&display=swap');
    @import url('https://fonts.googleapis.com/css2?family=Khmer&family=Roboto:wght@400&display=swap');

    body {
        font-family: 'Roboto', sans-serif;
    }

    page {
        background: white;
        display: block;
        margin: 0 auto;
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
    }

    page[size="A4"] {
        width: 21cm;
        height: 29cm;
    }

    .subject_title {
        text-align: center;
        font-size: 28px;
        font-weight: bold;
        padding-top: 30px;
    }


    .progress-title {
        font-size: 16px;
        font-weight: 700;
        color: #333;
        margin: 0 0 20px;
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
    }

    .progress {
        height: 8px;
        background: #333;
        border-radius: 0;
        box-shadow: none;
        margin-bottom: 30px;
        overflow: visible;
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
    }

    .progress .progress-bar {
        position: relative;
    }

    .progress .progress-bar:after {
        content: "";
        display: inline-block;
        width: 13px;
        background: #fff;
        position: absolute;
        top: -15px;
        bottom: -7px;
        right: -1px;
        z-index: 1;
        transform: rotate(45deg);
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
    }

    .progress .progress-value {
        display: block;
        font-size: 16px;
        font-weight: 600;
        color: #333;
        position: absolute;
        top: -30px;
        right: 0px;
    }

    @-webkit-keyframes animate-positive {
        0% {
            width: 0;
        }
    }

    @keyframes animate-positive {
        0% {
            width: 0;
        }
    }





    .assessment-table {
        border-spacing: 1;
        border-collapse: collapse;
        //background: white;
        border-radius: 5px;
        overflow: hidden;
        width: 100%;
        margin: 0 auto;
        position: relative;
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
    }

    .assessment-table * {
        position: relative;
    }

    .assessment-table td,
    .assessment-table th {
        padding-left: 8px;
    }

    .assessment-table thead tr {
        height: 20px;
        color: black;
    }

    .assessment-table tbody tr {
        height: 25px;
    }

    .assessment-table tbody tr:last-child {
        //border: 0;
    }

    .assessment-table td,
    table th {
        text-align: left;
    }

    .assessment-table td.l,
    table th.l {
        text-align: right;
    }

    .assessment-table td.c,
    table th.c {
        text-align: center;
    }

    .assessment-table td.r,
    table th.r {
        text-align: center;
    }


    .table100-head th {
        font-size: 15px;
        color: black;
        line-height: 20px;
        font-weight: unset;
    }

    .assessment-table tbody tr:nth-child(even) {
        color: black;
    }

    .assessment-table tbody tr {
        font-size: 13px;
        color: black;
        line-height: 18px;
        font-weight: unset;
    }

    .column1 {
        width: 260px;
        padding-left: 40px;
    }

    .column2 {
        width: 160px;
    }

    .column3 {
        width: 245px;
    }

    .column4 {
        width: 110px;
        text-align: right;
    }

    .column5 {
        width: 170px;
        text-align: right;
    }

    .column6 {
        width: 222px;
        text-align: right;
        padding-right: 62px;
    }


    .listTable {
        border-collapse: collapse;
        width: 90%;
        font-size: 15px;
        font-weight: normal;
    }

    .listTable td,
    .listTable th {
        border: 1px solid #000;
        text-align: left;
        padding-left: 12px;
    }

    .listTable tr:nth-child(even) {
        background-color: #f4fafd;
    }

    .listTable th {
        padding-top: 12px;
        padding-bottom: 12px;
        padding-left: 12px;
        text-align: left;
        color: black;
    }

    .gradingTable td {
        border: 1px solid #dddddd;
        text-align: center;
        padding-left: 8px;
        padding-right: 8px;
        padding-top: 15px;
        padding-bottom: 15px;
    }

    .gradingTable th {
        border: 1px solid #dddddd;
        text-align: center;
        padding-left: 8px;
        padding-right: 8px;
        padding-top: 15px;
        padding-bottom: 15px;
    }
</style>

@foreach ($resultSubjects as $key => $subject)
    <div style="font-size: 10px; right: 0; position: absolute;"></div>
    <div class="subject_title">
        <table class="listTable" style="width: 90%;">
            <thead>
                <tr>
                    <th style="text-align: left; background-color: #3fc3ee;"><small>Student: {{ $studentId }} </small>
                    </th>
                    <th style="text-align: left; background-color: #3fc3ee;"><small>Subject: {{ $subject['name'] }}
                        </small></th>
                </tr>
            </thead>
        </table>
        <!-- SUMMATIVE ASSESSMENT TABLE -->
        <div style="text-align: center;">
            <table class="listTable">
                <thead>
                    <tr>
                        <th colspan="2" style="text-align: center; background-color: #f0f0f0;">
                            <p>ASSESSMENTS</p>
                        </th>
                    </tr>
                    <tr>
                        <th width="80%" style="text-align: left;"> Strand</th>
                        <th width="20%" style="text-align: center;">Grade</th>
                    </tr>
                </thead>
                <tbody>
                    <?php
                        foreach ($strandLines[$key] as $strRow){
                            $straDetail = explode("/_/", $strRow);
                            if ($straDetail[0] != ""){
                                ?>
                    <tr>
                        <td>{{ $straDetail[0] }}</td>
                        <td width="20%" style="text-align: center; font-weight: bold;">{{ $straDetail[1] }}</td>
                    </tr>
                    <?php
                            }
                        }
                    ?>

                </tbody>
                <tfoot>
                    <tr>
                        <td style="font-weight:bold ">Final Grade</td>
                        <td width="20%" style="text-align: center; font-weight: bold;">
                            {{ $subjectAvgDetail[$key][1] }}</td>
                    </tr>
                </tfoot>
            </table>
        </div>
        <br />
        <!--FORMATIVE ASSESSMENT TABLE-->
        <table class="listTable" style="width: 90%; border-collapse: collapse;" border="1">
            <thead style="background-color: rgba(222, 209, 209, 0.232)">
                <tr>
                    <th colspan="7" style="text-align: center;">Life Skill</th>
                </tr>
                <tr>
                    <th>Name</th>
                    <th>Skill</th>
                    <th>Strand</th>
                    <th style="padding-left: 2px; text-align: left">EE</th>
                    <th style="padding-left: 2px; text-align: left">ME</th>
                    <th style="padding-left: 2px; text-align: left">AE</th>
                    <th style="padding-left: 2px; text-align: left">BE</th>
                </tr>
            </thead>
            <tbody>
                <?php
                $formativeAssessments = FormativeAssessmentData::select('academic_formative_assessments.assessment_name as assessment_name', 'academic_formative_assessments.t1 as tt1', 'academic_formative_assessments.t2 as tt2', 'academic_formative_assessments.t3 as tt3', 'academic_formative_assessments.t4 as tt4', 'academic_formative_assessments_data.t1 as t1', 'academic_formative_assessments_data.t2 as t2', 'academic_formative_assessments_data.t3 as t3', 'academic_formative_assessments_data.t4 as t4', 'academic_formative_assessments_data.type_id as type_id', 'academic_formative_assessments_data.type_percentage as type_percentage', 'skills_strands.value as skill', 'str.value as strand')
                    ->leftJoin('academic_formative_assessments', 'academic_formative_assessments.formative_assessment_id', 'academic_formative_assessments_data.formative_assessment_id')
                    ->leftJoin('skills_strands', 'skills_strands.skill_strand_id', 'academic_formative_assessments.skill')
                    ->leftJoin('skills_strands as str', 'str.skill_strand_id', 'academic_formative_assessments.strand')
                    ->whereBetween('academic_formative_assessments.date', [$startDate, $endDate])
                    ->where('academic_formative_assessments_data.student_id', $studentId)
                    ->where('academic_formative_assessments.subject_id', $key)
                    ->where('academic_formative_assessments.academic_year_id', $academicYearId)
                    ->get();
                
                ?>
                @foreach ($formativeAssessments as $fa)
                    <?php
                    $t1Text = null;
                    $t2Text = null;
                    $t3Text = null;
                    $t4Text = null;
                    
                    if ($fa->t1 != '') {
                        $t1Text = is_numeric($fa->t1) ? $fa->t1 : '+';
                    }
                    if ($fa->t2 != '') {
                        $t1Text = is_numeric($fa->t2) ? $fa->t2 : '+';
                    }
                    if ($fa->t3 != '') {
                        $t1Text = is_numeric($fa->t3) ? $fa->t3 : '+';
                    }
                    if ($fa->t4 != '') {
                        $t1Text = is_numeric($fa->t4) ? $fa->t4 : '+';
                    }
                    ?>
                    <tr>
                        <td style="">{{ $fa->assessment_name }}</td>
                        <td>{{ $fa->skill }}</td>
                        <td>{{ $fa->strand }}</td>
                        <td style="font-weight: bold;">{{ $t1Text }}</td>
                        <td style="font-weight: bold;">{{ $t2Text }}</td>
                        <td style="font-weight: bold;">{{ $t3Text }}</td>
                        <td style="font-weight: bold;">{{ $t4Text }}</td>
                    </tr>
                @endforeach

            </tbody>
        </table>
    </div>
    <?php
    $subjectComment = Comment::where('student_id', $studentId)
        ->where('subject_id', $gradeIDs[$key])
        ->where('academic_year_id', $academicYearId)
        ->whereBetween('comment_date', [$startDate, $endDate])
        ->orderBy('comment_date', 'DESC')
        ->get();
    ?>
    <br />
    <table class="listTable" style="width: 90%; border-collapse: collapse;" border="1">
        <thead>
            <tr>
                <th colspan="2" style="background-color: rgba(222, 209, 209, 0.232)">SUBJECT COMMENTS</th>
            </tr>
            <tr>
                <th style="text-align: center;">Date</th>
                <th style="text-align: center;">Comment</th>
            </tr>
        </thead>
        <tbody>
            @foreach ($subjectComment as $comment)
                <tr>
                    <td style="padding-left: 10px;">{{ $comment->comment_date }}</td>
                    <td style="padding-left: 10px;">{{ $comment->comment }}</td>
                </tr>
            @endforeach
        </tbody>
    </table>
@endforeach
