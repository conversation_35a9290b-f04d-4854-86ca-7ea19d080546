<?php
use App\Models\Classroom;
use App\Models\SummativeAssessment;
use App\Models\SummativeAssessmentData;
use App\Models\FormativeAssessmentData;
use App\Library\Helper\AcademicHelper;

$ah = new AcademicHelper();
$academicYearId = $ah->academicYear();

$classroom = Classroom::find($classroomId);
?>
<div style="padding: 10px;">
    <table class="table table-striped border gs-5">
        <thead>
            <th>#</th>
            <th>Student</th>
            <th>Photo</th>
            <th class="text-center">Assessment</th>
            <th class="text-center">Life Skill</th>
            <th class="text-center">Report Card</th>
        </thead>
        <tbody>
            @foreach ($classroom->students as $student)
                <tr data-id="{{ $student->student_id }}">
                    <td>{{ $student->student_id }}</td>
                    <td>{{ $student->student->name ?? 'N/A' }}</td>
                    <td><img src="{{ $student->student->photo ?? '' }}" class="user-photo user-photo-zoom" /></td>
                    <td class="text-center"><a class="btn btn-primary btnShowAssessmentList" href="#"
                            data-type="summative" data-student-id="{{ $student->student_id }}">View Assessment</a></td>
                    <td class="text-center"><a class="btn btn-primary btnShowAssessmentList" href="#"
                            data-type="formative" data-student-id="{{ $student->student_id }}">View Life Skill</a></td>
                    <td class="text-center"><a class="btn btn-primary btnShowAssessmentList" href="#"
                            data-type="report" data-classroom-id ="{{ $classroomId }}"
                            data-student-id="{{ $student->student_id }}">View Report Card</a></td>
                </tr>
            @endforeach
        </tbody>
    </table>
</div>
