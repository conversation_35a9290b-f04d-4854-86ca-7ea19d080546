<?php
use App\Models\SummativeAssessment;
use App\Models\SummativeAssessmentData;
use App\Models\ScoreTemplate;
use App\Library\Helper\AcademicHelper;
use App\Models\SettingsAssessment;
$ah = new AcademicHelper();
$term = $ah->currentSemester();
$assessment = SummativeAssessment::find($id);

$templateInfo = ScoreTemplate::leftJoin('academic_summative_score_template_grades', 'academic_summative_score_template_grades.summative_template_id', 'academic_summative_score_template.summative_template_id')->leftJoin('academic_summative_score_template_data', 'academic_summative_score_template_data.summative_template_id', 'academic_summative_score_template.summative_template_id')->where('academic_summative_score_template.subject_id', $assessment->subject_id)->where('academic_summative_score_template_grades.grade_id', $assessment->grade_id)->get();

$summativeAssessmentOpt = SummativeAssessmentData::where('assessment_id', $id)->first();

$lifeSkillInfo = SettingsAssessment::leftJoin('skills_strands', 'skills_strands.skill_strand_id', 'settings_assessment.skill_strand_id')
    ->where('grade_id', $assessment->grade_id)
    // ->where('term', $term)
    ->where('skills_strands.type', 'skill')
    ->get();

$strandInfo = SettingsAssessment::leftJoin('skills_strands', 'skills_strands.skill_strand_id', 'settings_assessment.skill_strand_id')
    ->where('grade_id', $assessment->grade_id)
    // ->where('term', $term)
    ->where('skills_strands.type', 'strand')
    ->get();
$typeId = $assessment->data->pluck('type_id')->first();
?>

<div class="row mb-3">
    <label for="exampleInputuname_3" class="col-sm-3 control-label">Assessment Type</label>
    <div class="col-9">
        <p class="form-control">Assessment</p>
    </div>
</div>
<div class="row mb-3">
    <label for="exampleInputuname_3" class="col-sm-3 control-label">Assessment name</label>
    <div class="col-9">
        <p class="form-control">{{ $assessment->assessment_name }}</p>
    </div>
</div>
<div class="row mb-3">
    <label for="exampleInputuname_3" class="col-sm-3 control-label">Comment</label>
    <div class="col-9">
        <p class="form-control">{{ $assessment->comment ?? ' - ' }}</p>
    </div>
</div>

<div class="row mb-3">
    <label for="exampleInputuname_3" class="col-sm-3 control-label">Date</label>
    <div class="col-9">
        <p class="form-control">{{ $assessment->date }}</p>

    </div>
</div>
<div class="row mb-3">
    <label for="exampleInputuname_3" class="col-sm-3 control-label">Max Score</label>
    <div class="col-9">
        <p class="form-control">{{ $assessment->max_score }}</p>

    </div>
</div>

<div class="row mb-3">
    <label for="exampleInputuname_3" class="col-sm-3 control-label">Template </label>
    <div class="col-9">
        <select disabled class="form-control" data-control="select2" required name="option"
            id="editSummativeAssessmentOption" data-dropdown-parent="#modalNewAssessment"
            data-placeholder="Assessment option">
            {{-- <option value="0">None</option> --}}
            @foreach ($templateInfo as $key => $info)
                <option @if ($info->data_id == $typeId) selected @endif value="{{ $info->data_id }}"
                    data-percentage="{{ $info->percentage }}">{{ $info->title }} ({{ $info->percentage }}%)</option>
            @endforeach
        </select>
    </div>
</div>

{{-- {{$assessment}} --}}
<div class="row mb-3">
    <label for="exampleInputuname_3" class="col-sm-3 control-label">Strand </label>
    <div class="col-9">
        <select disabled class="form-control" data-control="select2" required name="strand"
            id="editSummativeAssessmentStrand" data-dropdown-parent="#modalNewAssessment" data-placeholder="Strand">
            {{-- <option value="0">None</option> --}}
            @foreach ($strandInfo as $key => $info)
                <option @if ($info->skill_strand_id == $assessment->strand) selected @endif value="{{ $info->skill_strand_id }}">
                    {{ $info->value }} </option>
            @endforeach
        </select>

    </div>
</div>
