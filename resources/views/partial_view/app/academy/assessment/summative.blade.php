<?php
use App\Library\Helper\AcademicHelper;
use App\Models\SummativeAssessment;
use App\Models\Classroom;
use App\Models\SummativeAssessmentData;
use App\Models\ElectiveGradeStudent;
use App\Models\AcademicSemester;
use App\Models\ScoreTemplateData;
use App\Models\SettingsAssessment;
use App\Library\Helper\ReportCardHelper;

$reportHelper = new ReportCardHelper();
$ah = new AcademicHelper();

$academicYearId = $ah->academicYear();
$semesters = AcademicSemester::where('academic_year_id', $academicYearId)->where('branch_id', $ah->currentBranch())->get();
if ($searchSemester != 'null') {
    $currentSemester = AcademicSemester::where('semester_id', $searchSemester)->where('academic_year_id', $academicYearId)->where('branch_id', $ah->currentBranch())->first();
} elseif ($searchSemester != 'all') {
    $currentSemester = AcademicSemester::where('academic_year_id', $academicYearId)->where('branch_id', $ah->currentBranch())->where('is_default', 1)->first();
}
$assessments = SummativeAssessment::with('skill_strand')
    ->select('academic_summative_assessments.*', 'str.value as strand_title', 'skl.value as skill_title', \DB::raw('(select concat(" [" ,type_percentage, "%]") as optData from  academic_summative_assessments_data WHERE academic_summative_assessments_data.assessment_id = academic_summative_assessments.assessment_id limit 1) as optData'))
    ->leftJoin('skills_strands as str', 'str.skill_strand_id', 'academic_summative_assessments.strand')
    ->leftJoin('skills_strands as skl', 'skl.skill_strand_id', 'academic_summative_assessments.skill')
    ->where('academic_year_id', $ah->academicYear())
    ->where('academic_summative_assessments.branch_id', $ah->currentBranch())
    ->where('grade_id', $gradeId)
    ->orderBy('is_final_exam', 'asc')
    ->when(isset($currentSemester), function ($query) use ($currentSemester) {
        return $query->whereBetween('date', [$currentSemester->start_date, $currentSemester->end_date]);
    })
    ->get();
$assessmentIds = $assessments->pluck('assessment_id');
$assessmentGroupStrands = $assessments->groupBy('strand');
$students = ElectiveGradeStudent::with('student')->leftJoin('student_information', 'academic_elective_grade_students.student_id', '=', 'student_information.id')->where('grade_id', $gradeId)->where('student_status', 1)->orderBy('student_information.name')->get();

// Calculate Missing Impact
$group_data = $assessments->groupBy('strand');
$tempArr = [];
$assessmentIds = $assessments->pluck('assessment_id')->toArray();
$summativeData = SummativeAssessmentData::whereIn('assessment_id', $assessmentIds)->get()->groupBy('assessment_id');
foreach ($group_data as $strand_type_id => $data_assessments) {
    $strand_title = optional($data_assessments->first())->strand_title;
    $templateId = $data_assessments->pluck('template_id')->unique()->first();
    $typeIdArr = $data_assessments->pluck('assessment_id')->map(fn($id) => $summativeData->get($id)?->first()?->type_id)->filter()->unique()->toArray();
    if ($data_assessments[0]->is_final_exam == 1) {
        $missing_template_data = ScoreTemplateData::where('summative_template_id', $templateId)->whereNotIn('data_id', $typeIdArr)->where('is_final_exam_percentage', 1)->get();
    } else {
        $missing_template_data = ScoreTemplateData::where('summative_template_id', $templateId)->whereNotIn('data_id', $typeIdArr)->where('is_final_exam_percentage', 0)->get();
    }
    // dd($missing_template_data);
    if ($missing_template_data->isNotEmpty()) {
        $obj = (object) [
            'strand_type_id' => $strand_type_id,
            'strand_title' => $strand_title,
            'missing_template_data' => $missing_template_data,
            'templateId' => $templateId,
        ];
        $tempArr[] = $obj;
    }
}
$usedStrandIds = $assessments->pluck('strand')->filter()->unique();
$missingStrands = SettingsAssessment::with('skillStrand')->where('grade_id', $gradeId)
            // ->where('term', $term)
            // ->where('skills_strands.type', 'strand')
            ->when(isset($currentSemester), function ($query) use ($currentSemester) {
                return $query->where('term', [$currentSemester->academic_semester]);
                })
                ->whereNotIn('skill_strand_id',$usedStrandIds)
            ->get();
// dd($missingStrands);
?>

<style>
    .table_header {
        padding-left: 10px;
        width: 150px !important;
    }

    #summativeTable th:first-child,
    #summativeTable td:first-child {
        position: sticky;
        left: 0;
        z-index: 1;
        /* Ensure it's on top of other columns */
    }
</style>
<style>
  @keyframes blink {
    0%, 100% { opacity: 1; }
    50% { opacity: 0; }
  }

  .blinking-dot {
    animation: blink 1.2s infinite;
  }
</style>
{{-- @if ($missingStrands)
    <div class="table-responsive border p-4 rounded">
        <h5 class="text-danger text-center">Missing Strands </h5>
        <table id="missingStrandPercentageTable" class="table table-rounded table-striped ">
            <thead>
                <tr class="fw-bold">
                    <th style="padding-left: 10px; width:150px !important; white-space: nowrap;">Title</th>
                </tr>
            </thead>
            <tbody>
                @foreach ($missingStrands as $missingStrand)
                    <tr>
                        <td class="fw-bold  text-danger" style="padding-left: 10px; width:150px !important; white-space: nowrap;">
                            {{ $missingStrand->skillStrand->value }}
                        </td>
                    </tr>
                @endforeach
            </tbody>
        </table>
    </div>
@endif
@if ($tempArr)
    <div class="table-responsive border p-4 rounded">
        <h5 class="text-danger text-center">Missing Impact </h5>
        <table id="missingStrandPercentageTable" class="table table-rounded table-striped ">
            <thead>
                <tr class="fw-bold">
                    <th style="padding-left: 10px; width:150px !important; white-space: nowrap;">Title</th>
                    <th>Missing Impact Title</th>
                    <th>Missing Impact %</th>
                </tr>
            </thead>
            <tbody>
                @foreach ($tempArr as $missing_strand)
                    <tr>
                        <td class="fw-bold" style="padding-left: 10px; width:150px !important; white-space: nowrap;">
                            {{ $missing_strand->strand_title }}</td>
                        <td class=" text-danger">
                            @foreach ($missing_strand->missing_template_data as $missing_template_data)
                                {{ $missing_template_data->title }}
                                <br>
                            @endforeach
                        </td>
                        <td class=" text-danger">
                            @foreach ($missing_strand->missing_template_data as $missing_template_data)
                                {{ $missing_template_data->percentage }} %
                                <br>
                            @endforeach
                        </td>
                    </tr>
                @endforeach
            </tbody>
        </table>
    </div>
@endif --}}

@if (($missingStrands && $missingStrands->count()) || ($tempArr && count($tempArr)))
    <div class="row mb-3">
        <div class="col-md-6 mb-2 text-center">
        @if ($missingStrands && $missingStrands->count())
        <button class="btn btn-dark position-relative" type="button" id="missingStrandCollapse">
        Show / Hide Missing Strands
        <span class="position-absolute top-0 start-100 translate-middle p-1 bg-danger border border-light rounded-circle blinking-dot"></span>
        </button>
            {{-- </div> --}}
        @endif
  </div>
   <div class="col-md-6 mb-2 text-center">
        @if ($tempArr && count($tempArr))
            {{-- <div class="col-md-6 mb-2"> --}}
                <button class="btn btn-dark position-relative" type="button" id="missingImpactCollapse">
                Show / Hide Missing Impact
                <span class="position-absolute top-0 start-100 translate-middle p-1 bg-danger border border-light rounded-circle blinking-dot"></span>
                </button>
                @endif
            </div>
    </div>
@endif

@if ($missingStrands && $missingStrands->count())
    <div class=" mb-3" id="missingStrandCollapseModal" >
        <div class="table-responsive border p-3 rounded">
            <h5 class="text-danger text-center">Missing Strands</h5>
            <table class="table table-rounded table-striped">
                <thead>
                    <tr class="fw-bold">
                        <th style="padding-left: 10px; white-space: nowrap;">Title</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach ($missingStrands as $missingStrand)
                        <tr>
                            <td class="fw-bold text-danger" style="padding-left: 10px; white-space: nowrap;">
                                {{ $missingStrand->skillStrand->value }}
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>
@endif

@if ($tempArr && count($tempArr))
    <div class=" mb-3" id="missingImpactCollapseModal" >
        <div class="table-responsive border p-3 rounded">
            <h5 class="text-danger text-center">Missing Impact</h5>
            <table class="table table-rounded table-striped">
                <thead>
                    <tr class="fw-bold">
                        <th style="padding-left: 10px; white-space: nowrap;">Title</th>
                        <th>Missing Impact Title</th>
                        <th>Missing Impact %</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach ($tempArr as $missing_strand)
                        <tr>
                            <td class="fw-bold" style="padding-left: 10px; white-space: nowrap;">
                                {{ $missing_strand->strand_title }}
                            </td>
                            <td class="text-danger">
                                @foreach ($missing_strand->missing_template_data as $data)
                                    {{ $data->title }}<br>
                                @endforeach
                            </td>
                            <td class="text-danger">
                                @foreach ($missing_strand->missing_template_data as $data)
                                    {{ $data->percentage }}%<br>
                                @endforeach
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>
@endif

<hr class="mt-4">
<div class="row ">
    <div class="col-12 col-md-2 col-xl-2 mt-2">
        <select class="form-control" data-control="select2" id="semester">
            <option value="" disabled selected>Select Term</option>
            <option value="all">All Term</option>
            @foreach ($semesters as $semester)
                <option value="{{ $semester->semester_id }}" @if ($semester->semester_id == ($currentSemester ? $currentSemester->semester_id : $searchSemester)) selected @endif>Term
                    {{ $semester->academic_semester }}</option>
            @endforeach
        </select>
        <input type="hidden" value="{{ $gradeId }}" id="gradeId">
    </div>
    <div class="col-auto mt-2 me-4">
        <button type="button" class="btn btn-primary" id="summativeSemesterSearch">
            {{ __('general.search') }}
        </button>
    </div>
</div>

<div class="table-responsive">
    <table class="table table-rounded  border" id="summativeTable">
        <thead>
            <tr class="fw-semibold fs-6 text-gray-800 border-bottom border-gray-200">
                <th class="table_header border-end border-right-2 bg-body"></th>
                @foreach ($assessmentGroupStrands as $assessmentGroupStrand)
                    <th colspan="{{ count($assessmentGroupStrand) + 1 }}" style="font-weight:bold;font-size:15px"
                        class="border-start border-right-2   text-center">
                        {{ $assessmentGroupStrand[0]->skill_strand->value ?? ' - ' }}</th>
                @endforeach
                <th style="padding-right: 10px;"></th>
            </tr>
            <tr class="fw-semibold fs-6 text-gray-800 border-bottom border-gray-200">
                <th class="table_header border-end border-right-2 bg-body"></th>
                @foreach ($assessmentGroupStrands as $assessmentGroupStrand)
                    @foreach ($assessmentGroupStrand as $assessment)
                        <th class="border-start border-end" style="white-space: nowrap; padding: 5px; ">
                            <div
                                style="
                      max-height: 100px;
                      overflow: auto;
                      white-space: normal; 
                      display: block;
                      line-height: 1.2em;">
                                @foreach (explode(' ', $assessment->assessment_name) as $word)
                                    {{ $word }}<br>
                                @endforeach
                                {{-- {{$assessment->assessment_name}} --}}
                            </div>
                            <a href="#" title="Edit Assessment" class="btnEditSummative ms-2"
                                data-id="{{ $assessment->assessment_id }}" data-grade-id="{{ $gradeId }}">
                                <i class="fa-solid fa-pen-to-square"></i>
                            </a>
                            <a href="#" title="View Assessment" class="btnViewSummative ms-2"
                                data-id="{{ $assessment->assessment_id }}" data-grade-id="{{ $gradeId }}">
                                <i class="fa-solid fa-eye"></i>
                            </a>
                            <a href="#" title="Delete Assessment" class="btnDeleteSummative me-2"
                                data-id="{{ $assessment->assessment_id }}" data-grade-id="{{ $gradeId }}">
                                <i class="fa-solid fa-trash"></i>
                            </a>
                        </th>
                    @endforeach
                    <th class="border-start border-end text-center border-right-2"></th>
                @endforeach
                <th style="padding-right: 10px; border-right-2"></th>
            </tr>
            <tr class="fw-semibold fs-6 text-gray-800 border-bottom border-gray-200">
                <th class="table_header border-end border-right-2 bg-body" style="padding-left: 10px">Student Name
                </th>
                @foreach ($assessmentGroupStrands as $assessmentGroupStrand)
                    @foreach ($assessmentGroupStrand as $assessment)
                        <th class="border-start border-end  text-center" style=" white-space: nowrap;">
                            <span class="text-danger">({{ $assessment->date }})</span><br>
                            <span class="text-info">{{ $assessment->optData }}</span><br>

                            <span class="text-success">(Max - {{ $assessment->max_score }} )</span>

                        </th>
                    @endforeach
                    <th class="border-start text-center border-right-2" style="font-weight:bold">Total</th>
                @endforeach
                <th style="padding-right: 10px; font-weight:bold"> AVERAGE</th>
            </tr>
        </thead>
        <tbody>
            {{-- <div class="my-2 text-end text-danger fw-bold">** If you don't fill in one of the impact fields, the input
                will turn
                red. **</div> --}}
            @foreach ($students as $student)
                <tr class="border-bottom ">
                    <td style="padding-left: 10px; min-width:150px;position: sticky;"
                        class=" border-end border-right-2 bg-body">
                        {{ $student->student->name }}</td>
                    <?php
                 if(count($assessments) > 0 ){
                    $total_count = 0;
                    $total_assessment_point = 0;
                    $final_assessment_point = 0;
                    $summativeAssessments = SummativeAssessmentData::leftJoin('academic_summative_assessments', 'academic_summative_assessments.assessment_id', 'academic_summative_assessments_data.assessment_id')
                        ->leftJoin('skills_strands', 'skills_strands.skill_strand_id', 'academic_summative_assessments.strand')
                        ->when(isset($currentSemester), function ($query) use ($currentSemester) {
                                                    return $query->whereBetween('academic_summative_assessments.date', [$currentSemester->start_date, $currentSemester->end_date]);
                                                })
                        ->where('academic_summative_assessments_data.student_id', $student->student->id)
                        ->whereNotNull('academic_summative_assessments_data.score')
                        ->where('academic_summative_assessments.subject_id', $assessments[0]->subject_id)
                        ->where('academic_summative_assessments.grade_id', $gradeId)
                        ->whereIn('academic_summative_assessments_data.assessment_id', $assessmentIds)
                        ->where('academic_summative_assessments.academic_year_id', $academicYearId)
                        ->where('academic_summative_assessments.is_final_exam',1)
                        ->get();
                    $finalSubjectAverage = $reportHelper->calculateSubjectAverage([
                    'student_id'    => $student->student->id,
                    'subject_id'    => $assessments[0]->subject_id,
                    // 'start_date'    => $currentSemester->start_date,
                    // 'end_date'      => $currentSemester->end_date,
                    'academic_year_id'  => $academicYearId,
                    'print_summative'   => 1,
                    'print_formative'   => 0,
                    'summative_assessments' => $summativeAssessments,
                    'formative_assessments' => [],
                    ]);
                    $subjectAvgDetail = explode("/*/", $finalSubjectAverage);
                    $nFinalAveragePoint = $subjectAvgDetail[0];
                    $nFinalAveragePercentage = ($nFinalAveragePoint/($subjectAvgDetail[5] == 0 ? 1 : $subjectAvgDetail[5])) *100;
                    // print_r($nFinalAveragePercentage);
            foreach ($assessmentGroupStrands as $strand_key => $assessmentGroupStrand) {
                $summativeAssessments = SummativeAssessmentData::leftJoin('academic_summative_assessments', 'academic_summative_assessments.assessment_id', 'academic_summative_assessments_data.assessment_id')
                    ->leftJoin('skills_strands', 'skills_strands.skill_strand_id', 'academic_summative_assessments.strand')
                    ->when(isset($currentSemester), function ($query) use ($currentSemester) {
                                                return $query->whereBetween('academic_summative_assessments.date', [$currentSemester->start_date, $currentSemester->end_date]);
                                            })
                    ->where('academic_summative_assessments_data.student_id', $student->student->id)
                    ->whereNotNull('academic_summative_assessments_data.score')
                    ->where('academic_summative_assessments.subject_id', $assessments[0]->subject_id)
                    ->where('academic_summative_assessments.grade_id', $gradeId)
                    ->where('academic_summative_assessments.strand', $strand_key)
                    ->whereIn('academic_summative_assessments_data.assessment_id', $assessmentIds)
                    ->where('academic_summative_assessments.academic_year_id', $academicYearId)
                    ->get();
                  $strandSubjectAverage = $reportHelper->calculateSubjectAverage([
                'student_id'    => $student->student->id,
                'subject_id'    => $assessments[0]->subject_id,
                // 'start_date'    => $currentSemester->start_date,
                // 'end_date'      => $currentSemester->end_date,
                'academic_year_id'  => $academicYearId,
                'print_summative'   => 1,
                'print_formative'   => 0,
                'summative_assessments' => $summativeAssessments,
                'formative_assessments' => [],
            ]);
            $strandSubjectAvgDetail = explode("/*/", $strandSubjectAverage);
            $nStrandAveragePoint = $strandSubjectAvgDetail[0];
            $strandSubjectNADetail = explode("|", $strandSubjectAvgDetail[3]);
            if($strandSubjectAvgDetail[3] ==null && $assessmentGroupStrand[0]->is_final_exam == 0){
               $input = $assessmentGroupStrand[0]->optData; // e.g. " [60%]"
                preg_match('/(\d+)\%/', $input, $matches);
                $intOptData = isset($matches[1]) ? (int)$matches[1] : 0;
                $extraPoint =$intOptData *($nFinalAveragePercentage/100);
                 $total_assessment_point += $extraPoint;
                // print_r($extraPoint); 
            }
            $nStrandAveragePercentage = $strandSubjectAvgDetail[5];
            if($assessmentGroupStrand[0]->is_final_exam == 1){
                   $final_assessment_point += $nStrandAveragePoint;
            }else{
                $total_count ++;
                $total_assessment_point +=$nStrandAveragePoint; 
            }
            $assessmentData = SummativeAssessmentData::whereIn('assessment_id', $assessmentGroupStrand->pluck('assessment_id'))
                                                    ->where('student_id', $student->student->id)
                                                    ->get()
                                                    ->keyBy('assessment_id');
                                                    // echo "<pre>";
                                                    // echo json_encode($assessmentData, JSON_PRETTY_PRINT);
                                                    // echo "</pre>";
            $disabled_bg_red = '';       
            $bg_red = '';       
            foreach ($assessmentData as $counter => $as_d) {
                if($as_d->score === null){
                    $hasSameTypeId = false;
                    foreach ($assessmentData as $other) {
                        // Check if there is another item with the same type_id
                        if ($other !== $as_d && $other->type_id === $as_d->type_id && $other->score !== null) {
                            $hasSameTypeId = true;
                            break;
                        }
                    }
                    if (!$hasSameTypeId) {
                        $disabled_bg_red = 'background-color:#ee7070;';
                        $bg_red = 'background-color:#ff4f4f; color:black;';
                    }
                }
            }
                // Loop through assessments and get data from the pre-fetched collection
                foreach ($assessmentGroupStrand as $assessment) {
                    $data = $assessmentData->get($assessment->assessment_id);
                    $score = $data->score ?? null;
                    $assessment_data_id = $data->data_id ?? null;
                   if($assessment->is_locked == 0 && ($assessment->teacher_id == \Auth::user()->id)){
                    ?>
                    <td class="border-start border-end ">
                        <input class="form-control summativeScore table-summative-input  " type="number"
                            value="{{ $score }}" data-id="{{ $assessment_data_id }}"
                            assessment-id="{{ $assessment->assessment_id }}"
                            assessment-max_score="{{ $assessment->max_score }}" student-id="{{ $student->id }}"
                            style=" min-width: 70px; {{ $bg_red }}">
                    </td>
                    <?php
                    }else{
                        ?>
                    <td class="border-start border-end "><input
                            class="form-control summativeScore table-summative-input" type="number"
                            value="{{ $score }}" data-id="{{ $assessment_data_id }}"
                            assessment-id="{{ $assessment->assessment_id }}"
                            assessment-max_score="{{ $assessment->max_score }}" student-id="{{ $student->id }}"
                            disabled style="min-width: 70px; {{ $disabled_bg_red }}"></td>
                    <?php
                    }
                }
                ?>
                    <td style="padding-right: 10px; font-weight:bold; font-size:14px;"
                        class=" text-center align-middle border-end border-right-2">
                        {{ $nStrandAveragePoint }} <span class="text-secondary fs-8">/{{ $nStrandAveragePercentage }}
                        </span></td>
                    <?php
            }
            }
                ?>
                    <td style="padding-right: 10px; font-weight:bold; font-size:14px; color:rgb(24, 155, 24);"
                        class=" text-center align-middle">
                        @if (count($assessments) > 0)
                            {{-- {{ $nAveragePoint }} --}}
                            @php
                                if ($total_count != 0) {
                                    $total_wo_final = $total_assessment_point / $total_count;
                                } else {
                                    $total_wo_final = $total_assessment_point;
                                }
                            @endphp
                            {{ round($total_wo_final + $final_assessment_point, 0) }}
                        @endif
                    </td>
                </tr>
            @endforeach
        </tbody>
    </table>
</div>
