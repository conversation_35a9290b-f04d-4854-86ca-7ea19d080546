Strong<?php
    use App\Models\FormativeAssessment;
    use App\Models\FormativeAssessmentData;
    use App\Models\ElectiveGradeStudent;
    use App\Library\Helper\AcademicHelper;
    use App\Models\AcademicTerm;

    $ah = new AcademicHelper();
    $lockedTerms = AcademicTerm::where('academic_year_id', $ah->academicYear())
                         ->where('branch_id', $ah->currentBranch())
                         ->where('is_locked', 1)
                         ->get();

    $assessment = FormativeAssessment::find($id);

    $statusText = '';
    $isLocked = false;
    foreach ($lockedTerms as $key => $lt) {
        $startDate = new Datetime($lt->start_date);
        $endDate = new Datetime($lt->end_date);
        $aDate = new Datetime($assessment->date);

        if($aDate >= $startDate && $aDate <= $endDate) {
            $statusText = '<span class="badge badge-danger badge-lg"><i class="fas fa-lock"></i>&nbsp;&nbsp;Assessment is locked!</span>';
            $isLocked = true;
            break;
        }
    }


    $students = ElectiveGradeStudent::where('grade_id', $assessment->grade_id)
                                    ->whereHas('student')
                                    ->get();
?>
<div class="text-end">@if($isLocked) <?php echo $statusText; ?> @endif</div>
<div class="row">
    <div class="col-9">
        <table class="table table-rounded table-striped border" id="formativeDataArea">
            <thead>
                <tr class="fw-semibold fs-6 text-gray-800 border-bottom border-gray-200">
                    <th style="padding-left: 10px;">#</th>
                    <th>{{ __('app/academy/assessment.student') }}</th>
                    <th>{{ __('app/academy/assessment.photo') }}</th>
                    <th class="text-center">{{ __('Strong') }}</th>
                    <th class="text-center">{{ __('Satisfactory') }}</th>
                    <th class="text-center">{{ __('Needs Improvement') }}</th>
                    <th class="text-center">{{ __('Not Taught') }}</th>
                    <th class="text-center">{{ __('app/academy/assessment.comments') }}</th>
                </tr>
            </thead>
            <tbody>
                @foreach ($students as $student)
                    <?php
                        $studentId = $student->student_id;
                        $t1 = null;
                        $t2 = null;
                        $t3 = null;
                        $t4 = null;
                        $comment = null;
                        $dataId = null;
                        $isFound = false;

                        foreach ($assessment->data as $data) {
                            if($data->student_id == $student->student_id) {
                                $t1 = $data->t1;
                                $t2 = $data->t2;
                                $t3 = $data->t3;
                                $t4 = $data->t4;
                                $comment = $data->comment;
                                $isFound = true;
                                continue;
                            }
                        }

                        if(!$isFound) {
                            $typeID         = null;
                            $typePercentage = null;

                            foreach ($assessment->data as $data) {
                                if($data->type->data_id != '') {
                                    $typeID         = $data->type->data_id;
                                    $typePercentage = $data->type->percentage;
                                    continue;
                                }
                            }

                            $newRecord = FormativeAssessmentData::create([
                                'formative_assessment_id'     => $id,
                                'student_id'                  => $student->student_id,
                                'type_id'                     => $typeID,
                                'type_percentage'             => $typePercentage
                            ]);

                            $dataId = $newRecord->assessment_data_id;

                        }
                    ?>
                    <tr class="scoreRowFormative" data-student="{{$student->student_id}}">
                        <td style="padding-left: 10px;">{{$student->student_id}}</td>
                        <td>{{$student->student->name ?? ''}}</td>
                        <td><img src="{{$student->student->photo ?? ''}}" class="user-photo user-photo-zoom" /></td>
                        <td><input @if($isLocked) disabled @endif type="text" class="form-control text-center virtualKeypad {{$student->student_id}}_t1" value="{{$t1}}" /></td>
                        <td><input @if($isLocked) disabled @endif type="text" class="form-control text-center virtualKeypad {{$student->student_id}}_t2" value="{{$t2}}" /></td>
                        <td><input @if($isLocked) disabled @endif type="text" class="form-control text-center virtualKeypad {{$student->student_id}}_t3" value="{{$t3}}" /></td>
                        <td><input @if($isLocked) disabled @endif type="text" class="form-control text-center virtualKeypad {{$student->student_id}}_t4" value="{{$t4}}" /></td>
                        <td><input @if($isLocked) disabled @endif type="text" class="form-control text-center comment-box_{{$student->student_id}}" value="{{$comment}}" /></td>
                    </tr>
                @endforeach
            </tbody>
        </table>
    </div>

    <div class="col-3"  @if($isLocked) style="display: none;" @endif>
        <table class="table">
            <tr>
                <td colspan="4" class="text-center">{{ __('app/academy/assessment.click_copy') }}</td>
            </tr>
            @if ($ah->currentBranch() != 2)
            <tr>
                <td style="padding: 5px;"><a class="btn btn-secondary w-100 keypad" data-symbol="✔">✔</a></td>
                <td style="padding: 5px;"><a class="btn btn-secondary w-100 keypad" data-symbol="✖">✖</a></td>
                <td style="padding: 5px;"><a class="btn btn-secondary w-100 keypad" data-symbol="❀">❀</a></td>
                <td style="padding: 5px;"><a class="btn btn-secondary w-100 keypad" data-symbol="❤">❤</a></td>
            </tr>
            @endif
            
            <tr>
                <td style="padding: 5px;"><a class="btn btn-secondary w-100 keypad" data-symbol="1">1</a></td>
                <td style="padding: 5px;"><a class="btn btn-secondary w-100 keypad" data-symbol="2">2</a></td>
                <td style="padding: 5px;"><a class="btn btn-secondary w-100 keypad" data-symbol="3">3</a></td>
                <td style="padding: 5px;"><a class="btn btn-secondary w-100 keypad" data-symbol="4">4</a></td>
            </tr>
            <tr>
                <td style="padding: 5px;"><a class="btn btn-secondary w-100 keypad" data-symbol="5">5</a></td>
                <td style="padding: 5px;"><a class="btn btn-secondary w-100 keypad" data-symbol="6">6</a></td>
                <td style="padding: 5px;"><a class="btn btn-secondary w-100 keypad" data-symbol="7">7</a></td>
                <td style="padding: 5px;"><a class="btn btn-secondary w-100 keypad" data-symbol="8">8</a></td>
            </tr>
            <tr>
                <td style="padding: 5px;"><a class="btn btn-secondary w-100 keypad" data-symbol="9">9</a></td>
                <td style="padding: 5px;"><a class="btn btn-secondary w-100 keypad" data-symbol="0">0</a></td>
                <td style="padding: 5px;"><a class="btn btn-secondary w-100 keypad" data-symbol="">-</a></td>
                <td style="padding: 5px;"><a class="btn btn-secondary w-100 keypad" data-symbol="">-</a></td>
            </tr>
        </table>
    </div>
</div>

<script>
    $(document).ready(function(){
        var isLocked = '{{$isLocked}}';
        if(isLocked) {
            $('#btnSubmitKgFormativeData').hide();
        } else {
            $('#btnSubmitKgFormativeData').show();
        }
    });
</script>
