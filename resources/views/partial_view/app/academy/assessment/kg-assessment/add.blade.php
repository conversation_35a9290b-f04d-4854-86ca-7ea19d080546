<?php 
    use App\Models\TimeTable;
    use App\Library\Helper\AcademicHelper;
    use Carbon\Carbon;

    $ah = new AcademicHelper();
    $timetable = $ah->userTimetable(\Auth::user()->id);
    $days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
?>

<style>
    @keyframes animateBackground {
        from {background-color: #ffffff;}
        42%{background-color: #f1f1f1;}
        to {border-bottom-color: #ffffff;}
    }

    .currentDay {
        animation: animateBackground 1s infinite;
    }
   
</style>
<div class="row">
    <div class="col-lg-6 col-sm-4 col-xs-12">
        <table class="table table-rounded border table-striped gs-7" id="aListTable">
            <thead>
                <th>{{ __('app/academy/assessment.grade') }}</th>
                <th>{{ __('app/academy/assessment.subject') }}</th>
            </thead>
            <tbody>
                @foreach ($timetable as $session)
                    <tr style="cursor: pointer;" data-grade="{{$session->grade_id}}" data-subject="{{$session->subject_id}}" class="sessionKgRow">
                        <td>{{$session->elective_grade->grade_name ?? '[Deleted]'}}</td>
                        <td>{{$session->subject->subject_name}}</td>
                    </tr>
                @endforeach
            </tbody>
        </table>
    </div>

    <div class="col-lg-6 col-sm-8 col-xs-12" id="newSummativeDetails">        
        <form id="formNewAssessment">

            <table class="table table-rounded border" id="summativeOptTable" style="opacity: 0.3; pointer-events: none;">
                <tr>
                    <td style="padding-left: 10px;">{{ __('app/academy/assessment.type') }}</td>
                    <td>
                        <select class="types form-control" data-control="select2" required id="type" name="type" data-dropdown-parent="#modalNewAssessment" data-placeholder="Select Type">
                            <!-- <option></option> -->
                            <!-- <option value="summative">{{ __('app/academy/assessment.summative') }}</option> -->
                            <option value="formative">{{ __('app/academy/assessment.formative') }}</option>
                        </select>
                    </td>
                </tr>

                <tr>
                    <td style="padding-left: 10px;">{{ __('app/academy/assessment.learning_area') }}</td>
                    <td>
                        <select id="learningAreasSelect" class="form-control" data-control="select2" required name="learning_area">
                            <option value="">Select Learning Area</option>
                        </select>
                    </td>
                </tr>

                <tr>
                    <td style="padding-left: 10px;">{{ __('app/academy/assessment.date') }}</td>
                    <td><input type="date" class="form-control" required name="date" /></td>
                </tr>

                <tr>
                    <td style="padding-left: 10px;">{{ __('app/academy/assessment.option') }}</td>
                    <td>
                        <select class="form-control" data-control="select2" required name="option" id="assessmentOption" data-dropdown-parent="#modalNewAssessment" data-placeholder="-">
                        </select>
                    </td>
                </tr>
                
            </table>
            {{-- <input type="hidden" name="timetable_id" id="assessment_timetable_id" required /> --}}
            <input type="hidden" name="na_grade_id" id="na_grade_id" required />
            <input type="hidden" name="na_subject_id" id="na_subject_id" required />
        </form>
    </div>
</div>