@php
    use App\Models\FormativeAssessment;
    use App\library\Helper\AcademicHelper;
    use App\Models\AcademicTerm;

    $ah = new AcademicHelper;

    $lockedTerms = AcademicTerm::where('academic_year_id', $ah->academicYear())
                         ->where('branch_id', $ah->currentBranch())
                         ->where('is_locked', 1)
                         ->get();

    $assessments = FormativeAssessment::where('academic_year_id', $ah->academicYear())
                                      ->where('branch_id', $ah->currentBranch())
                                      ->where('teacher_id', auth()->user()->id)
                                      ->orderBy('date', 'desc')
                                      ->get();
@endphp

<div class="table-responsive">
    <table class="table table-rounded table-striped border" id="formativeTable">
        <thead>
            <tr class="fw-semibold fs-6 text-gray-800 border-bottom border-gray-200">
                <th style="padding-left: 10px;">{{ __('app/academy/assessment.assessment') }}</th>
                <th>{{ __('app/academy/assessment.grade') }}</th>
                <th>{{ __('app/academy/assessment.subject') }}</th>
                <th>{{ __('app/academy/assessment.date') }}</th>
                <th>{{ __('app/academy/assessment.template') }}</th>
                <th>{{ __('general.status') }}</th>
                <th></th>
            </tr>
        </thead>
        <tbody>
            @foreach ($assessments as $assessment)
                @php
                    $statusText = '<i class="fas fa-lock-open"></i> ' . __('Unlocked');
                    $isLocked = false;
                    foreach ($lockedTerms as $lt) {
                        $startDate = new DateTime($lt->start_date);
                        $endDate = new DateTime($lt->end_date);
                        $aDate = new DateTime($assessment->date);

                        if ($aDate >= $startDate && $aDate <= $endDate) {
                            $statusText = '<span class="badge badge-danger badge-lg"><i class="fas fa-lock"></i>&nbsp;&nbsp;' . __('Locked') . '</span>';
                            $isLocked = true;
                            break;
                        }
                    }
                @endphp

                <tr>
                    <td style="padding-left: 10px;">{{ $assessment->assessment_name }}</td>
                    <td>{{ $assessment->grade->grade_name ?? 'N/A' }}</td>
                    <td>{{ $assessment->subject->subject_name ?? 'N/A' }}</td>
                    <td>{{ $assessment->date }}</td>
                    <td>
                        {{ $assessment->type->title ?? '' }} 
                        [{{ $assessment->type->percentage ?? '' }}%]
                    </td>
                    <td>{!! $statusText !!}</td>
                    <td style="text-align: right; padding-right: 10px;">
                        <a href="#" data-id="{{ $assessment->formative_assessment_id }}" data-bs-toggle="modal" data-bs-target="#modalFormativeData" class="btnViewKGFormative"><i class="fas fa-eye"></i></a>
                        @if(!$isLocked)
                            <a href="#" data-id="{{ $assessment->formative_assessment_id }}" data-title="{{ $assessment->assessment_name }}" class="btnEditFormative"><i class="fas fa-edit"></i></a>
                            <a href="#" data-id="{{ $assessment->formative_assessment_id }}" class="btnDeleteKgFormative"><i class="fas fa-trash"></i></a>
                        @endif
                    </td>
                </tr>
            @endforeach
        </tbody>
    </table>
</div>

<div class="modal bg-white fade" tabindex="-1" id="modalFormativeData">
    <div class="modal-dialog modal-fullscreen">
        <div class="modal-content shadow-none">
            <div class="modal-header">
                <h5 class="modal-title">Formative Assessment Detail</h5>
                <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal" aria-label="Close">
                    <span class="svg-icon svg-icon-2x"></span>
                </div>
            </div>
            <div class="modal-body" id="formativeKGDataArea">
                <p style="text-align: center;"><img src="/img/loader.gif" style="width: 30px;" /></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-light" data-bs-dismiss="modal">{{ __('general.close') }}</button>
                <button type="button" class="btn btn-primary" id="btnSubmitKgFormativeData">{{ __('general.save') }}</button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" tabindex="-1" id="modalFormativeEdit">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ __('app/academy/assessment.assessment_name') }}</h5>
                <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal" aria-label="Close">
                    <span class="svg-icon svg-icon-2x"></span>
                </div>
            </div>
            <div class="modal-body" id="formativeEditArea"></div>
            <div class="modal-footer">
                <button type="button" class="btn btn-light" data-bs-dismiss="modal">{{ __('general.close') }}</button>
                <button type="button" class="btn btn-primary" id="btnSaveKgFormativeName">{{ __('general.save') }}</button>
            </div>
        </div>
    </div>
</div>
