<?php 
    use App\Models\FormativeAssessment;
    use App\Models\FormativeAssessmentData;
    use App\Models\ScoreTemplate;
    use App\Models\LearningArea;

    $assessment = FormativeAssessment::find($id);
    // dd($assessment->grade_id);

    $learningAreas = LearningArea::where('academic_year_id', $assessment->academic_year_id)
                                ->where('branch_id', $assessment->branch_id)
                                ->whereJsonContains('grades', (string)$assessment->grade_id) // Ensure grade_id is cast to string
                                ->get();

    $templateInfo = ScoreTemplate::leftJoin(
                                            'academic_summative_score_template_grades', 
                                            'academic_summative_score_template_grades.summative_template_id', 
                                            'academic_summative_score_template.summative_template_id'
                                        )
                                     ->leftJoin(
                                            'academic_summative_score_template_data', 
                                            'academic_summative_score_template_data.summative_template_id', 
                                            'academic_summative_score_template.summative_template_id'
                                        )
                                     ->where('academic_summative_score_template.subject_id', $assessment->subject_id)
                                     ->where('academic_summative_score_template_grades.grade_id', $assessment->grade_id)
                                     ->get();
?>

<div class="row mb-3">
<label for="exampleInputuname_3" class="col-sm-4 control-label">{{ __('app/academy/assessment.date') }}</label>
    <div class="col-8">
        <input type="date" class="form-control" value="{{$assessment->date}}" id="editFormativeAssessmentDate" />
    </div>
</div>

<div class="row mb-3">
<label for="exampleInputuname_3" class="col-sm-4 control-label">{{ __('app/academy/assessment.learning_area') }}</label>
    <div class="col-8">
        <select class="form-control" data-control="select2" required data-dropdown-parent="#modalNewAssessment" name="editFormativeAssessmentName" id="editFormativeAssessmentName">
            @foreach ($learningAreas as $learningArea)
                <option value="{{$learningArea->id}}" @if($assessment->uid == $learningArea->uid) selected @endif>
                        {{$learningArea->name}}
                </option>  
            @endforeach
        </select>
    </div>
</div>

<div class="row mb-3">
    <label for="exampleInputuname_3" class="col-sm-4 control-label">{{ __('app/academy/assessment.template') }}</label>
    <div class="col-8">
        <select class="form-control" data-control="select2" required name="option" id="editFormativeAssessmentOption" data-dropdown-parent="#modalNewAssessment" data-placeholder="-">
            <option value="0">None</option>
            @foreach ($templateInfo as $key => $info )
                <option @if($info->data_id == $assessment->formative_type_id) selected @endif value="{{$info->data_id}}" data-percentage="{{$info->percentage}}">{{$info->title}} ({{$info->percentage}}%)</option>
            @endforeach
        </select>
    </div>
</div>

<input type="hidden" id="editFormativeAssessmentId" value="{{$id}}" />
<input type="hidden" id="editFormativeGradeId" value="{{$assessment->grade_id}}" />