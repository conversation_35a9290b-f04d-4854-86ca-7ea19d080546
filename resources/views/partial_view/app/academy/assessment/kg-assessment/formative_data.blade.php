@php
    use App\Models\FormativeAssessment;
    use App\Models\FormativeAssessmentData;
    use App\Models\ElectiveGradeStudent;
    use App\Library\Helper\AcademicHelper;
    use App\Models\AcademicTerm;

    $ah = new AcademicHelper();
    $lockedTerms = AcademicTerm::where('academic_year_id', $ah->academicYear())
        ->where('branch_id', $ah->currentBranch())
        ->where('is_locked', 1)
        ->get();

    $assessment = FormativeAssessment::with('data')->find($id);

    $isLocked = false;
    $statusText = '';
    $assessmentDate = new Datetime($assessment->date);
    foreach ($lockedTerms as $lt) {
        $startDate = new DateTime($lt->start_date);
        $endDate = new DateTime($lt->end_date);
        if ($assessmentDate >= $startDate && $assessmentDate <= $endDate) {
            $statusText = '<span class="badge badge-danger badge-lg"><i class="fas fa-lock"></i> Assessment is locked!</span>';
            $isLocked = true;
            break;
        }
    }

    $students = ElectiveGradeStudent::where('grade_id', $assessment->grade_id)
        ->whereHas('student')
        ->with('student')
        ->get();
    $options = ['Strong', 'Satisfactory', 'Needs Improvement', 'Not Taught'];

@endphp

<div class="text-end">@if($isLocked) {!! $statusText !!} @endif</div>

<div class="row">
    <div class="alert alert-primary text-center fs-4" role="alert">
        {{ $assessment->assessment_name }}
    </div>
    <div class="col-12">
        <table class="table table-rounded table-striped border" id="formativeKGDataTable">
            <thead>
                <tr class="fw-semibold fs-6 text-gray-800 border-bottom border-gray-200">
                    <th style="padding-left: 10px;">#</th>
                    <th>{{ __('app/academy/assessment.student') }}</th>
                    <th>{{ __('app/academy/assessment.photo') }}</th>
                    <th class="text-center">{{ __('app/academy/assessment.mark') }}</th>
                    <th class="text-center">{{ __('app/academy/assessment.comments') }}</th>
                </tr>
            </thead>
            <tbody>
                @foreach ($students as $student)
                                @php
                                    $data = $assessment->data->firstWhere('student_id', $student->student_id)
                                        ?? FormativeAssessmentData::create([
                                            'formative_assessment_id' => $id,
                                            'student_id' => $student->student_id,
                                            'type_id' => $assessment->data->first()->type->data_id ?? null,
                                            'type_percentage' => $assessment->data->first()->type->percentage ?? null
                                        ]);
                                        $score = match (true) {
                                            ($data->t1 === '✔') => 'Strong',
                                            ($data->t2 === '✔') => 'Satisfactory',
                                            ($data->t3 === '✔') => 'Needs Improvement',
                                            ($data->t4 === '✔') => 'Not Taught',
                                            default => null, // Or a default value if you don't want null

                                        };

                                @endphp

                                <tr class="scoreRowFormative" data-student="{{$student->student_id}}">
                                    <td style="padding-left: 10px;">{{$student->student_id}}</td>
                                    <td>{{ $student->student->name ?? '' }}</td>
                                    <td><img src="{{ $student->student->photo ?? '' }}" class="user-photo user-photo-zoom" /></td>

                                    <td>
                                        <select class="form-control mark-select" data-student-id="{{$student->student_id}}"
                                            data-assessment-id="{{$data->assessment_data_id}}" @if($isLocked) disabled @endif>
                                            <option value="">Select Mark</option>
                                            @foreach ($options as $option)
                                                <option value="{{$option}}" @if($score == $option) selected @endif>{{$option}}</option>
                                            @endforeach
                                        </select>
                                    </td>
                                    <td>
                                        <input type="text" class="form-control comment-input" data-comment-id="{{$student->student_id}}"
                                            value="{{ $data->comment ?? ''}}" @if($isLocked) disabled @endif>
                                    </td>
                                </tr>
                @endforeach
            </tbody>
        </table>
    </div>

</div>

<script>
    $(document).ready(function () {
        $('#btnSubmitKgFormativeData').toggle(!{{ json_encode($isLocked) }});
    });
</script>