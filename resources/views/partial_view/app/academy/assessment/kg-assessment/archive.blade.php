@php
 
use App\Models\TeacherClassLesson;
use App\Models\Subject;
use App\library\Helper\AcademicHelper;

$ah = new AcademicHelper;

$subjects = TeacherClassLesson::select('subject_id')
    ->where('teacher_id', \Auth::user()->id)
    ->where('academic_year_id', $ah->academicYear())
    ->where('branch_id', $ah->currentBranch())
    ->groupBy('subject_id')
    ->get();
$subjectsList = Subject::whereIn('subject_id', $subjects)->orderBy('subject_name')->get();
@endphp

<div class="row">
    <div class="col-2">
        <div class="row mb-3">
            <label for="inputEmail3"
                class="col-form-label">{{ __('app/administration/assessment_administration.type') }}</label>
            <div class="col">
                <select class="form-control" data-control="select2" data-placeholder="Assessment Type"
                    id="assessmentType">
                    <!-- <option value="summative">
                        {{ __('app/administration/assessment_administration.summative') }}
                    </option> -->
                    <option value="formative">
                        {{ __('app/administration/assessment_administration.formative') }}
                    </option>
                </select>
            </div>
        </div>

        <div class="row mb-3">
            <label for="inputEmail3"
                class="col-form-label">{{ __('app/administration/assessment_administration.subject') }}</label>
            <div class="col">
                <select class="form-control" data-control="select2" data-placeholder="Select subject" id="subjectList">
                    @foreach ($subjectsList as $subject)
                        <option value="{{ $subject->subject_id }}">
                            {{$subject->subject_name}}
                        </option>
                    @endforeach
                </select>
            </div>
        </div>

        <div class="row mb-3">
            <div class="col">
                <a class="btn btn-dark w-100" id="searchAssessment"><i class="fas fa-search"></i>
                    {{ __('general.search') }}</a>
            </div>
        </div>
    </div>

    <div class="col-10">
        <table class="table table-striped table-hover border" id="assessmentKgTable">
            <thead>
                <th style="padding-left: 10px;">
                    {{ __('app/administration/assessment_administration.assessment') }}
                </th>
                <th> {{ __('app/administration/assessment_administration.type') }}</th>
                <th> {{ __('app/administration/assessment_administration.date') }}</th>
                <th> {{ __('app/administration/assessment_administration.subject') }}</th>
                <th> {{ __('app/administration/assessment_administration.teacher') }}</th>
                <th> {{ __('app/administration/assessment_administration.grade') }}</th>
                <th></th>
            </thead>
            <tbody id="assessmentListArea">

            </tbody>
        </table>
    </div>
</div>
<!--end:::Main-->

