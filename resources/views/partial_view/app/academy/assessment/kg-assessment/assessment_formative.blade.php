@php 
    use App\Models\FormativeAssessmentData;
@endphp
<div class="table-responsive">
    <table class="table table-rounded table-striped border" id="formativeTable">
        <thead>
            @if (!$assessments->isEmpty())
                    <tr class="fw-semibold fs-6 text-gray-800 border-bottom border-gray-200">
                        <th class="text-center table_header border-end border-right-2 bg-body" rowspan="2"
                            style="padding: 10px; width:20px;">No</th>
                        <th class="text-center table_header border-end border-right-2 bg-body" rowspan="2"
                            style="padding: 10px; min-width:50px;">Student ID</th>
                        <th class="text-center table_header border-end border-right-2 bg-body" rowspan="2"
                            style="padding: 10px; min-width:50px;">Student Name</th>

                        @foreach ($assessments as $assessment)
                                    @php
                                        $statusText = '<i class="fas fa-lock-open"></i> ' . __('Unlocked');
                                        $isLocked = false;
                                        foreach ($lockedTerms as $key => $lt) {
                                            $startDate = new Datetime($lt->start_date);
                                            $endDate = new Datetime($lt->end_date);
                                            $aDate = new Datetime($assessment->date);

                                            if ($aDate >= $startDate && $aDate <= $endDate) {
                                                $statusText = '<span class="badge badge-danger badge-lg"><i class="fas fa-lock"></i>&nbsp;&nbsp;' . __('Locked') . '</span>';
                                                $isLocked = true;
                                                break;
                                            }
                                        }
                                    @endphp
                                    <th class="border-start border-end text-center"
                                        style="width: 30px; word-wrap: break-word; overflow-wrap: break-word; white-space: normal;"
                                        rowspan="1">
                                        {{$assessment->assessment_name}}
                                        <br>
                                        <a href="#" data-id="{{$assessment->formative_assessment_id}}" data-bs-toggle="modal"
                                            data-bs-target="#modalFormativeData" class="btnEditKGFormative"><i class="fas fa-eye"></i></a>
                                        @if(!$isLocked)
                                            <a href="#" data-id="{{$assessment->formative_assessment_id}}"
                                                data-title="{{$assessment->assessment_name}}" class="btnEditFormative">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                        @endif
                                        @if(!$isLocked)
                                            <a href="#" data-id="{{$assessment->formative_assessment_id}}"
                                                data-grade="{{$assessment->grade_id}}" class="btnDeleteKgFormative">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        @endif
                                    </th>
                        @endforeach

                    </tr>
                    <tr class="fw-semibold fs-6 text-gray-800 border-bottom border-gray-200">
                        @foreach ($assessments as $assessment)

                            <th class="border-start border-end text-center" style="white-space: nowrap;">
                                <span class="text-danger">({{$assessment->date}})</span><br>
                                <span class="text-info">{{optional($assessment->type)->title ?? 'N/A'}}
                                    [{{optional($assessment->type)->percentage ?? '-'}}%]</span><br>
                            </th>
                        @endforeach
                    </tr>
            @else
                <tr>
                    <td colspan="4" style="text-align:center; color:red; font-weight:bold">No Assessments Yet</td>
                </tr>
                <tr class="fw-semibold fs-6 text-gray-800 border-bottom border-gray-200">
                    <th class="text-center table_header border-end border-right-2 bg-body" rowspan="2"
                        style="padding-left: 10px; width:20px;">No</th>
                    <th class="text-center table_header border-end border-right-2 bg-body" rowspan="2"
                        style="padding-left: 10px; min-width:50px;">Student ID</th>
                    <th class="text-center table_header border-end border-right-2 bg-body" rowspan="2"
                        style="padding-left: 10px; min-width:50px;">Student Name</th>
                </tr>
            @endif

        </thead>

        <tbody>
            @foreach ($students as $index => $student)
                    <tr class="border-bottom">
                        <!-- Row Number -->
                        <td style="text-align:center; width:10px" class="text-center border-end border-right-2 bg-body">
                            {{ $index + 1 }}
                        </td>

                        <!-- Student ID -->
                        <td style="text-align:center; width:50px" class="border-end border-right-2 bg-body">
                            {{$student->student->id}}
                        </td>
                        <!-- Student Name -->
                        <td style="padding-left: 10px; width:50px; position: sticky;" class="border-end border-right-2 bg-body">
                            {{$student->student->name}}
                        </td>

                        <!-- Student Scores for Each Assessment -->
                        @foreach ($assessments as $assessment)
                            @php
                                $data = FormativeAssessmentData::where('formative_assessment_id', $assessment->formative_assessment_id)
                                    ->where('student_id', $student->student_id)
                                    ->first();

                                $score = null;

                                if ($data) {
                                    if ($data->t1) {
                                        $score = 'Strong';
                                    } else if ($data->t2) {
                                        $score = 'Satisfactory';
                                    } else if ($data->t3) {
                                        $score = 'Needs Improvement';
                                    } else if ($data->t4) {
                                        $score = 'Not Taught';
                                    } 
                                }

                                $percentage = $data->type_percentage ?? 0;
                            @endphp

                            <td class="text-center border-start border-end">
                                {{ $score !== null ? $score : '-' }}
                            </td>
                        @endforeach


                    </tr>
            @endforeach

        </tbody>


    </table>
</div>

<div class="modal fade" tabindex="-1" id="modalFormativeEdit">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ __('app/academy/assessment.assessment_name') }}</h5>
                <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal"
                    aria-label="Close">
                    <span class="svg-icon svg-icon-2x"></span>
                </div>
            </div>

            <div class="modal-body" id="formativeEditArea">

            </div>

            <div class="modal-footer">
                <button type="button" class="btn btn-light" data-bs-dismiss="modal">{{ __('general.close') }}</button>
                <button type="button" class="btn btn-primary"
                    id="btnSaveKgFormativeName">{{ __('general.save') }}</button>
            </div>
        </div>
    </div>
</div>