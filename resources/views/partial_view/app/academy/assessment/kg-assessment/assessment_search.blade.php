<?php 
    use App\Models\FormativeAssessment;
use App\Models\Timetable;
use App\Library\Helper\AcademicHelper;


$academicHelper = new AcademicHelper();
$branchId = $academicHelper->currentBranch();
$academicYearId = $academicHelper->academicYear();

$list = null;

$gradesList = Timetable::select('grade_id')
    ->where('academic_year_id', $academicYearId)
    ->where('branch_id', $branchId)
    ->where('subject_id', $subject)
    ->where('user_id', \Auth::user()->id)
    ->groupBy('grade_id')
    ->get()
    ->pluck('grade_id')
    ->toArray();

if ($type == 'formative') {
    $list = FormativeAssessment::where('academic_year_id', $academicYearId)
        ->where('branch_id', $branchId)
        ->where('subject_id', $subject)
        ->whereIn('grade_id', $gradesList)
        ->where('teacher_id', '=', \Auth::user()->id)
        ->get();
}
 
?>

@foreach ($list as $item)
    <tr>
        <td style="padding-left: 10px;">{{$item->assessment_name}}</td>
        <td>{{ trans('app/administration/assessment_administration.' . $type) }}</td>
        <td>{{$item->date}}</td>
        <td>{{ trans($item->subject->subject_name) }}</td>
        <td>{{$item->teacher->name ?? $item->teacher_id}}</td>
        <td>{{$item->grade?->grade_name ?? '[DELETED]'}}</td>
        <td style="padding-right: 10px; text-align: right;">    
            <a href="#" data-id="{{ $item->formative_assessment_id }}" data-bs-toggle="modal" 
                data-bs-target="#modalViewFormativeData" class="btnViewKGFormative"><i class="fas fa-eye"></i></a>
        </td>
    </tr>
@endforeach