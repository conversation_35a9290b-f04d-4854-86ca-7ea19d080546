<?php
use App\Models\TimeTable;
use App\Library\Helper\AcademicHelper;
use Carbon\Carbon;

$ah = new AcademicHelper();
$timetable = $ah->userTimetable(\Auth::user()->id);
$days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
?>

<style>
    @keyframes animateBackground {
        from {
            background-color: #ffffff;
        }

        42% {
            background-color: #f1f1f1;
        }

        to {
            border-bottom-color: #ffffff;
        }
    }

    .currentDay {
        animation: animateBackground 1s infinite;
    }
</style>
<div class="row">
    <div class="col-lg-6 col-sm-4 col-xs-12">
        <table class="table table-rounded border table-striped gs-7" id="aListTable">
            <thead>
                <th>Grade</th>
                <th>Subject</th>
            </thead>
            <tbody>
                @foreach ($timetable as $session)
                    <tr style="cursor: pointer;" data-grade="{{ $session->grade_id }}"
                        data-subject="{{ $session->subject_id }}" class="sessionRow">
                        <td>{{ $session->elective_grade->grade_name ?? '[Deleted]' }}</td>
                        <td>{{ $session->subject->subject_name }}</td>
                    </tr>
                @endforeach
            </tbody>
        </table>
    </div>

    <div class="col-lg-6 col-sm-8 col-xs-12" id="newSummativeDetails">
        <form id="formNewAssessment2" method="post">

            <table class="table table-rounded border" id="summativeOptTable"
                style="opacity: 0.3; pointer-events: none;">
                <tr>
                    <td style="padding-left: 10px;">Type</td>
                    <td>
                        <select class="form-control" data-control="select2" required name="type" id="type"
                            data-dropdown-parent="#modalNewAssessment" data-placeholder="Assessment type">
                            <option></option>
                            <option value="summative">Assessment</option>
                            <option value="formative">Life Skill</option>
                        </select>
                    </td>
                </tr>

                <tr>
                    <td style="padding-left: 10px;">Title</td>
                    <td><input type="text" class="form-control" required name="title" id="title" /></td>
                </tr>

                <tr>
                    <td style="padding-left: 10px;">Date</td>
                    <td><input type="date" class="form-control" required name="date" /></td>
                </tr>
                <tr id="finalExamTr">
                    <td style="padding-left: 10px;">Final & Midterm ?</td>
                    <td>
                        <select class="form-control" data-control="select2" name="is_final_exam" id="isFinalExam"
                            data-dropdown-parent="#modalNewAssessment" data-placeholder="Type Option">
                            <option></option>
                            <option value="0">No</option>
                            <option value="1">Yes</option>
                        </select>
                    </td>
                </tr>
                <tr id="impactTr">
                    <td style="padding-left: 10px;">Impact</td>
                    <td>
                        <select class="form-control" data-control="select2" name="option" id="assessmentOption"
                            data-placeholder="Assessment option">

                        </select>
                    </td>
                </tr>
                <tr id="commentTr">
                    <td style="padding-left: 10px;">Comment</td>
                    <td>
                        <input type="text" class="form-control" name="comment"
                            data-dropdown-parent="#modalNewAssessment" placeholder="Enter Comment for Assessment" />
                    </td>
                </tr>

                <tr id="maxScoreTr">
                    <td style="padding-left: 10px;">Max Score</td>
                    <td>
                        <input type="text" class="form-control" name="max-score"
                            data-dropdown-parent="#modalNewAssessment" placeholder="Max Score 10 to 100" />
                    </td>
                </tr>

                <tr id="lifeSkillSelector" style="">
                    <td style="padding-left: 10px;">Life Skill</td>
                    <td>
                        <select class="form-control" data-control="select2" required name="lifeSkill" id="lifeSkill"
                            data-dropdown-parent="#modalNewAssessment" data-placeholder="Life Skill">

                        </select>
                    </td>
                </tr>

                <tr id="StrandTr">
                    <td style="padding-left: 10px;">Strand</td>
                    <td>
                        <select class="form-control" data-control="select2" required name="strand" id="strand"
                            data-dropdown-parent="#modalNewAssessment" data-placeholder="Strand">

                        </select>
                    </td>
                </tr>
                <tr>
                    <td style="padding-left: 10px;">Copy to Other Grades</td>
                    <td>
                        <select class="form-control" multiple data-control="select2" name="same_subject_grades[]"
                            id="same_subject_grades" data-dropdown-parent="#modalNewAssessment"
                            data-placeholder="Grades (Optional)">

                        </select>
                    </td>
                </tr>
            </table>
            {{-- <input type="hidden" name="timetable_id" id="assessment_timetable_id" required /> --}}
            <input type="hidden" name="na_grade_id" id="na_grade_id" required />
            <input type="hidden" name="na_subject_id" id="na_subject_id" required />
        </form>
    </div>
</div>
