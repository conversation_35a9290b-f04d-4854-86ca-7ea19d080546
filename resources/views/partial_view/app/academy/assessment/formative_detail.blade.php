<?php
use App\Models\FormativeAssessment;
use App\Models\FormativeAssessmentData;
use App\Models\ScoreTemplate;
use App\Library\Helper\AcademicHelper;
use App\Models\SettingsAssessment;
$ah = new AcademicHelper();
$term = $ah->currentSemester();

$assessment = FormativeAssessment::find($id);

// $ar = new AssessmentRepository(['subjectId' => $assessment->subject_id, 'gradeId'   => $assessment->grade_id ]);

$lifeSkillInfo = SettingsAssessment::leftJoin('skills_strands', 'skills_strands.skill_strand_id', 'settings_assessment.skill_strand_id')
    ->where('grade_id', $assessment->grade_id)
    // ->where('term', $term)
    ->where('skills_strands.type', 'skill')
    ->get();

$strandInfo = SettingsAssessment::leftJoin('skills_strands', 'skills_strands.skill_strand_id', 'settings_assessment.skill_strand_id')
    ->where('grade_id', $assessment->grade_id)
    // ->where('term', $term)
    ->where('skills_strands.type', 'strand')
    ->get();
?>
<div class="row mb-3">
    <label for="exampleInputuname_3" class="col-sm-3 control-label">Assessment Type</label>
    <div class="col-9">
        <p class="form-control">Life Skill</p>
    </div>
</div>
<div class="row mb-3">
    <label for="exampleInputuname_3" class="col-sm-3 control-label">Assessment name</label>
    <div class="col-9">
        <p class="form-control">{{ $assessment->assessment_name }}</p>
    </div>
</div>
<div class="row mb-3">
    <label for="exampleInputuname_3" class="col-sm-3 control-label">Comment</label>
    <div class="col-9">
        <p class="form-control">{{ $assessment->comment ?? '-' }}</p>
    </div>
</div>
<div class="row mb-3">
    <label for="exampleInputuname_3" class="col-sm-3 control-label">Date</label>
    <div class="col-9">
        <p class="form-control">{{ $assessment->date ?? '-' }}</p>
    </div>
</div>

<div class="row mb-3">
    <label for="exampleInputuname_3" class="col-sm-3 control-label">Strand </label>
    <div class="col-9">
        <select disabled class="form-control" data-control="select2" required name="formativeStrand"
            id="editFormativeAssessmentStrand" data-dropdown-parent="#modalNewAssessment" data-placeholder="Strand">
            <option value="0">None</option>
            @foreach ($strandInfo as $key => $info)
                <option @if ($info->skill_strand_id == $assessment->strand) selected @endif value="{{ $info->skill_strand_id }}">
                    {{ $info->value }} </option>
            @endforeach
        </select>
    </div>
</div>

<div class="row mb-3">
    <label for="exampleInputuname_3" class="col-sm-3 control-label">Life Skill </label>
    <div class="col-9">
        <select disabled class="form-control" data-control="select2" required name="formativeSkill"
            id="editFormativeAssessmentSkill" data-dropdown-parent="#modalNewAssessment" data-placeholder="Life Skill">
            <option value="0">None</option>
            @foreach ($lifeSkillInfo as $key => $info)
                <option @if ($info->skill_strand_id == $assessment->skill) selected @endif value="{{ $info->skill_strand_id }}">
                    {{ $info->value }} </option>
            @endforeach
        </select>
    </div>
</div>
