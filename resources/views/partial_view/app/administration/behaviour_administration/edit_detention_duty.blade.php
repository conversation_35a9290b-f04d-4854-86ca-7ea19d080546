<?php
    use App\Models\DetentionDutyList;
    $detention_duty = DetentionDutyList::find($dutyId);
?>
<input type="hidden" name="item_id" id="dutyId" value="{{ $dutyId }}">
<div class="row mb-3">
    <label for="inputEmail3"
        class="col-3 col-form-label">{{ __('app/administration/behaviour_administration.teacher_name') }}</label>
    <div class="col-9">
        <select class="form-control required " data-control="select2" data-dropdown-parent="#modalDetentionDutyEdit"
            data-placeholder="{{ __('app/administration/behaviour_administration.teacher_name') }}" id="editDutyUserId">
            <option value=""></option>
            @foreach ($branch_users as $branch_user)
                <option value="{{ $branch_user->id }}"
                  @if ($detention_duty->duty_teacher_id == $branch_user->id)
                    selected
                  @endif
                    >{{ $branch_user->name }}
                </option>
            @endforeach
        </select>
    </div>
</div>

<div class="row mb-3">
    <label for="inputEmail3"
        class="col-3 col-form-label">{{ __('app/administration/behaviour_administration.detention_type') }}</label>
    <div class="col-9">
        <select class="form-control required " data-control="select2" data-dropdown-parent="#modalDetentionDutyEdit"
            data-placeholder="{{ __('app/administration/behaviour_administration.detention_type') }}"
            id="editDetentionTypeId">
            <option value=""></option>
            @foreach ($dentention_types as $dentention_type)
                <option value="{{ $dentention_type->detention_type_setting_id }}"
                  @if ($detention_duty->detention_type_id == $dentention_type->detention_type_setting_id)
                    selected
                  @endif
                  >
                    {{ $dentention_type->detention_type_setting_label }}
                </option>
            @endforeach
        </select>
    </div>
</div>
</div>