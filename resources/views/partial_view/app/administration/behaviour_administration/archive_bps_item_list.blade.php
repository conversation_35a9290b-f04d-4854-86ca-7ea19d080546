@php
use \App\Models\DisciplineItem;
use App\Library\Helper\AcademicHelper;


 $academicHelper = new AcademicHelper();
 $academicYearId = $academicHelper->academicYear();
 $archive_bps_items = DisciplineItem::withCount(['records' => function ($query) use($academicYearId) {
                                        $query->where('academic_year_id', $academicYearId);
                                        }])->where('item_status',0)->orderBy('item_title')->get();
@endphp

<table class="table" id="tableArchiveItemList">
    <thead>
        <th style="padding-left: 10px;">{{__('app/administration/behaviour_administration.title')}}</th>
        <th>{{__('app/administration/behaviour_administration.type')}}</th>
        <th>{{__('app/administration/behaviour_administration.point')}}</th>
        <th>{{__('app/administration/behaviour_administration.record_count')}}</th>
        <th></th>
    </thead>
    <tbody>
        @foreach ($archive_bps_items as $archive_bps_item)
            <tr>
                <td style="padding-left: 10px;">{{$archive_bps_item->item_title}}</td>
                <td>{{strtoupper($archive_bps_item->item_type)}}</td>
                <td>{{$archive_bps_item->item_point}}</td>
                <td>{{$archive_bps_item->records_count}}</td>
                <td class="text-end">
                  <a href="#" class="btnRestoreBpsItem" data-id="{{$archive_bps_item->discipline_item_id}}"><i class="fas fa-rotate-right"></i></a>
                </td>
            </tr>
        @endforeach
    </tbody>
</table>