<table class="table" id="tableBpsItemList">
    <thead>
        <th style="padding-left: 10px;">{{__('app/administration/behaviour_administration.title')}}</th>
        <th>{{__('app/administration/behaviour_administration.type')}}</th>
        <th>{{__('app/administration/behaviour_administration.point')}}</th>
        <th>{{__('app/administration/behaviour_administration.record_count')}}</th>
        <th></th>
    </thead>
    <tbody>
        @foreach ($bps_items as $bps_item)
            <tr>
                <td style="padding-left: 10px;">{{$bps_item->item_title}}</td>
                <td>{{strtoupper($bps_item->item_type)}}</td>
                <td>{{$bps_item->item_point}}</td>
                <td>{{$bps_item->records_count}}</td>
                <td class="text-end">
                  <a href="#" class="btnEditBpsItem" data-id="{{$bps_item->discipline_item_id}}"><i class="fas fa-edit"></i></a>
                  <a href="#" class="btnArchiveBpsItem" data-id="{{$bps_item->discipline_item_id}}"><i class="fas fa-archive"></i></a>
                </td>
            </tr>
        @endforeach
    </tbody>
</table>