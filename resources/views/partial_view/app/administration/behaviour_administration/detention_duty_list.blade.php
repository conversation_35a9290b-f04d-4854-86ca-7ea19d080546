<div class="fw-bold fs-5 text-center my-2">
    Detention Duty list
</div>
<table class="table" id="tableDetentionDutyList">
    <thead>
        <th style="padding-left: 10px;">{{__('app/administration/behaviour_administration.assigned_teacher_id')}}</th>
        <th>{{__('app/administration/behaviour_administration.assigned_teacher_name')}}</th>
        <th>{{__('app/administration/behaviour_administration.assigned_user')}}</th>
        <th>{{__('app/administration/behaviour_administration.assigned_detention_type')}}</th>
        <th>{{__('app/administration/behaviour_administration.remain_detention_count')}}</th>
        <th></th>
    </thead>
    <tbody>
        @foreach ($duty_teacher_lists as $duty_teacher_list)
            <tr>
                <td style="padding-left: 10px;">{{$duty_teacher_list->duty_teacher_id}}</td>
                <td>{{$duty_teacher_list->duty_teacher->name}}</td>
                <td>{{$duty_teacher_list->assigned_user->name}}</td>
                <td>{{$duty_teacher_list->detention_type->detention_type_setting_label}}</td>
                <td>{{$duty_teacher_list->detention_records_count}}</td>
                <td class="text-end">
                  <a href="#" class="btnEditDutyTeacher" title="Edit Duty" data-id="{{$duty_teacher_list->detention_duty_list_id}}"><i class="fas fa-edit"></i></a>
                  <a href="#" class="btnArchiveDutyTeacher" title="Delete Duty" data-id="{{$duty_teacher_list->detention_duty_list_id}}"><i class="fas fa-archive"></i></a>
                </td>
            </tr>
        @endforeach
    </tbody>
</table>