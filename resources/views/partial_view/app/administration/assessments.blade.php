<?php
use App\Library\Helper\AcademicHelper;
use App\Models\FormativeAssessment;
use App\Models\SummativeAssessment;

$ah = new AcademicHelper();
$current_year = $ah->academicYear();
$current_branch = $ah->currentBranch();
$is_locked = 1;
$summativeAssessment = SummativeAssessment::where('academic_year_id', $current_year)->where('branch_id', $current_branch)->orderBy('date', 'desc')->first();
$formativeAssessment = FormativeAssessment::where('academic_year_id', $current_year)->where('branch_id', $current_branch)->orderBy('date', 'desc')->first();
$final_mid_term_setting = DB::table('settings')->where('setting_type', 'final_mid_term')->where('setting_value_4', $current_branch)->first();
if (isset($summativeAssessment)) {
    if ($summativeAssessment->is_locked == 1) {
        $is_locked = 0;
    }
}
if (isset($formativeAssessment)) {
    if ($formativeAssessment->is_locked == 1) {
        $is_locked = 0;
    }
}

?>

<div id="kt_app_content" class="app-content flex-column-fluid">
    <div id="kt_app_content_container" class="app-container container-fluid">
        <div class="card card-flush h-md-100" style="border-top-right-radius: 0px; border-bottom-right-radius: 0px;">
            <div class="card-body" style="padding: 15px;">
                {{-- --}}
                {{-- <div class="row"> --}}
                @if (!$final_mid_term_setting)
                    <div class="text-danger text-center">If You Don't see the Final And Midterm Input Box , Please Report
                        Informtions to IT Department !</div>
                @endif
                <form id="finalAndMidCreateForm" method="post">
                    <div class="text-center text-secondary">
                        Enter Final & Mid Term %
                    </div>
                    @if ($final_mid_term_setting)
                        <table class="table" id="finalAndMidCreateTable">
                            <tr>
                                <td style="padding-left: 10px;">Final/Mid Term</td>
                                <td><input type="number" class="form-control" required name="mid" id="midInput"
                                        data-branch-id="{{ $final_mid_term_setting->setting_value_4 }}"
                                        placeholder="Enter Mid Term % "
                                        value={{ $final_mid_term_setting->setting_value_2 }} />
                                </td>
                            </tr>
                            {{-- <tr>
                                <td style="padding-left: 10px;">Final Term </td>
                                <td><input type="number" class="form-control" required name="final" id="finalInput"
                                        data-branch-id="{{ $final_mid_term_setting->setting_value_4 }}"
                                        placeholder="Enter Final Term % "
                                        value="{{ $final_mid_term_setting->setting_value_3 }}" />
                                </td>
                            </tr> --}}
                        </table>
                    @endif
                </form>
                <hr class="my-10 text-secondary">
                {{-- </div> --}}
                <div class="row align-items-center">
                    {{-- @foreach ($classes as $classroom) --}}
                    <div class="col-lg-4 col-xl-4 col-sm-4 col-xs-12 mb-4">
                        <h2 class="accordion-header mb-4">
                            <button class="accordion-button fs-5 fw-thin text-secondary" type="button">
                                Lock all Assessments For This Academic Year
                            </button>
                        </h2>
                        <a class="nav-link w-100 btn btn-flex btn-secondary" href="#" id="btnLockAllAssessments">
                            @if ($is_locked == 1)
                                <span class="svg-icon svg-icon-2hx">
                                    <i class="fa-solid fa-lock"></i>
                                </span>
                                <span class="d-flex flex-column align-items-start">
                                    <span class="fs-4 fw-bold">Lock All Assessment </span>
                                </span>
                            @else
                                <span class="svg-icon svg-icon-2hx">
                                    <i class="fa-solid fa-lock-open"></i>
                                </span>
                                <span class="d-flex flex-column align-items-start">
                                    <span class="fs-4 fw-bold">UnLock All Assessment </span>
                                </span>
                            @endif

                        </a>
                    </div>
                    <div class="col-lg-4 col-xl-4 col-sm-4 col-xs-12 mb-4">
                        <h2 class="accordion-header mb-4">
                            <button class="accordion-button fs-5 fw-thin text-secondary" type="button">
                                Lock All Data earlier than the Chosen Date
                            </button>
                        </h2>
                        <a class="nav-link w-100 btn btn-flex btn-secondary" href="#" id="btnlockByDate"
                            data-bs-toggle="modal" data-bs-target="#modalPopUpLockByDate">
                            <span class="svg-icon svg-icon-2hx">
                                <i class="fa-solid fa-lock"></i>
                            </span>
                            <span class="d-flex flex-column align-items-start">
                                <span class="fs-4 fw-bold">Lock By Date </span>
                            </span>

                        </a>
                    </div>
                    <div class="col-lg-4 col-xl-4 col-sm-4 col-xs-12 mb-4">
                        <h2 class="accordion-header mb-4">
                            <button class="accordion-button fs-5 fw-thin text-secondary" type="button">
                                Unlock All Data earlier than the Chosen Date
                            </button>
                        </h2>
                        <a class="nav-link w-100 btn btn-flex btn-secondary" href="#" id="btnlockByDate"
                            data-bs-toggle="modal" data-bs-target="#modalPopUpUnLockByDate">
                            <span class="svg-icon svg-icon-2hx">
                                <i class="fa-solid fa-lock-open"></i>
                            </span>
                            <span class="d-flex flex-column align-items-start">
                                <span class="fs-4 fw-bold">UnLock By Date </span>
                            </span>

                        </a>
                    </div>
                    {{-- <div class="col-lg-2 col-xl-2 col-sm-2 col-xs-12 mb-4"></div>
                    <div class="col-lg-6 col-xl-6 col-sm-6 col-xs-12 mb-4">
                        <h2 class="accordion-header mb-4">
                            <button class="accordion-button fs-5 fw-thin text-secondary" type="button">
                                Lock all Assessments with Dates earlier than the Chosen Date
                            </button>
                        </h2>
                        <div class="row">
                            <div class="col-9 col-md-9 col-lg-9 col-xl-9 mt-2" id="divDate">
                                <input type="date"
                                    placeholder="{{ __('app/administration/academic_settings.pick_date') }}"
                                    class="form-control " id="inputDate" />
                            </div>
                            <div class="col  mt-2">
                                <button type="button" class="btn btn-primary ml-10" id="btnLockAssessmentByDate">
                                    <i class="fa-solid fa-lock"></i>
                                    {{ __('app/administration/academic_settings.lock') }}
                                </button>
                            </div>
                        </div>
                    </div> --}}
                </div>
            </div>
        </div>
    </div>
</div>

{{-- Lock By Date Model --}}
<div class="modal fade" tabindex="-1" id="modalPopUpLockByDate">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ __('app/administration/academic_settings.lock_by_date') }}</h5>
                <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal"
                    aria-label="Close">
                    <i class="fa fa-times"></i>
                </div>
            </div>

            <div class="modal-body">
                <div class="row mb-3">
                    <label for="inputEmail3"
                        class="col-3 col-form-label">{{ __('app/administration/academic_settings.date') }}</label>
                    <div class="col-9">
                        <input type="date" placeholder="{{ __('app/administration/academic_settings.pick_date') }}"
                            class="form-control " id="inputDate" />
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-light" data-bs-dismiss="modal"> {{ __('general.close') }}
                </button>
                <button type="button" class="btn btn-primary" id="btnLockAssessmentByDate">
                    {{ __('general.save_changes') }} </button>
            </div>
        </div>
    </div>
</div>

{{-- Lock By Date Model --}}
<div class="modal fade" tabindex="-1" id="modalPopUpUnLockByDate">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ __('app/administration/academic_settings.unlock_by_date') }}</h5>
                <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal"
                    aria-label="Close">
                    <i class="fa fa-times"></i>
                </div>
            </div>

            <div class="modal-body">
                <div class="row mb-3">
                    <label for="inputEmail3"
                        class="col-3 col-form-label">{{ __('app/administration/academic_settings.date') }}</label>
                    <div class="col-9">
                        <input type="date"
                            placeholder="{{ __('app/administration/academic_settings.pick_date') }}"
                            class="form-control " id="inputUnLockDate" />
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-light" data-bs-dismiss="modal"> {{ __('general.close') }}
                </button>
                <button type="button" class="btn btn-primary" id="btnUnLockAssessmentByDate">
                    {{ __('general.save_changes') }} </button>
            </div>
        </div>
    </div>
</div>
