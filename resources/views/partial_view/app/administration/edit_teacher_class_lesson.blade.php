<?php
    use App\Models\TeacherClassLesson;
    $tcl = TeacherClassLesson::find($tclId);
?>
<input type="hidden" name="item_id" id="tclId" value="{{$tclId}}">
<div class="row mb-3">
    <label for="inputEmail3" class="col-3 col-form-label">Weekly Count</label>
    <div class="col-9">
        <input type="number" class="form-control" name="weekly_count" id="weeklyCount" placeholder="Enter Weekly Count" required
            value="{{$tcl->weekly_count}}">
    </div>
</div>
<div class="row mb-3">
    <label for="inputEmail3" class="col-3 col-form-label">Start Date</label>
    <div class="col-9">
        <input type="date" class="form-control" name="start_date" id="startDate" placeholder="Choose Start Date" required
            value="{{$tcl->start_date}}">
    </div>
</div>
<div class="row mb-3">
    <label for="inputEmail3" class="col-3 col-form-label">End Date</label>
    <div class="col-9">
        <input type="date" class="form-control" name="end_date" id="endDate" placeholder="Choose End Date" required
            value="{{$tcl->end_date}}">
    </div>
</div>