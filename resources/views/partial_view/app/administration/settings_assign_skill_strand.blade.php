<?php
    use App\Models\SettingsAssessment;
    use App\Models\ElectiveGrade;
    use App\Library\Helper\AcademicHelper;

    $academicHelper = new AcademicHelper();
    $academicYearId = $academicHelper->academicYear();
    $academicYearId = $academicHelper->academicYear();
    $branchId = $academicHelper->currentBranch();

    $assignedSkillStrands = SettingsAssessment::leftJoin('skills_strands', 'skills_strands.skill_strand_id', 'settings_assessment.skill_strand_id')
        ->leftJoin('academic_elective_grade','academic_elective_grade.grade_id','settings_assessment.grade_id')
        ->where('academic_elective_grade.branch_id', $branchId)
        ->where('academic_elective_grade.academic_year_id', $academicYearId)
        ->orderBy('term','DESC')
        ->orderBy('grade_name', 'ASC')
        ->get();
?>

<h5 class="">Assigned Strands & Skills</h5>

<table class="table table table-striped border" id="assignedStrandSkillTable">
    <thead>
        <th style="padding-left: 10px;">Term</th>
        <th>Type</th>
        <th>Grade</th>
        <th>Value</th>
        <th></th>
    </thead>
    <tbody>
        @foreach ($assignedSkillStrands as $skill_Stand)
            <tr>
                <td style="padding-left: 10px;">Term {{$skill_Stand->term}}</td>
                <td>
                    @if($skill_Stand->is_final_mid_exam)
                    setting
                    @else
                    {{$skill_Stand->type}}
                    @endif
                </td>
                <td>{{$skill_Stand->grade_name}}</td>
                <td>{{$skill_Stand->value}}</td>
                <td style="padding-right: 10px; text-align: right;">
                    <a href="#" class="btnDeleteAssignedSS" data-id="{{$skill_Stand->setting_id}}"><i class="fas fa-trash"></i></a>
                </td>
            </tr>
        @endforeach
    </tbody>
</table>



