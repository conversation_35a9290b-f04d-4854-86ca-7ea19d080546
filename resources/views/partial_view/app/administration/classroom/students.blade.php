<?php
use App\Models\Classroom;
use App\Models\StudentClassroom;

// $classroom = Classroom::find($classroomId);
// $students = $classroom->students;
$todayDate = date('Y-m-d');
$students = StudentClassroom::leftJoin('users', 'users.id', 'students_classroom.student_id')
    ->leftJoin('student_attendance', function ($join) use ($todayDate) {
        $join->on('student_attendance.student_id', 'students_classroom.student_id')->where('student_attendance.date', $todayDate);
    })
    ->where('students_classroom.classroom_id', $classroomId)
    ->where('users.user_status', 1)
    ->get();
?>
<style>
    .dropdown-content {
        display: none;
    }
</style>
<div class="text-end">
    <a href="#" class="btn btn-sm fw-bold btn-secondary" id="btnResetPasswordClass"
        data-class-id="{{ $classroomId }}"><i class="fa fa-rotate-right"></i>
        Generate
        Passwords
    </a>
</div>
<table id="classroomDetailTable" class="table table-striped border">
    <thead>
        <th>
            <input type="checkbox" id="selectAllRow" class=" ms-4" />
        </th>
        <th style="padding-left: 10px;">ID</th>
        <th>Student</th>
        <th>Photo</th>
        {{-- <th>Attendance</th> --}}
        {{-- <th>Note</th> --}}
        <th style="text-align:right; padding-right: 10px;">
            <a href="#" style="display: none;" id="addClassroomStudent" data-bs-toggle="modal"
                data-bs-target="#modalAddStudents"><i class="fas fa-plus"></i></a>
        </th>
    </thead>
    <tbody id="classroomDetailArea">
        @foreach ($students as $key => $student)
            <?php
            //   $attendanceStatus = $student->attendance_status;
            
            //   $presentClass   = null;
            //   $lateClass      = null;
            //   $absentClass    = null;
            //   $dropdownDisplay    = 'none';
            
            //   if ($attendanceStatus == '') {
            //       $presentClass   = 'btn-primary';
            //       $lateClass      = 'btn-secondary';
            //       $absentClass    = 'btn-secondary';
            //       $lateMinutes =null;
            //   } else {
            //       if ($attendanceStatus == "present") {
            //           $presentClass   = 'btn-primary';
            //           $lateClass      = 'btn-secondary';
            //           $absentClass    = 'btn-secondary';
            //       }
            //       if ($attendanceStatus == "late") {
            //           $presentClass   = 'btn-secondary';
            //           $lateClass      = 'btn-primary';
            //           $absentClass    = 'btn-secondary';
            //           $dropdownDisplay ='block';
            //       }
            //       if ($attendanceStatus == "absent") {
            //           $presentClass   = 'btn-secondary';
            //           $lateClass      = 'btn-secondary';
            //           $absentClass    = 'btn-primary';
            //       }
            //       $entranceTime = $student->swipe_time ?? null;
            //         if ($entranceTime != '') {
            //         $to_time = strtotime($entranceTime);
            //         $from_time = strtotime('08:00:00');
            //         $lateMinutes = round(abs($from_time - $to_time) / 60);
            //         }
            //   }
            ?>
            <tr data-id="{{ $student->id }}">
                <td> <input type="checkbox" class="rowCheckbox ms-4 " id="rowCheckbox{{ $key }}" /></td>
                <td style="padding-left: 10px;">{{ $student->id }}</td>
                <td>{{ $student->name ?? 'N/A' }}</td>
                <td><img src="{{ $student->photo ?? '' }}" class="user-photo user-photo-zoom" /></td>
                {{-- <td>
                <div class="btn-group attendanceRow" data-id="{{$student->id}}" id="buttonGroup_{{$student->id}}"
                    role="group">
                    <button type="button" data-student-id="{{$student->id}}" data-type="present"
                        class="attendanceBtn attendanceBtn_{{$student->id}} btn {{$presentClass}} ">Present</button>
                    <button type="button" data-student-id="{{$student->id}}" data-type="late"
                        class="attendanceBtn attendanceBtn_{{$student->id}} btn {{$lateClass}} lateBtn">Late</button>
                    <button type="button" data-student-id="{{$student->id}}" data-type="absent"
                        class="attendanceBtn attendanceBtn_{{$student->id}} btn {{$absentClass}} ">Absent</button>
                </div>
                <div id="myDropdown" class="dropdown-content text-center mt-2 bg-secondary rounded "
                    style="width:250px;display:{{$dropdownDisplay}}"> --}}
                {{-- <div>
                        <a href="#" class="text-white lateMinus">1 - 5 mins</a>
                    </div>
                    <div>
                        <a href="#" class="text-white lateMinus">6 - 15 mins</a>
                    </div>
                    <div>
                        <a href="#" class="text-white lateMinus">16 - 30 mins</a>
                    </div>
                    <div>
                        <a href="#" class="text-white lateMinus">Over 30 mins</a>
                    </div> --}}

                {{-- <div>
                        <input class='form-control lateMinus' type="number" name="lateMinus"
                            id='lateMinus_{{$student->id}}' value='{{$lateMinutes}}' placeholder="Enter Late Minus ">
                    </div>
                </div>
            </td> --}}
                {{-- <td><input type='text' class='form-control' id='attendanceNote_{{$student->id}}'
                    value='{{$student->notes}}' /></td> --}}
                <td style="text-align: right; padding-right: 10px; width:100px;">
                    <a href='#' title='View' class='btnViewStudentDetail' data-id="{{ $student->id }}"><i
                            class='fas fa-eye'></i></a>&nbsp;
                    <a href="#" id="addBpsforSingleStudent" data-id="{{ $student->sc_id }}"><i
                            class="fas fa-smile"></i></a>&nbsp;
                    <a href="#" class="btnDeleteStudent" data-id="{{ $student->sc_id }}"><i
                            class="fas fa-trash"></i></a>
                </td>
            </tr>
        @endforeach
    </tbody>
</table>
