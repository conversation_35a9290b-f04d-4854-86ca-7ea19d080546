<?php
    use App\Models\SkillsStrands;
    use App\Library\Helper\AcademicHelper;

    $academicHelper = new AcademicHelper();
    $branchId = $academicHelper->currentBranch();

    $strandAndSkills = SkillsStrands::where('branch_id', $branchId)
                                       ->get();
?>
<h5 class="">Strands & Skills List</h5>
<table class="table table-striped border" id="strandSkillsTable">
    <thead>
        <th style="padding-left: 10px;">Type</th>
        <th>Value</th>
        <th style="text-align:right; padding-right: 10px;">
            <a href="#" id="addStrndSkl" data-bs-toggle="modal" data-bs-target="#modalAddSS"><i class="fas fa-plus"></i></a>
        </th>
    </thead>
    <tbody>
        @foreach ($strandAndSkills as $element)
            <tr>
                <td style="padding-left: 10px;">
                    @if ($element->is_final_mid_exam == 1)
                        setting
                        @else
                        {{$element->type}}
                    @endif
                </td>
                <td>{{$element->value}}</td>
                <td style="padding-right: 10px; text-align: right;">
                    <a href="#" class="btnDeleteSS" data-id="{{$element->skill_strand_id}}"><i class="fas fa-trash"></i></a>
                </td>
            </tr>
        @endforeach
    </tbody>
</table>



