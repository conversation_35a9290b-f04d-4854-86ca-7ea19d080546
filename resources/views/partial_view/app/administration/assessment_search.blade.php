<?php
    use App\Models\SummativeAssessment;
    use App\Models\FormativeAssessment;
    use App\Library\Helper\AcademicHelper;


    $academicHelper = new AcademicHelper();
    $branchId = $academicHelper->currentBranch();
    $academicYearId = $academicHelper->academicYear();

    $list = null;
    $type = null;

    // if($type == 'summative') {
        $summativeList  = SummativeAssessment::where('academic_year_id', $academicYearId)
                                   ->where('branch_id', $branchId)
                                   ->where('teacher_id', $teacher_id)
                                   ->get();
    // } else if($type == 'formative') {
        $formativeList  = FormativeAssessment::where('academic_year_id', $academicYearId)
                                   ->where('branch_id', $branchId)
                                   ->where('teacher_id', $teacher_id)
                                   ->get();
    // }
    $list = $summativeList->merge($formativeList);
    $grades = $academicHelper->userTimetable($teacher_id);
?>
<div id="assessmentGradeArea" class=" row my-4">
    @foreach ($grades as $session)

    <div class="col-lg-3 col-xl-3 col-sm-6 col-xs-12 mb-4">
        <a class="nav-link w-100 btn btn-flex btn-secondary  subjectRow" data-id="{{$session->grade_id}}"
            href="#tab-classroom-{{$session->grade_id}}">
            <span class="svg-icon svg-icon-2hx">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                        d="M16.0173 9H15.3945C14.2833 9 13.263 9.61425 12.7431 10.5963L12.154 11.7091C12.0645 11.8781 12.1072 12.0868 12.2559 12.2071L12.6402 12.5183C13.2631 13.0225 13.7556 13.6691 14.0764 14.4035L14.2321 14.7601C14.2957 14.9058 14.4396 15 14.5987 15H18.6747C19.7297 15 20.4057 13.8774 19.912 12.945L18.6686 10.5963C18.1487 9.61425 17.1285 9 16.0173 9Z"
                        fill="currentColor" />
                    <rect opacity="0.3" x="14" y="4" width="4" height="4" rx="2" fill="currentColor" />
                    <path
                        d="M4.65486 14.8559C5.40389 13.1224 7.11161 12 9 12C10.8884 12 12.5961 13.1224 13.3451 14.8559L14.793 18.2067C15.3636 19.5271 14.3955 21 12.9571 21H5.04292C3.60453 21 2.63644 19.5271 3.20698 18.2067L4.65486 14.8559Z"
                        fill="currentColor" />
                    <rect opacity="0.3" x="6" y="5" width="6" height="6" rx="3" fill="currentColor" />
                </svg>
            </span>
            <span class="d-flex flex-column align-items-start">
                <span class="fs-4 fw-bold">{{$session->elective_grade->grade_name ??
                    '[Deleted]'}}</span>
                <span class="fs-7">{{$session->subject->subject_name}}</span>
            </span>
        </a>
    </div>

    @endforeach
</div>
<table class="table table-striped table-hover border" id="assessmentTable">
    <thead>
        <th style="padding-left: 10px;">{{
            __('app/administration/assessment_administration.assessment') }}
        </th>
        <th> {{ __('app/administration/assessment_administration.type') }}</th>
        <th> {{ __('app/administration/assessment_administration.date') }}</th>
        <th> {{ __('app/administration/assessment_administration.subject') }}
        </th>
        <th> {{ __('app/administration/assessment_administration.teacher') }}
        </th>
        <th> {{ __('app/administration/assessment_administration.grade') }}</th>
        <th></th>
    </thead>
    <tbody>
        @foreach ($list as $item)
        <tr>
            <td style="padding-left: 10px;">{{$item->assessment_name}}</td>
            <td>
                @if (isset($item->formative_assessment_id))
                Life Skill
                <?php $type ='formative'; ?>
                @else
                <?php $type ='summative'; ?>
                Assessment
                @endif
            </td>
            <td>{{$item->date}}</td>
            <td>{{$item->subject->subject_name}}</td>
            <td>{{$item->teacher->name ?? $item->teacher_id}}</td>
            <td>{{$item->grade->grade_name ?? '[DELETED]'}}</td>
            <td style="padding-right: 10px; text-align: right;">
                <a href="#" class="btnViewAssessment" data-type="{{$type}}" data-id="<?php
        if($type == 'summative'){
            echo $item->assessment_id;
        }else{
            echo $item->formative_assessment_id;
        }
            ?>
            "><i class="fas fa-eye"></i></a>
            </td>
        </tr>
        @endforeach
    </tbody>
</table>