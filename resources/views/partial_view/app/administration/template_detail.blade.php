<?php
use App\Models\ScoreTemplate;

$template = ScoreTemplate::find($templateId);
?>

<div class="card ">
    <div class="card-header card-header-stretch">
        <h3 class="card-title">{{ $template->title }}</h3>
        <div class="card-toolbar">
            <ul class="nav nav-tabs nav-line-tabs nav-stretch fs-6 border-0">
                <li class="nav-item">
                    <a class="nav-link active" data-bs-toggle="tab" href="#kt_tab_pane_7">Grades [<small>Total
                            {{ $template->grades->count() }}</small>]</a>
                </li>
                <li class="nav-item" id="tabTemplateOptions">
                    <a class="nav-link" data-bs-toggle="tab" href="#kt_tab_pane_8">Options [<small>Total
                            {{ $template->template_data->count() }}</small>]</a>
                </li>
            </ul>
        </div>
    </div>
    <div class="card-body">
        <div class="tab-content" id="myTabContent">
            <div class="tab-pane fade show active" id="kt_tab_pane_7" role="tabpanel">
                <table class="table table-hover" id="templateSubjectTable">
                    <thead>
                        <th>Subject</th>
                        <th style="padding-right: 10px; text-align: right;"><a id="btnAddTempplateGrade"
                                style="color: blue;" data-subject="{{ $template->subject_id }}"><i style="color: blue;"
                                    class="fas fa-plus"></i> Grade</a></th>
                    </thead>
                    <tbody>
                        @foreach ($template->grades as $key => $grade)
                            <tr>
                                <td style="padding-left: 10px;">{{ $grade->grade->grade_name }}</td>
                                <td style="padding-right: 10px; text-align: right;">
                                    <a href="#" class="btnDeleteTemplateGrade"
                                        data-id="{{ $grade->template_grade_id }}"><i class="fas fa-trash"></i></a>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            <div class="tab-pane fade" id="kt_tab_pane_8" role="tabpanel">
                <table class="table table-hover" id="templateOptionsTable">
                    <thead>
                        <th>Title</th>
                        <th>Percentage</th>
                        <th>Term-End Percentage ? </th>
                        {{-- <th style="padding-right: 10px; text-align: right;"><a id="btnAddTempplateOption"
                                style="color: blue;"><i style="color: blue;" class="fas fa-plus"></i> Option</a></th> --}}
                    </thead>
                    <tbody>
                        @foreach ($template->template_data as $key => $data)
                            <tr class="optionRowEdit" data-id="{{ $data->data_id }}" data-title="{{ $data->title }}"
                                data-percent="{{ $data->percentage }}">
                                <td style="padding-left: 10px;">{{ $data->title }}</td>
                                <td style="padding-left: 10px;">{{ $data->percentage }}</td>
                                <td style="padding-left: 10px;">
                                    @if ($data->is_final_exam_percentage == 1)
                                        <i class="fa-solid fa-check text-success"></i>
                                    @else
                                        <i class="fa-solid fa-xmark text-danger"></i>
                                    @endif
                                </td>
                                {{-- <td style="padding-right: 10px; text-align: right;">
                                    <a href="#" class="btnDeleteTemplateOption" data-id="{{ $data->data_id }}"><i
                                            class="fas fa-trash"></i></a>
                                </td> --}}
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

        </div>
    </div>
</div>


<script>
    $(document).ready(function() {
        makeDataTable('templateSubjectTable');
        makeDataTable('templateOptionsTable');
    });
</script>
