<form id="formEditLearningArea">
    <input type="hidden" name="id" value="{{ $learningArea->id }}" />
    <!-- Grades Dropdown -->
    <div class="row mb-3">
        <label for="grades" class="col-3 col-form-label">{{ __('Grade') }}</label>
        <div class="col-9">
            <select class="form-control chosen-select" name="grade[]" multiple>
                @php
                    $selectedGrades = json_decode($learningArea->grades, true) ?? []; // Decode grades array with fallback
                @endphp
                @foreach($gradesList as $data)
                    <option value="{{ $data->grade_id }}" @if(in_array($data->grade_id, (array) $selectedGrades)) selected
                    @endif>
                        {{ $data->grade_name }}
                    </option>
                @endforeach
            </select>
        </div>
    </div>
    <!-- Learning Area Input -->
    <div class="row mb-3">
        <label for="learning_area" class="col-3 col-form-label">{{ __('Learning Area') }}</label>
        <div class="col-9">
            <input type="text" name="learning_area" required class="form-control" value="{{ $learningArea->name }}" />
        </div>
    </div>
</form>