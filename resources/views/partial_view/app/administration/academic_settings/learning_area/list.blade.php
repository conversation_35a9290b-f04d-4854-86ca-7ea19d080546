<div class="table-responsive">

    <table class="table table-striped" id="learningAreaTable">
        <thead>
            <tr>
                <th>#</th>
                <th>{{ __('app/administration/academic_settings.learning_area') }}</th>
                <th>{{ __('app/administration/academic_settings.elective_grade') }}</th>
                <th>{{ __('app/administration/academic_settings.subject') }}</th>
                <th></th>
            </tr>
        </thead>
        <tbody>
            @foreach ($learning_areas as $index => $learning_area)
                        <tr>
                            <td>{{ $index + 1 }}</td>
                            <td>{{ $learning_area->name }}</td>
                            <td>
                                @php
                                    $grades = json_decode($learning_area->grades, true); // Decode grades array
                                @endphp
                                @if (empty($grades))
                                    {{ __('app/administration/academic_settings.no_grades') }}
                                @else
                                    @foreach ($learning_area->formatted_grades as $grade)
                                        @php
                                            $subject = $grade->subject?->subject_name;
                                        @endphp

                                        <span class="badge badge-primary" id="{{ $grade->grade_id }}">{{ $grade->grade_name }}</span>
                                    @endforeach
                                @endif
                            </td>
                            <td>{{ $subject ?? '-'}}</td>
                            <td>
                                <!-- Add action buttons for editing/deleting -->
                                <a class="btnEditLA" href="#" data-id="{{$learning_area->id}}"><i class="fas fa-edit"></i></a>&nbsp;
                                <a class="btnDeleteLA" href="#" data-id="{{$learning_area->id}}"><i class="fas fa-trash"></i></a>

                            </td>
                        </tr>
            @endforeach
        </tbody>
    </table>
</div>