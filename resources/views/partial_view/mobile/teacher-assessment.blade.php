<?php
    use App\Models\DisciplineRecord;
    use App\Library\Helper\AcademicHelper;
    use App\Models\MobileDevice;
    use App\Models\UserBranch;
    use App\Models\DisciplineItem;
    use App\Models\Classroom;
    use App\Models\ElectiveGrade;
        use App\Models\FormativeAssessment;
        use App\Models\SummativeAssessment;

    $ah = new AcademicHelper();
$academicYearId = $ah->academicYear();
    $device = MobileDevice::where('auth_code', $authCode)->first();

    if($device){
        $userId = $device->student_id;
        $branches = UserBranch::leftJoin('branches', 'branches.branch_id', 'users_branches.branch_id')->where('user_id', $userId)->get();
        $branchId = $ah->mobileCurrentBranch($userId);
        $classes = $ah->mobileBranchClassrooms($userId);
        $students = $ah->branchStudents($branchId);

        $teacherClassrooms = $ah->teacherTCLClassrooms($userId);
        $classes = Classroom::whereIn('classroom_id',$teacherClassrooms->pluck('class_id'))->get();
        $timetable = $ah->userTimetable($userId);

            $array = $ah->userTimetable($userId);



                $list = null;
    $type = null;


        $summativeList  = SummativeAssessment::where('academic_year_id', $academicYearId)
                                   ->where('branch_id', $branchId)
                                   ->where('teacher_id', $userId)
                                   ->get();

        $formativeList  = FormativeAssessment::where('academic_year_id', $academicYearId)
                                   ->where('branch_id', $branchId)
                                   ->where('teacher_id', $userId)
                                   ->get();

    $list = $summativeList->merge($formativeList);
    $grades = $ah->userTimetable($userId);

?>

        <!DOCTYPE html>
        <html lang="en">

        <head>
            <meta charset="utf-8">
            <meta http-equiv="refresh" content="7200">
            <meta http-equiv="X-UA-Compatible" content="IE=edge">
            <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, shrink-to-fit=no">
            <meta name="_token" content="{!! csrf_token() !!}"/>
            <link rel="icon" type="image/x-icon" href="/theme_v2/assets/img/favicon.ico" />
            <link href="https://fonts.googleapis.com/css?family=Nunito:400,600,700" rel="stylesheet">
            <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.15.4/css/all.css" integrity="sha384-DyZ88mC6Up2uqS4h/KRgHuoeGwBcD4Ng9SiP4dIRy0EXTlnuz47vAwmeGwVChigm" crossorigin="anonymous">
            <link href="/theme/dist/assets/plugins/global/plugins.bundle.css" rel="stylesheet" type="text/css" />
            <link href="/theme/dist/assets/css/style.bundle.css" rel="stylesheet" type="text/css" />
            <link href="/theme/dist/assets/plugins/global/plugins.bundle.css" rel="stylesheet" type="text/css"/>
            <link href="/theme/dist/assets/plugins/custom/datatables/datatables.bundle.css" rel="stylesheet" type="text/css"/>
            <style>
                .form-label {
                    display: block;
                    margin-left: 15px;
                    margin-top: 10px;
                }
                .center-button {
                    display: flex;
                    justify-content: center;
                    margin-top: 20px;
                }
            </style>
        </head>

        <body>

        <div class="p-2">
        <table class="table table-striped table-hover border" id="assessmentTable">
            <thead>
                <th style="padding-left: 10px;">{{
                    __('app/administration/assessment_administration.assessment') }}
                </th>
                <th> {{ __('app/administration/assessment_administration.type') }}</th>
                <th> {{ __('app/administration/assessment_administration.date') }}</th>
                <th> {{ __('app/administration/assessment_administration.subject') }}
                </th>
                <th> {{ __('app/administration/assessment_administration.grade') }}</th>
                <th></th>
            </thead>
            <tbody>
                @foreach ($list as $item)
                <tr>
                    <td style="padding-left: 10px;">{{$item->assessment_name}}</td>
                    <td>
                        @if (isset($item->formative_assessment_id))
                        Life Skill
                        <?php $type ='formative'; ?>
                        @else
                        <?php $type ='summative'; ?>
                        Assessment
                        @endif
                    </td>
                    <td>{{$item->date}}</td>
                    <td>{{$item->subject->subject_name}}</td>
                    <td>{{$item->grade->grade_name ?? '[DELETED]'}}</td>
                    <td style="padding-right: 10px; text-align: right;">
                        <a href="#" class="btnViewAssessment" data-type="{{$type}}" data-id="<?php
                if($type == 'summative'){
                    echo $item->assessment_id;
                }else{
                    echo $item->formative_assessment_id;
                }
                    ?>
                    "><i class="fas fa-eye"></i></a>
                    </td>
                </tr>
                @endforeach
            </tbody>
        </table>
        </div>

        <div class="modal fade" tabindex="-1" id="modalViewAssessment">
            <div class="modal-dialog modal-xl modal-dialog-centered modal-dialog-scrollable">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">{{ __('app/administration/assessment_administration.assessment_details') }}</h5>
                        <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal"
                            aria-label="Close">
                            <i class="fa fa-times"></i>
                        </div>
                    </div>

                    <div class="modal-body" id="assessmentDetailArea">

                    </div>
                    <div class="modal-footer flex-column flex-md-row justify-content-between">

                        <div class="d-flex flex-column flex-md-row w-100 w-md-auto">
                            <button type="button" class="btn btn-light mb-2 mb-md-0 w-100 w-md-auto" data-bs-dismiss="modal">
                                {{ __('general.close') }}
                            </button>
                            <button type="button" class="btn btn-primary w-100 w-md-auto" id="btnSaveAssessmentDetail">
                                {{ __('general.save_changes') }}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
          <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.1/jquery.min.js"
              integrity="sha512-aVKKRRi/Q/YV+4mjoKBsE4x3H+BkegoM/em46NNlCqNTmUYADjBbeNefNxYV7giUp0VxICtqdrbqU7iVaeZNXA=="
              crossorigin="anonymous" referrerpolicy="no-referrer"></script>
          <script src="/library/js/app/academy/assessment.js?t={{time()}}"></script>



        <script src="/theme/dist/assets/plugins/global/plugins.bundle.js"></script>
        <script src="/theme/dist/assets/js/scripts.bundle.js"></script>
        <script src="/theme/dist/assets/plugins/global/plugins.bundle.js"></script>
        <script src="/library/service/helper.js"></script>
        <script src="/library/js/app/general.js"></script>
        <script src="/library/js/api_bps.js?v=3"></script>
        <script src="/theme/dist/assets/plugins/custom/datatables/datatables.bundle.js"></script>
        <script src="https://ajax.aspnetcdn.com/ajax/jquery.validate/1.10.0/jquery.validate.min.js"></script>
<script src="/library/js/app/administration/assessment_administration.js"></script>
    </body>

    </html>
    <?php
    } else {
        echo "test-".$authCode."-Invalid Authentication code!";

    }
?>
