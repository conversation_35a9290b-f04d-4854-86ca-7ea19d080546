<?php 

use App\Models\AcademicSemester;
use App\Models\AcademicTerm;
use App\Library\Helper\AcademicHelper;
use App\Models\ReportCardData;

Route::group([ 'middleware' => ['auth', 'is_staff']], function(){
   

    Route::get('/app/reports/kg-report', function(){
        $ah = new AcademicHelper();
        $classrooms = $ah->branchClassrooms();
        $requests = ReportCardData::where('academic_year_id', $ah->academicYear())
                                  ->where('created_by', \Auth::user()->id)
                                  ->orderBy('created_at', 'desc')
                                  ->take(10)
                                  ->get();

        $terms = AcademicSemester::where('branch_id', $ah->currentBranch())
                ->where('academic_year_id', $ah->academicYear())                
                ->get();

        return view('app.report.kg_report', [
            'classrooms'    => $classrooms,
            'requests'      => $requests,
            'terms'         => $terms,
        ]);
    })->middleware('checkpermission:120');

    Route::post('/app/reports/kg-report/classroom-students', ['middleware' => 'checkpermissionaction:120-1', 'uses' => '\App\Http\Controllers\KGReportCardController@classroomStudents']);
    Route::post('/app/reports/kg-report/pre-check', ['middleware' => 'checkpermissionaction:120-1', 'uses' => '\App\Http\Controllers\KGReportCardController@reportCardPreCheck']);

    Route::get('/app/reports/kg-report/generate', [App\Http\Controllers\FileGenerateController::class, 'generateKGReportCard']);

});

