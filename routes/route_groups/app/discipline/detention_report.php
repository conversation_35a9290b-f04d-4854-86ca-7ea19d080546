<?php


 Route::group([ 'middleware' => ['auth', 'is_staff']], function(){
     Route::get('/app/discipline/detention-report', function(){
         return view('app.discipline.detention_report');
     })->middleware('checkpermission:106');

    Route::post('/app/discipline/detention-report/delete-record', ['middleware' => 'checkpermissionaction:106-1', 'uses' => '\App\Http\Controllers\BPSController@deleteDetentionRecord']);
    Route::post('/app/discipline/detention-report/detention-record', ['middleware' => 'checkpermissionaction:106-1', 'uses' => '\App\Http\Controllers\BPSController@changeDetentionRecordType']);
    // Route::post('/app/discipline/detention-report/serve-record', ['middleware' => 'checkpermissionaction:106-1', 'uses' => '\App\Http\Controllers\BPSController@serveBps']);
    Route::post('/app/discipline/detention-report/serve-record', [\App\Http\Controllers\BPSController::class,'serveBps']);
    Route::get('/app/discipline/detention-report/add-bps/{id}',  [App\Http\Controllers\BPSController::class, 'addBpsDetail']);
    Route::match(['get', 'post'],'/app/discipline/detention-report/get-detention-records', ['middleware' => 'checkpermissionaction:106-1', 'uses' => '\App\Http\Controllers\BPSController@detentionRecords']);
 });