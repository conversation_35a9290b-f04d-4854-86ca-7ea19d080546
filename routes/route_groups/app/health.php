<?php 

use App\Http\Controllers\HealthController;
use App\Http\Controllers\HealthReportController;

    Route::get('/app/health', [HealthController::class, 'index'])
        ->middleware(['auth', 'is_staff', 'checkpermissionaction:42-1']);

    Route::group(['middleware' => ['auth', 'is_staff']], function () {

    Route::get('/app/health/student/show-detail', [HealthController::class, 'studentDetail']);

    Route::get('/app/health/staff/show-detail', [HealthController::class, 'staffDetail']);

    Route::post('/app/health/student/save-info', [HealthController::class, 'saveStudentHealthInfo']);
    Route::post('/app/health/student/create-record', [HealthController::class, 'createStudentRecord']);
    Route::post('/app/health/student/create-measure-record', [HealthController::class, 'createStudentMeasureRecord']);
    Route::post('/app/health/student/delete-measure-record/{id}', [HealthController::class, 'deleteStudentMeasureRecord']);
    Route::post('/app/health/student/delete-record', [HealthController::class, 'deleteStudentRecord']);

    Route::post('/app/health/staff/create-record', [HealthController::class, 'createStaffRecord']);
    Route::post('/app/health/staff/delete-record', [HealthController::class, 'deleteStaffRecord']);

    Route::post('/app/health/guest/create-record', [HealthController::class, 'createGuestRecord']);
    Route::post('/app/health/guest/delete-record', [HealthController::class, 'deleteGuestRecord']);

    Route::post('/app/health/staff/save-info', [HealthController::class, 'saveStafftHealthInfo']);

    Route::get('/app/reports/health/list-info', [HealthReportController::class, 'generateList']);

});
