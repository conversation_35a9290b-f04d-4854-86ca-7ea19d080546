<?php

use App\Models\User;
use App\Library\Helper\ListHelper;
use App\Library\Helper\AcademicHelper;
use App\Http\Controllers\GlobalController;
use App\Models\DetentionDutyList;
use App\Models\DetentionTypeSetting;
use App\Models\DisciplineItem;
use App\Models\Subject;

Route::group(['middleware' => ['auth', 'is_staff']], function () {

    Route::get('/app/administration/behaviour-administration', function () {
        $academicHelper = new AcademicHelper();
        $branchId = $academicHelper->currentBranch();
        $academicYearId = $academicHelper->academicYear();

        $bps_items = DisciplineItem::withCount(['records' => function ($query) use ($academicYearId) {
            $query->where('academic_year_id', $academicYearId);
        }])->where('item_status', 1)->orderBy('item_title')->get();

        $duty_teacher_lists = DetentionDutyList::withCount(['detention_records' => function ($query) use ($academicYearId, $branchId) {
            $query->where('academic_year_id', $academicYearId)->where('branch_id', $branchId)->where('is_served', 0);
        }])
            ->with('duty_teacher', 'assigned_user', 'detention_type')
            ->where('academic_year_id', $academicYearId)
            ->where('branch_id', $branchId)
            ->where('status', 1)
            ->get();

        $dentention_types = DetentionTypeSetting::all();
        return view('app.administration.behaviour_administration', [
            'bps_items' => $bps_items,
            'duty_teacher_lists' => $duty_teacher_lists,
            'branch_users' => $academicHelper->branchUsers($branchId),
            'dentention_types' => $dentention_types,
        ]);
    })->middleware('checkpermission:121');

    Route::get('/app/administration/behaviour-administration/show-archive-bps-item', function () {
        return view('partial_view.app.administration.behaviour_administration.archive_bps_item_list');
    });

    Route::get('/app/administration/behaviour-administration/show-edit-bps-item', function () {
        return view(
            'partial_view.app.administration.behaviour_administration.edit_bps_item',
            [
                'itemId' => $_GET['itemId'],
            ]
        );
    });

    Route::get('/app/administration/behaviour-administration/show-edit-detention-duty', function () {
        $academicHelper = new AcademicHelper();
        $branchId = $academicHelper->currentBranch();
        $dentention_types = DetentionTypeSetting::all();
        return view(
            'partial_view.app.administration.behaviour_administration.edit_detention_duty',
            [
                'dutyId' => $_GET['dutyId'],
                'branch_users' => $academicHelper->branchUsers($branchId),
                'dentention_types' => $dentention_types,
            ]
        );
    });
    Route::post('/app/administration/behaviour-administration/store-bps-item', ['middleware' => 'checkpermissionaction:121-1', 'uses' => '\App\Http\Controllers\BPSController@storeBpsItem']);
    Route::post('/app/administration/behaviour-administration/update-bps-item', ['middleware' => 'checkpermissionaction:121-1', 'uses' => '\App\Http\Controllers\BPSController@updateBpsItem']);
    Route::post('/app/administration/behaviour-administration/archive-bps-item', ['middleware' => 'checkpermissionaction:121-1', 'uses' => '\App\Http\Controllers\BPSController@archiveBpsItem']);
    Route::post('/app/administration/behaviour-administration/store-detention-duty', ['middleware' => 'checkpermissionaction:121-1', 'uses' => '\App\Http\Controllers\BPSController@storeDetentionDuty']);
    Route::post('/app/administration/behaviour-administration/update-detention-duty', ['middleware' => 'checkpermissionaction:121-1', 'uses' => '\App\Http\Controllers\BPSController@updateDetentionDuty']);
    Route::post('/app/administration/behaviour-administration/archive-detention-duty', ['middleware' => 'checkpermissionaction:121-1', 'uses' => '\App\Http\Controllers\BPSController@archiveDetentionDuty']);
});
