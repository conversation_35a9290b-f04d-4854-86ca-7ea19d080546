<?php 

use App\Models\User;
use App\Library\Helper\ListHelper;
use App\Library\Helper\AcademicHelper;
use App\Http\Controllers\GlobalController;
use App\Models\Subject;
use App\Models\TeacherClassLesson;

Route::group([ 'middleware' => ['auth', 'is_staff']], function(){

    Route::get('/app/administration/teacher-class-lesson', function(){

        $academicHelper = new AcademicHelper();
        $branchId = $academicHelper->currentBranch();
        $academicYearId = $academicHelper->academicYear();

        $data = TeacherClassLesson::where('academic_year_id', $academicYearId)
                                  ->where('branch_id', $branchId)
                                  ->get();

        return view('app.administration.teacher_class_lesson', [
           'data'       => $data,
           'users'      => $academicHelper->tclBranchUsers($branchId),
           'subjects'   => Subject::all(),
           'classrooms' => $academicHelper->branchClassrooms(),
           'subjects'   => Subject::all()
        ]);
    })->middleware('checkpermission:20');

    Route::get('/app/administration/teacher-class-lesson/show-edit-tcl', function () {
        return view(
            'partial_view.app.administration.edit_teacher_class_lesson',
            [
                'tclId' => $_GET['tclId'],
            ]
        );
    });

    Route::post('/app/administration/teacher-class-lesson/create', ['middleware' => 'checkpermissionaction:15-1', 'uses' => '\App\Http\Controllers\TCLController@create']);
    Route::post('/app/administration/teacher-class-lesson/delete', ['middleware' => 'checkpermissionaction:15-1', 'uses' => '\App\Http\Controllers\TCLController@delete']);
    Route::post('/app/administration/teacher-class-lesson/update', ['middleware' => 'checkpermissionaction:15-1', 'uses' => '\App\Http\Controllers\TCLController@update']);


    

});

