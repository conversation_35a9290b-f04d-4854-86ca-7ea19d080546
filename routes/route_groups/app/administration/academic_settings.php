<?php

use App\Http\Controllers\LearningAreaController;
use App\Models\LearningArea;
use App\Models\User;
use App\Library\Helper\ListHelper;
use App\Library\Helper\AcademicHelper;
use App\Models\Subject;
use App\Models\SkillsStrands;
use App\Models\ElectiveGrade;

Route::group(['middleware' => ['auth', 'is_staff']], function () {
    $academicHelper = new AcademicHelper();
    $subjects = Subject::all();

    Route::get('/app/administration/academic-settings', function () {
        $academicHelper = new AcademicHelper();
        $academicYearId = $academicHelper->academicYear();
        $branchId = $academicHelper->currentBranch();
        $electiveGrades = ElectiveGrade::where('academic_year_id', $academicYearId)
            ->where('branch_id', $branchId)
            ->get();
        $strandAndSkills = SkillsStrands::where('branch_id', $branchId)->get();

        return view('app.administration.academic_settings', [
            'subjects' => Subject::all(),
            'classrooms' => $academicHelper->branchClassrooms(),
            'elective_grades' => $electiveGrades,
            'skills_strands' => $strandAndSkills
        ]);
    })->middleware('checkpermission:85');

    Route::get('/app/administration/academic-settings/show-excluded-subject', function () {
        return view('partial_view.app.administration.settings_excluded_subjects');
    });

    Route::get('/app/administration/academic-settings/show-strand-skill', function () {
        return view('partial_view.app.administration.settings_strand_skill_list');
    });
    Route::get('/app/administration/academic-settings/get-academic-semester', function () {
        return view(
            'partial_view.app.administration.academic_settings.academic_semester',
            [
                'academicYearId' => $_GET['academicYearId'],
                'branchId' => $_GET['branchId'],
            ]
        );
    });
    Route::get('/app/administration/academic-settings/academic-semester-edit', function () {
        return view(
            'partial_view.app.administration.academic_settings.academic_semester_edit',
            [
                'semester_id' => $_GET['semesterId']
            ]
        );
    });
    Route::post('/app/administration/academic-settings/add-academic-semester', ['middleware' => 'checkpermissionaction:85-1', 'uses' => '\App\Http\Controllers\AcademicsController@addAcademicSemester']);
    Route::post('/app/administration/academic-settings/add-excluded-subject', ['middleware' => 'checkpermissionaction:85-1', 'uses' => '\App\Http\Controllers\AcademicSettingsController@addExcludedSubject']);
    Route::post('/app/administration/academic-settings/delete-excluded-subject', ['middleware' => 'checkpermissionaction:85-1', 'uses' => '\App\Http\Controllers\AcademicSettingsController@deleteExcludedSubject']);
    Route::post('/app/administration/academic-settings/save-bell-times', ['middleware' => 'checkpermissionaction:85-1', 'uses' => '\App\Http\Controllers\AcademicSettingsController@saveBellTimes']);
    Route::post('/app/administration/academic-settings/lbc-save-attribute', ['middleware' => 'checkpermissionaction:85-1', 'uses' => '\App\Http\Controllers\AcademicSettingsController@saveLbcAtribute']);
    Route::post('/app/administration/academic-settings/lbc-save-point', ['middleware' => 'checkpermissionaction:85-1', 'uses' => '\App\Http\Controllers\AcademicSettingsController@saveLbcPoint']);
    Route::post('/app/administration/academic-settings/lbc-save-type', ['middleware' => 'checkpermissionaction:85-1', 'uses' => '\App\Http\Controllers\AcademicSettingsController@saveLbcType']);
    Route::post('/app/administration/academic-settings/add-strand-skill', ['middleware' => 'checkpermissionaction:85-1', 'uses' => '\App\Http\Controllers\AcademicSettingsController@addStrandSkill']);
    Route::post('/app/administration/academic-settings/delete-strand-skill', ['middleware' => 'checkpermissionaction:85-1', 'uses' => '\App\Http\Controllers\AcademicSettingsController@deleteStrandSkill']);
    Route::post('/app/administration/academic-settings/assign-ss', ['middleware' => 'checkpermissionaction:85-1', 'uses' => '\App\Http\Controllers\AcademicSettingsController@assignStrandSkill']);
    Route::post('/app/administration/academic-settings/delete-assigned-strand-skill', ['middleware' => 'checkpermissionaction:85-1', 'uses' => '\App\Http\Controllers\AcademicSettingsController@deleteAssignedStrandSkill']);
    Route::post('/app/administration/academic-settings/lock-all-assessments', ['middleware' => 'checkpermissionaction:85-1', 'uses' => '\App\Http\Controllers\AcademicSettingsController@lockAllAssessments']);
    Route::post('/app/administration/academic-settings/lock-assessments-by-date', ['middleware' => 'checkpermissionaction:85-1', 'uses' => '\App\Http\Controllers\AcademicSettingsController@lockAssessmentsByDate']);

    Route::post('/app/administration/academic-settings/add-final-mid-exam-percentage', ['middleware' => 'checkpermissionaction:85-1', 'uses' => '\App\Http\Controllers\AcademicSettingsController@addFinalMidExamPercentage']);


    // learning area routes
    Route::get('/learning-areas', [LearningAreaController::class, 'index'])->name('learningAreas.index');

    Route::get('/app/administration/academic-settings/learning-area/show-create-form', function () use ($academicHelper, $subjects) {
        return view('partial_view.app.administration.academic_settings.learning_area.form_create', [
            'subjects' => $subjects,
            'gradesList' => $academicHelper->branchElectiveGrades()
        ]);
    });

    Route::get('/app/administration/academic-settings/learning-area/import-previous-year', function () use ($academicHelper) {
        $learning_areas = LearningArea::where('branch_id', $academicHelper->currentBranch())
            ->orderBy('created_at', 'desc')
            ->get();
        return view('partial_view.app.administration.academic_settings.learning_area.import_learning_area', [
            'yearList' => $academicHelper->academicYearList(),
            'learning_areas' => $learning_areas
        ]);
    });

    Route::get('/app/administration/academic-settings/learning-area/get-by-year', [LearningAreaController::class, 'getByYear']);
    Route::post('/app/administration/academic-settings/learning-area/import', [LearningAreaController::class, 'importFromPreviousYear']);

    Route::post(
        '/app/administration/academic-settings/learning-area/store',
        [LearningAreaController::class, 'store']
    )->middleware('checkpermissionaction:85-1');

    Route::post(
        '/app/administration/academic-settings/learning-area/delete',[LearningAreaController::class, 'destroy']
    )->name('learningAreas.destroy')->middleware(
        'checkpermissionaction:85-1'
    );

    Route::get(
        '/app/administration/academic-settings/learning-area/show-edit-form/{learning_area_id}',[LearningAreaController::class, 'edit']
    )->name('learningAreas.showEditForm')->middleware(
        'checkpermissionaction:85-1'
    );

    Route::post(
        '/app/administration/academic-settings/learning-area/update', [LearningAreaController::class, 'update']
    )->name('learningAreas.update')->middleware(    
        'checkpermissionaction:85-1'
    );

   });
