<?php 

use App\Models\User;
use App\Library\Helper\ListHelper;
use App\Library\Helper\AcademicHelper;
use App\Http\Controllers\GlobalController;
use App\Models\Classroom;
use App\Models\StudentInformation;
use App\Models\Subject;

Route::group([ 'middleware' => ['auth', 'is_staff']], function(){

    Route::get('/app/administration/elective-grades', function(){
        $academicHelper = new AcademicHelper();
        $branchId = $academicHelper->currentBranch();
        $academicYearId = $academicHelper->academicYear();

        $list = StudentInformation::leftJoin('students_classroom', function ($join) use ($academicYearId, $branchId) {
            $join->on('students_classroom.student_id', 'student_information.id');
            $join->where('students_classroom.academic_year_id', $academicYearId);
            $join->where('students_classroom.branch_id', $branchId);
        })
            ->leftJoin('classrooms', 'classrooms.classroom_id', 'students_classroom.classroom_id')
            ->where('student_information.branch_id', $branchId)
            ->where('student_information.student_status', 1)
                ->whereNotNull('students_classroom.student_id') 
            ->orderBy('name')
            ->get();

        return view('app.administration.elective_grade', [
           'users'    => $academicHelper->branchUsers($branchId),
           'grades'   => $academicHelper->branchElectiveGrades(),
        //    'students' => $academicHelper->branchStudents($branchId),
           'students' => $list,
           'subjects' => Subject::all(),
           'classrooms' => $academicHelper->branchClassrooms()
        ]);
    })->middleware('checkpermission:58');
    
    Route::post('/app/administration/elective-grades/get-students', ['middleware' => 'checkpermissionaction:58-1', 'uses' => '\App\Http\Controllers\ElectiveGradeController@getStudents']);
    Route::post('/app/administration/elective-grades/create', ['middleware' => 'checkpermissionaction:58-1', 'uses' => '\App\Http\Controllers\ElectiveGradeController@create']);
    Route::post('/app/administration/elective-grades/get-classroom-students', ['middleware' => 'checkpermissionaction:58-1', 'uses' => '\App\Http\Controllers\ElectiveGradeController@getClassroomStudents']);
    Route::post('/app/administration/elective-grades/delete-student', ['middleware' => 'checkpermissionaction:58-1', 'uses' => '\App\Http\Controllers\ElectiveGradeController@deleteStudent']);
    Route::post('/app/administration/elective-grades/add-student', ['middleware' => 'checkpermissionaction:58-1', 'uses' => '\App\Http\Controllers\ElectiveGradeController@addStudent']);
    Route::post('/app/administration/elective-grades/delete-grade', ['middleware' => 'checkpermissionaction:58-1', 'uses' => '\App\Http\Controllers\ElectiveGradeController@deleteGrade']);
    Route::post('/app/administration/elective-grades/update-grade', ['middleware' => 'checkpermissionaction:58-1', 'uses' => '\App\Http\Controllers\ElectiveGradeController@updateGrade']);

});

