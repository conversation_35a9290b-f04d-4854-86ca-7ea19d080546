<?php

use App\Models\User;
use App\Library\Helper\ListHelper;
use App\Library\Helper\AcademicHelper;
use App\Http\Controllers\GlobalController;
use App\Models\Classroom;
use App\Models\Subject;

Route::group(['middleware' => ['auth', 'is_staff']], function () {

    Route::get('/app/administration/summative-score-template', function () {
        $academicHelper = new AcademicHelper();
        $branchId = $academicHelper->currentBranch();

        return view('app.administration.score_template', [
            'subjects' => Subject::all()
        ]);
    })->middleware('checkpermission:59');

    Route::get('/app/administration/summative-score-template/show-template-detail', function () {
        return view('partial_view.app.administration.template_detail', ['templateId' => $_GET['templateId']]);
    });


    Route::post('/app/administration/summative-score-template/get-subject-grade', ['middleware' => 'checkpermissionaction:59-1', 'uses' => '\App\Http\Controllers\ScoreTemplateController@getSubjectGrades']);
    Route::post('/app/administration/summative-score-template/create', ['middleware' => 'checkpermissionaction:59-1', 'uses' => '\App\Http\Controllers\ScoreTemplateController@create']);
    Route::post('/app/administration/summative-score-template/update', ['middleware' => 'checkpermissionaction:59-1', 'uses' => '\App\Http\Controllers\ScoreTemplateController@update']);
    Route::post('/app/administration/summative-score-template/delete-grade', ['middleware' => 'checkpermissionaction:59-1', 'uses' => '\App\Http\Controllers\ScoreTemplateController@deleteGrade']);
    Route::post('/app/administration/summative-score-template/add-grade', ['middleware' => 'checkpermissionaction:59-1', 'uses' => '\App\Http\Controllers\ScoreTemplateController@addGrade']);
    Route::post('/app/administration/summative-score-template/add-option', ['middleware' => 'checkpermissionaction:59-1', 'uses' => '\App\Http\Controllers\ScoreTemplateController@addOption']);
    Route::post('/app/administration/summative-score-template/delete-option', ['middleware' => 'checkpermissionaction:59-1', 'uses' => '\App\Http\Controllers\ScoreTemplateController@deleteOption']);
    Route::post('/app/administration/summative-score-template/delete-template', ['middleware' => 'checkpermissionaction:59-1', 'uses' => '\App\Http\Controllers\ScoreTemplateController@deleteTemplate']);
    Route::post('/app/administration/summative-score-template/find-template-grade', ['middleware' => 'checkpermissionaction:59-1', 'uses' => '\App\Http\Controllers\ScoreTemplateController@findTemplateGrade']);
    Route::post('/app/administration/summative-score-template/find-final-mid-term-percentage', ['middleware' => 'checkpermissionaction:59-1', 'uses' => '\App\Http\Controllers\ScoreTemplateController@findFinalMidTermPercentage']);
});
