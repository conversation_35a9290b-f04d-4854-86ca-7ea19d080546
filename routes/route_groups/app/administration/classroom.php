<?php

use App\Models\User;
use App\Library\Helper\ListHelper;
use App\Library\Helper\AcademicHelper;
use App\Http\Controllers\GlobalController;
use App\Models\Classroom;

Route::group([ 'middleware' => ['auth', 'is_staff']], function(){

    Route::get('/app/administration/classes', function(){
        $academicHelper = new AcademicHelper();
        $branchId = $academicHelper->currentBranch();

        return view('app.administration.classrooms', [
           'users' => $academicHelper->branchUsers($branchId),
           'classrooms' => $academicHelper->branchClassrooms(),
           'students' => $academicHelper->branchStudents($branchId)
        ]);
    })->middleware('checkpermission:15');

    Route::get('/app/administration/classes/show-create-form', function(){
        return view('partial_view.app.administration.classroom.new');
    });

    Route::get('/app/administration/classes/show-edit-form', function(){
        return view('partial_view.app.administration.classroom.edit', ['classroomId' => $_GET['classroomId']]);
    });

    Route::get('/app/administration/classes/get-students', function(){
        return view('partial_view.app.administration.classroom.students', ['classroomId' => $_GET['classroomId']]);
    });
    Route::get('/app/administration/classes/view-student-form', function(){ 
        return view('partial_view.app.administration.classroom.student_detail', ['studentId' => $_GET['studentId']]);
    });

    // Route::post('/app/administration/classes/get-students', ['middleware' => 'checkpermissionaction:15-1', 'uses' => '\App\Http\Controllers\ClassroomController@getStudents']);
    Route::post('/app/administration/classes/store', ['middleware' => 'checkpermissionaction:15-1', 'uses' => '\App\Http\Controllers\ClassroomController@store']);
    Route::post('/app/administration/classes/delete', ['middleware' => 'checkpermissionaction:15-1', 'uses' => '\App\Http\Controllers\ClassroomController@delete']);
    Route::post('/app/administration/classes/add-student', ['middleware' => 'checkpermissionaction:15-1', 'uses' => '\App\Http\Controllers\ClassroomController@addStudent']);
    Route::post('/app/administration/classes/delete-student', ['middleware' => 'checkpermissionaction:15-1', 'uses' => '\App\Http\Controllers\ClassroomController@deleteStudent']);
    Route::post('/app/administration/classes/get-year-classrooms', ['middleware' => 'checkpermissionaction:15-1', 'uses' => '\App\Http\Controllers\ClassroomController@getYearClassrooms']);
    Route::get('/app/administration/classes/reset-password-classroom', ['middleware' => 'checkpermissionaction:15-1', 'uses' => '\App\Http\Controllers\ClassroomController@resetPasswordClassroom']);





});
