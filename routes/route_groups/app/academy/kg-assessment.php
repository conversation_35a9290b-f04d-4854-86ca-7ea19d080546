<?php
use App\Models\AcademicSemester;
use App\Models\AcademicTerm;
use App\Library\Helper\AcademicHelper;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

Route::group(['middleware' => ['auth', 'is_staff']], function () {
    
    Route::get('/app/academy/kg-assessment', function () {
        $ah = new AcademicHelper();
        $academicHelper = new AcademicHelper();

        $timetable = $academicHelper->userTimetable(\Auth::user()->id);
       
        // Fetch all academic terms for the current academic year and branch
        // $terms = AcademicTerm::where('academic_year_id', $academicHelper->academicYear())
        //     ->where('branch_id', $academicHelper->currentBranch())
        //     ->get();
        $terms = AcademicSemester::where('branch_id',$ah->currentBranch())
                                    ->where('academic_year_id',$ah->academicYear())
                                    ->get();
                                 
        $lockedTerms = $terms->where('is_locked', 1);
        $termLockText = $lockedTerms->implode('term_title', ', ');

        return view('app.academy.kg-assessment', [
            'timetable' => $timetable,
            'terms' => $terms,
            'termLockText' => $termLockText,
            'lockedTerms' => $lockedTerms,
        ]);
    })->middleware('checkpermission:119');

    Route::get('/app/academy/assessment/kg-assessment/show-formative-data', function (Request $request) {
        return view('partial_view.app.academy.assessment.kg-assessment.formative_data', [
            'id' => $request->query('id')
        ]);
    });
    Route::get('/app/academy/assessment/kg-assessment/view-formative-data', function (Request $request) {
        return view('partial_view.app.academy.assessment.kg-assessment.formative_view', [
            'id' => $request->query('id')
        ]);
    });

    Route::get('/app/academy/assessment/kg-assessment/formative/edit', function (Request $request) {
        return view('partial_view.app.academy.assessment.kg-assessment.formative_edit', [
            'id' => $request->query('id')
        ]);
    });

    Route::get('/app/academy/assessment/kg-assessment/show-search-table', function (Request $request) {
        return view('partial_view.app.academy.assessment.kg-assessment.assessment_search', [
            'type' => $request->query('type'),
            'subject' => $request->query('subject')
        ]);
    });

    Route::post('/app/academy/assessment/kg-formative-save', ['middleware' => 'checkpermissionaction:119-1', 'uses' => '\App\Http\Controllers\AssessmentController@saveKgFormative']);
    
    Route::post('/app/academy/assessment/kg-save-formative-data', ['middleware' => 'checkpermissionaction:119-1', 'uses' => '\App\Http\Controllers\AssessmentController@saveFormativeData']);
    Route::post('/app/academy/assessment/kg-update-formative-title', ['middleware' => 'checkpermissionaction:119-1', 'uses' => '\App\Http\Controllers\AssessmentController@updateKGFormativeTitle']);
    Route::post('/app/academy/assessment/kg-get-grade-options', ['middleware' => 'checkpermissionaction:119-1', 'uses' => '\App\Http\Controllers\AssessmentController@getGradeOptionsAndLearningAreas']);
    Route::post('/app/academy/assessment/kg-create', ['middleware' => 'checkpermissionaction:119-1', 'uses' => '\App\Http\Controllers\AssessmentController@createKgAssessment']);
    Route::post('/app/academy/assessment/kg-delete-formative', ['middleware' => 'checkpermissionaction:119-1', 'uses' => '\App\Http\Controllers\AssessmentController@deleteFormativeAssessment']);

    Route::get('/app/academy/assessment/kg-assessment/assessment_formative', ['middleware' => 'checkpermissionaction:119-1', 'uses' => '\App\Http\Controllers\AssessmentController@showKgFormative']);
    // Route::get('app/academy/assessment/kg-assessment/show-formative-assessments', function(){ 
    //     return view('partial_view.app.academy.assessment.kg-assessment.formative');
    // });

    // Route::get('/app/academy/assessment/kg-assessment/show-assessment-add', function(){ 
    //     return view('partial_view.app.academy.assessment.kg-assessment.add');
    // });

});

