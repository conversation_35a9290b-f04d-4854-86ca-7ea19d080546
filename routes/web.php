<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\App;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

include __DIR__ . '/route_groups/attendance.php';
include __DIR__ . '/route_groups/cron_jobs.php';
include __DIR__ . '/route_groups/broadcast.php';
include __DIR__ . '/route_groups/mobile_api.php';

include __DIR__ . '/route_groups/admin/general_routes.php';
include __DIR__ . '/route_groups/admin/staff.php';
include __DIR__ . '/route_groups/admin/student.php';
include __DIR__ . '/route_groups/admin/user_document.php';
include __DIR__ . '/route_groups/admin/settings.php';
include __DIR__ . '/route_groups/admin/academics.php';
include __DIR__ . '/route_groups/admin/module.php';
include __DIR__ . '/route_groups/admin/file_manager.php';
include __DIR__ . '/route_groups/admin/error_logs.php';
// include __DIR__ . '/route_groups/admin/mobile_setting.php';


include __DIR__ . '/route_groups/app/general_routes.php';
include __DIR__ . '/route_groups/app/home.php';
include __DIR__ . '/route_groups/app/administration/teacher_class_lesson.php';
include __DIR__ . '/route_groups/app/administration/classroom.php';
include __DIR__ . '/route_groups/app/administration/elective_grade.php';
include __DIR__ . '/route_groups/app/administration/student.php';
include __DIR__ . '/route_groups/app/administration/timetable.php';
include __DIR__ . '/route_groups/app/administration/score_template.php';
include __DIR__ . '/route_groups/app/administration/academic_settings.php';
include __DIR__ . '/route_groups/app/administration/activity_manager.php';
include __DIR__ . '/route_groups/app/administration/assessment_administration.php';
include __DIR__ . '/route_groups/app/administration/attendance_settings.php';
include __DIR__ . '/route_groups/app/administration/inventory.php';
include __DIR__ . '/route_groups/app/administration/mobile_notification.php';
include __DIR__ . '/route_groups/app/administration/staff.php';
include __DIR__ . '/route_groups/app/administration/student_storage.php';
include __DIR__ . '/route_groups/app/administration/feedback.php';
include __DIR__ . '/route_groups/app/administration/pickup.php';
include __DIR__ . '/route_groups/app/administration/house_system.php';
include __DIR__ . '/route_groups/app/administration/public_holiday.php';
include __DIR__ . '/route_groups/app/administration/behaviour_administration.php';

include __DIR__ . '/route_groups/app/library.php';
include __DIR__ . '/route_groups/app/messages.php';
include __DIR__ . '/route_groups/app/file_manager.php';
include __DIR__ . '/route_groups/app/health.php';
include __DIR__ . '/route_groups/app/visitor_badge.php';

include __DIR__ . '/route_groups/app/academy/assessment.php';
include __DIR__ . '/route_groups/app/academy/kg-assessment.php';
include __DIR__ . '/route_groups/app/academy/homeroom.php';
include __DIR__ . '/route_groups/app/academy/activity.php';
include __DIR__ . '/route_groups/app/academy/lesson_plan.php';
include __DIR__ . '/route_groups/app/academy/comments.php';
include __DIR__ . '/route_groups/app/academy/term_plan.php';
include __DIR__ . '/route_groups/app/academy/progress.php';
include __DIR__ . '/route_groups/app/academy/homework.php';

include __DIR__ . '/route_groups/app/lbc.php';
include __DIR__ . '/route_groups/app/appraisal/professionalism.php';
include __DIR__ . '/route_groups/app/appraisal/student_goals.php';
include __DIR__ . '/route_groups/app/appraisal/teacher_goals.php';
include __DIR__ . '/route_groups/app/discipline/bps.php';
include __DIR__ . '/route_groups/app/discipline/bps_report.php';
include __DIR__ . '/route_groups/app/discipline/detention_report.php';
include __DIR__ . '/route_groups/app/discipline/major.php';
include __DIR__ . '/route_groups/app/discipline/behaviour_analysis.php';
include __DIR__ . '/route_groups/app/discipline/bps_awards.php';

include __DIR__ . '/route_groups/app/report/report_card.php';
include __DIR__ . '/route_groups/app/report/kg_report.php';
include __DIR__ . '/route_groups/app/report/assessment.php';
include __DIR__ . '/route_groups/app/report/student_attendance.php';
include __DIR__ . '/route_groups/app/report/leave_request.php';
include __DIR__ . '/route_groups/app/report/lesson_plan.php';
include __DIR__ . '/route_groups/app/report/term_plan.php';
include __DIR__ . '/route_groups/app/report/professionalism.php';
include __DIR__ . '/route_groups/app/report/staff_attendance.php';
include __DIR__ . '/route_groups/app/report/staff_list.php';
include __DIR__ . '/route_groups/app/report/student_list.php';
include __DIR__ . '/route_groups/app/report/overall_report.php';
include __DIR__ . '/route_groups/app/report/pickup_request.php';
include __DIR__ . '/route_groups/app/report/app_usage.php';

include __DIR__ . '/route_groups/app/head_office/user_document.php';
include __DIR__ . '/route_groups/app/head_office/staff.php';
include __DIR__ . '/route_groups/app/head_office/student.php';
include __DIR__ . '/route_groups/app/head_office/behaviour_analysis.php';
include __DIR__ . '/route_groups/app/head_office/academic_year.php';
include __DIR__ . '/route_groups/app/head_office/mobile_setting.php';
include __DIR__ . '/route_groups/app/attendance/attendance.php';

include __DIR__ . '/route_groups/student/general.php';
include __DIR__ . '/route_groups/student/home.php';
include __DIR__ . '/route_groups/student/message.php';
include __DIR__ . '/route_groups/student/attendance.php';
include __DIR__ . '/route_groups/student/assessment.php';
include __DIR__ . '/route_groups/student/homework.php';
include __DIR__ . '/route_groups/student/files.php';
include __DIR__ . '/route_groups/student/discipline.php';
include __DIR__ . '/route_groups/student/health.php';
include __DIR__ . '/route_groups/student/library.php';


//test update

Route::get('/locale/{locale}', function ($locale) {
    if (!in_array($locale, ['en', 'kh', 'ch', 'az'])) {
        abort(400);
    }

    App::setLocale($locale);
    session()->put('locale', $locale);
    return redirect()->back();
});

Route::get('/', function () {
    return view('auth.login');
});


Route::get('/auth/logout', '\App\Http\Controllers\Auth\LoginController@logout');
Route::post('/auth/login', '\App\Http\Controllers\Auth\LoginController@login');
