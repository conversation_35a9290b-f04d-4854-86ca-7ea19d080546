# Mobile Messaging API Documentation

## Overview

The Mobile Messaging API provides comprehensive messaging functionality for the mobile application, supporting conversations between students, teachers, and staff members with role-based permissions and real-time notifications.

## Authentication

All messaging endpoints require authentication using the `authCode` parameter, which is obtained during the mobile login process.

## Base URL

All endpoints are prefixed with `/mobile-api/messaging/`

## Endpoints

### 1. Get Conversations List

**GET** `/mobile-api/messaging/conversations`

Retrieves all conversations where the authenticated user is a member.

**Parameters:**

- `authCode` (required): Authentication code from mobile login

**Response:**

```json
{
  "success": true,
  "data": {
    "conversations": [
      {
        "conversation_id": 1,
        "conversation_uuid": "uuid-string",
        "topic": "Math Class Discussion",
        "creator": {
          "id": 123,
          "name": "John <PERSON>"
        },
        "members": [
          {
            "id": 123,
            "name": "John <PERSON>",
            "user_type": "staff",
            "photo": "https://sis.bfi.edu.mm/path/to/photo.jpg"
          }
        ],
        "grouped_members": [
          {
            "type": "staff",
            "type_label": "Staff",
            "count": 2,
            "members": [
              {
                "id": 123,
                "name": "<PERSON>",
                "user_type": "staff",
                "photo": "https://sis.bfi.edu.mm/path/to/photo.jpg"
              }
            ]
          },
          {
            "type": "student",
            "type_label": "Student",
            "count": 3,
            "members": [
              {
                "id": 456,
                "name": "Jane Student",
                "user_type": "student",
                "photo": "https://sis.bfi.edu.mm/path/to/photo.jpg"
              }
            ]
          }
        ],
        "last_message": {
          "message_id": 456,
          "content": "Please submit your homework",
          "sender_id": 123,
          "created_at": "2024-01-15 10:30:00",
          "message_type": "text"
        },
        "unread_count": 2,
        "created_at": "2024-01-15 09:00:00",
        "updated_at": "2024-01-15 10:30:00"
      }
    ],
    "total_count": 5
  }
}
```

### 2. Get Conversation Messages

**GET** `/mobile-api/messaging/conversation/messages`

Retrieves messages from a specific conversation with pagination.

**Parameters:**

- `authCode` (required): Authentication code
- `conversation_uuid` (required): UUID of the conversation
- `page` (optional): Page number (default: 1)
- `limit` (optional): Messages per page (default: 50)

**Response:**

```json
{
  "success": true,
  "data": {
    "conversation": {
      "id": 1,
      "uuid": "uuid-string",
      "topic": "Math Class Discussion"
    },
    "messages": [
      {
        "message_id": 456,
        "content": "Please submit your homework",
        "message_type": "text",
        "attachment_url": null,
        "sender": {
          "id": 123,
          "name": "John Teacher",
          "user_type": "staff",
          "photo": "https://sis.bfi.edu.mm/path/to/photo.jpg"
        },
        "created_at": "2024-01-15 10:30:00",
        "is_own_message": false
      }
    ],
    "pagination": {
      "current_page": 1,
      "per_page": 50,
      "total_messages": 25,
      "total_pages": 1,
      "has_more": false
    }
  }
}
```

### 3. Send Message

**POST** `/mobile-api/messaging/send-message`

Sends a message to an existing conversation.

**Parameters:**

- `authCode` (required): Authentication code
- `conversation_uuid` (required): UUID of the conversation
- `message_content` (required): Message text content
- `message_type` (optional): Type of message (default: "text")
- `attachment_url` (optional): URL of attached file

**Response:**

```json
{
  "success": true,
  "message": "Message sent successfully",
  "data": {
    "message_id": 789,
    "conversation_uuid": "uuid-string",
    "content": "Thank you for the assignment",
    "message_type": "text",
    "created_at": "2024-01-15 11:00:00",
    "sender": {
      "id": 456,
      "name": "Jane Student",
      "user_type": "student"
    }
  },
  "notifications": [
    {
      "user_id": 123,
      "notification_sent": true
    }
  ]
}
```

### 4. Create Conversation

**POST** `/mobile-api/messaging/create-conversation`

Creates a new conversation with specified members.

**Parameters:**

- `authCode` (required): Authentication code
- `topic` (required): Conversation topic/title
- `members` (required): Array of user IDs to include in conversation

**Request Body:**

```json
{
  "authCode": "auth-code-here",
  "topic": "Homework Discussion",
  "members": [123, 456, 789]
}
```

**Response:**

```json
{
  "success": true,
  "message": "Conversation created successfully",
  "data": {
    "conversation_id": 10,
    "conversation_uuid": "new-uuid-string",
    "topic": "Homework Discussion",
    "created_by": 456,
    "created_at": "2024-01-15 11:30:00"
  },
  "notifications": [
    {
      "user_id": 123,
      "notification_sent": true
    }
  ]
}
```

### 5. Get Available Users

#### 5.1 For Students (Restricted Access)

**GET** `/mobile-api/messaging/available-users/student`

Retrieves users that students can message (homeroom teacher, subject teachers, head of section, librarian, classmates).

**Parameters:**

- `authCode` (required): Authentication code (must be student)
- `user_type` (optional): Filter by user type ("student", "staff") - **Note: Only affects the flat `users` array for backward compatibility. The `grouped_users` array always contains all available user types.**

**Student Access Includes (RESTRICTED):**

- **Homeroom teacher** - Their assigned homeroom teacher only
- **Subject teachers** - Only teachers who actually teach them (from timetable)
- **Head of section/department** - Department heads and section leaders
- **Librarian** - Library staff
- **Classmates** - Only students in the same classroom

**Student Access EXCLUDES:**

- ❌ Students from other classes/classrooms
- ❌ Teachers who don't teach them
- ❌ Random staff members
- ❌ All other users in the system

**Response:**

```json
{
  "success": true,
  "data": {
    "grouped_users": [
      {
        "type": "homeroom_teacher",
        "type_label": "Homeroom Teacher",
        "count": 1,
        "users": [
          {
            "id": 123,
            "name": "John Teacher",
            "user_type": "staff",
            "role": "homeroom_teacher",
            "email": "<EMAIL>",
            "photo": "https://sis.bfi.edu.mm/path/to/photo.jpg",
            "branch_id": 1
          }
        ]
      },
      {
        "type": "subject_teacher",
        "type_label": "Subject Teachers",
        "count": 3,
        "users": [
          {
            "id": 124,
            "name": "Math Teacher",
            "user_type": "staff",
            "role": "subject_teacher",
            "email": "<EMAIL>",
            "photo": "https://sis.bfi.edu.mm/path/to/photo.jpg",
            "branch_id": 1
          }
        ]
      },
      {
        "type": "classmate",
        "type_label": "Classmates",
        "count": 25,
        "users": [
          {
            "id": 456,
            "name": "Jane Student",
            "user_type": "student",
            "role": "classmate",
            "email": "<EMAIL>",
            "photo": "https://sis.bfi.edu.mm/path/to/photo.jpg",
            "branch_id": 1
          }
        ]
      }
    ],
    "total_count": 29,
    "filtered_count": 29,
    "user_type": "student",
    "access_level": "restricted",
    "applied_filter": null,
    "users": [
      // Flat array for backward compatibility - respects user_type filter
    ]
  }
}
```

**📝 Important Notes:**

- **`grouped_users`**: Always contains ALL available user types/roles, regardless of the `user_type` parameter
- **`users`**: Flat array that respects the `user_type` filter for backward compatibility
- **`total_count`**: Total count of all available users
- **`filtered_count`**: Count after applying the `user_type` filter
- **`applied_filter`**: Shows what filter was applied (null if no filter)
- **Active Students**: For Head of School and Head of Section, only **active students** are shown (students with `user_status=1`, enrolled in current academic year, and assigned to a classroom)

#### 5.2 For Staff (Role-Based Access)

**GET** `/mobile-api/messaging/available-users/staff`

Retrieves users that staff can message based on their specific role and responsibilities.

**Parameters:**

- `authCode` (required): Authentication code (must be staff)
- `user_type` (optional): Filter by user type ("student", "staff", "parent") - **Note: Only affects the flat `users` array for backward compatibility. The `grouped_users` array always contains all available user types.**

**Staff Access Based on Role:**

**🏫 Head of School:**

- ✅ All **active** students in branch (enrolled in current academic year with classroom assignment)
- ✅ All staff members in branch
- ✅ All parents of active students in branch

**👔 Head of Section/Department:**

- ✅ All **active** students in their section/department (enrolled in current academic year with classroom assignment)
- ✅ All staff members in branch
- ✅ Parents of active students in their section

**🏠 Homeroom Teacher:**

- ✅ Students in their homeroom classes only
- ✅ Parents of their homeroom students only
- ❌ Other students or parents

**📚 Subject Teacher:**

- ✅ Students who take their subjects only
- ❌ Other students, parents, or staff

**👥 General Staff:**

- ✅ Other staff members only
- ❌ Students or parents

**Response:**

```json
{
  "success": true,
  "data": {
    "grouped_users": [
      {
        "type": "staff",
        "type_label": "Staff",
        "count": 5,
        "users": [
          {
            "id": 123,
            "name": "John Teacher",
            "user_type": "staff",
            "email": "<EMAIL>",
            "photo": "https://sis.bfi.edu.mm/path/to/photo.jpg",
            "branch_id": 1
          }
        ]
      },
      {
        "type": "student",
        "type_label": "Student",
        "count": 150,
        "users": [
          {
            "id": 456,
            "name": "Jane Student",
            "user_type": "student",
            "email": "<EMAIL>",
            "photo": "https://sis.bfi.edu.mm/path/to/photo.jpg",
            "branch_id": 1
          }
        ]
      }
    ],
    "total_count": 155,
    "filtered_count": 155,
    "user_type": "staff",
    "staff_role": "head_of_school",
    "access_level": "full",
    "applied_filter": null,
    "branches": [
      {
        "branch_id": 1,
        "branch_name": "Main Campus",
        "branch_description": "Primary School Branch",
        "academic_year_id": 2024,
        "access_level": "full",
        "staff_role": "head_of_school",
        "users_count": 155
      }
    ],
    "total_branches": 1,
    "users": [
      {
        "id": 123,
        "name": "John Teacher",
        "user_type": "staff",
        "email": "<EMAIL>",
        "photo": "https://sis.bfi.edu.mm/path/to/photo.jpg",
        "branch_id": 1
      }
    ]
  }
}
```

#### 5.3 Legacy Endpoint (Deprecated)

**GET** `/mobile-api/messaging/available-users`

This endpoint automatically routes to the appropriate method based on user type but is deprecated. Use the specific endpoints above instead.

### 6. Get Conversation Members

**GET** `/mobile-api/messaging/conversation/members`

Retrieves conversation members grouped by user type.

**Parameters:**

- `authCode` (required): Authentication code
- `conversation_uuid` (required): UUID of the conversation

**Response:**

```json
{
  "success": true,
  "data": {
    "conversation": {
      "id": 1,
      "uuid": "uuid-string",
      "topic": "Math Class Discussion"
    },
    "grouped_members": [
      {
        "type": "staff",
        "type_label": "Staff",
        "count": 2,
        "members": [
          {
            "id": 123,
            "name": "John Teacher",
            "user_type": "staff",
            "email": "<EMAIL>",
            "photo": "https://sis.bfi.edu.mm/path/to/photo.jpg",
            "branch_id": 1
          }
        ]
      },
      {
        "type": "student",
        "type_label": "Student",
        "count": 3,
        "members": [
          {
            "id": 456,
            "name": "Jane Student",
            "user_type": "student",
            "email": "<EMAIL>",
            "photo": "https://sis.bfi.edu.mm/path/to/photo.jpg",
            "branch_id": 1
          }
        ]
      }
    ],
    "total_count": 5,
    "members": [
      {
        "id": 123,
        "name": "John Teacher",
        "user_type": "staff",
        "email": "<EMAIL>",
        "photo": "https://sis.bfi.edu.mm/path/to/photo.jpg",
        "branch_id": 1
      }
    ]
  }
}
```

### 7. Delete Conversation

**DELETE** `/mobile-api/messaging/conversation/delete`

Deletes a conversation. Only the conversation creator can delete it.

**Parameters:**

- `authCode` (required): Authentication code
- `conversation_uuid` (required): UUID of the conversation to delete

**Response:**

```json
{
  "success": true,
  "message": "Conversation deleted successfully",
  "data": {
    "conversation_uuid": "uuid-string",
    "deleted_by": "John Teacher",
    "deleted_at": "2024-01-15T10:30:00.000000Z"
  }
}
```

### 8. Leave Conversation

**POST** `/mobile-api/messaging/conversation/leave`

Removes the user from a conversation.

**Parameters:**

- `authCode` (required): Authentication code
- `conversation_uuid` (required): UUID of the conversation to leave

**Response:**

```json
{
  "success": true,
  "message": "Successfully left the conversation",
  "data": {
    "conversation_uuid": "uuid-string",
    "left_by": "Jane Student",
    "left_at": "2024-01-15T10:30:00.000000Z",
    "remaining_members_count": 5
  }
}
```

### 9. Delete Message

**DELETE** `/mobile-api/messaging/message/delete`

Deletes a specific message. Only the sender can delete their own messages within 24 hours.

**Parameters:**

- `authCode` (required): Authentication code
- `message_id` (required): ID of the message to delete

**Response:**

```json
{
  "success": true,
  "message": "Message deleted successfully",
  "data": {
    "message_id": 123,
    "deleted_at": "2024-01-15T10:30:00.000000Z"
  }
}
```

### 10. Search Messages

**GET** `/mobile-api/messaging/search`

Searches conversations and messages for specific content.

**Parameters:**

- `authCode` (required): Authentication code
- `query` (required): Search query string
- `type` (optional): Search type ("all", "conversations", "messages")

**Response:**

```json
{
  "success": true,
  "data": {
    "conversations": [
      {
        "type": "conversation",
        "id": 1,
        "uuid": "uuid-string",
        "title": "Math Class Discussion",
        "created_at": "2024-01-15 09:00:00"
      }
    ],
    "messages": [
      {
        "type": "message",
        "id": 456,
        "content": "Please submit your homework",
        "conversation_uuid": "uuid-string",
        "conversation_title": "Math Class Discussion",
        "sender_name": "John Teacher",
        "created_at": "2024-01-15 10:30:00"
      }
    ]
  },
  "query": "homework",
  "type": "all"
}
```

### 7. Mark Messages as Read

**POST** `/mobile-api/messaging/mark-read`

Marks messages in a conversation as read by the authenticated user.

**Parameters:**

- `authCode` (required): Authentication code
- `conversation_uuid` (required): UUID of the conversation

**Response:**

```json
{
  "success": true,
  "message": "Messages marked as read",
  "conversation_uuid": "uuid-string"
}
```

### 8. Upload Message Attachment

**POST** `/mobile-api/messaging/upload-attachment`

Uploads a file attachment for use in messages.

**Parameters:**

- `authCode` (required): Authentication code
- `attachment` (required): File to upload (multipart/form-data)

**Supported File Types:**

- Images: jpg, jpeg, png, gif
- Documents: pdf, doc, docx, txt
- Maximum size: 10MB

**Response:**

```json
{
  "success": true,
  "message": "File uploaded successfully",
  "data": {
    "file_path": "uploads/messages/2024/01/filename.jpg",
    "file_name": "original_filename.jpg",
    "file_size": 1024000,
    "file_type": "jpg",
    "file_url": "https://sis.bfi.edu.mm/uploads/messages/2024/01/filename.jpg"
  }
}
```

## Role-Based Permissions

### Students

- Can message teachers and other students in the same branch
- Can create conversations with teachers and classmates
- Receive notifications for new messages

### Staff/Teachers

- Can message students and other staff in the same branch
- Can create conversations with any users in their branch
- Receive notifications for new messages

## Error Responses

All endpoints return consistent error responses:

```json
{
  "error": "Error message description"
}
```

Common HTTP status codes:

- `400`: Bad Request (missing required parameters)
- `401`: Unauthorized (invalid authCode)
- `403`: Forbidden (access denied)
- `404`: Not Found (resource not found)
- `500`: Internal Server Error

## Notifications

The messaging system integrates with the existing notification system:

- Web notifications are created for all conversation members
- Mobile push notifications are sent to students
- Notifications include sender name and conversation topic

## Integration Notes

1. **Authentication**: Uses existing mobile device authentication system
2. **Database**: Integrates with existing `msg_chat`, `msg_messages`, and `msg_chat_users` tables
3. **Notifications**: Uses `NotificationRepository` and `MobileNotificationRepository`
4. **File Storage**: Uploads stored in `public/uploads/messages/` directory
5. **Permissions**: Role-based access control using existing user types and branch system
